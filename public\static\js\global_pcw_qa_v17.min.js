;(window.lib = window.lib || {}),
  (lib.kit = lib.kit || {}),
  (lib = lib || {}),
  (lib.kit = lib.kit || {}),
  (lib.kit.util = lib.kit.util || {}),
  (lib.kit.util.jsLoad = {})
var callbackNameValue = '',
  __getLocalDfp =
    ((window.Q = window.Q || {}),
    (window.huQueue = []),
    (window.vtypeSending = !1),
    (Q.__callbacks__ = Q.__callbacks__ || {}),
    (lib.kit.util.jsLoad.jsonp = function(t, e) {
      function i() {}
      function n(t) {
        t.clearAttributes && t.clearAttributes(),
          t && t.parentNode && t.parentNode.removeChild(t)
      }
      ;(callbackNameValue =
        'cb' + Math.floor(2147483648 * Math.random()).toString(36)),
        (e.timeout = e.timeout || 1e4),
        (e.params = e.params || {}),
        (e.params.callback = 'window.Q.__callbacks__.' + callbackNameValue)
      var o,
        r,
        a = Q.__callbacks__,
        s = e.oncomplete || i,
        c = e.onsuccess || i,
        l = e.onfailure || i,
        u = e.onTimeout || l || i
      ;(a[callbackNameValue] = function(t) {
        o && clearTimeout(o),
          s(t),
          (!t || ('A00000' != t.code && '0' != t.code) ? l : c)(t),
          n(r),
          setTimeout(function() {
            delete a[callbackNameValue]
          }, 6e3)
      }),
        (t =
          t +
          (/\?/.test(t) ? '&' : '?') +
          (function(t) {
            var e = []
            if ('object' == typeof t)
              for (var i in t)
                e[e.length] =
                  encodeURIComponent(i) + '=' + encodeURIComponent(t[i])
            return e.join('&').replace(/%20/g, '+')
          })(e.params)),
        ((r = document.createElement('script')).type = 'text/javascript'),
        e.charset && (r.charset = e.charset),
        (r.src = t),
        (r.onerror = function() {
          l({ code: 'E00000' }), n(r)
        }),
        document.getElementsByTagName('head')[0].appendChild(r),
        (o = setTimeout(function() {
          u({ code: 'E00000' }), n(r)
        }, e.timeout))
    }),
    function() {
      var t = ''
      return (t =
        window.localStorage && window.localStorage instanceof Storage
          ? localStorage.getItem('__dfp')
          : t) && qa_isDpfUseful(t)
        ? t.split('@')[0]
        : ''
    }),
  qa_isDpfUseful = function(t) {
    if (t && 0 < t.length) {
      var t = t.split('@'),
        e = t[2],
        t = t[1],
        e = isNaN(e) ? 0 : Number(e),
        t = isNaN(t) ? 0 : Number(t),
        i = new Date().getTime()
      if (i < t && e < i) return !0
    }
    return !1
  },
  isPWA = function() {
    return window.matchMedia('(display-mode: standalone )').matches
  },
  Url = function(t) {
    ;(this.url = t = t || ''),
      (this.query = {}),
      (this.hrParam = {}),
      (this.info = {}),
      this.parse()
  },
  md5V2 =
    ((Url.prototype = {
      parse: function(t) {
        t && (this.url = t),
          this.parseAnchor(),
          this.parseParam(),
          this.parseInfo()
      },
      parseAnchor: function() {
        var t = (t = this.url.match(/\#(.*)/)) ? t[1] : null
        null != (this._anchor = t) &&
          ((this.anchor = this.getNameValuePair(t)),
          (this.url = this.url.replace(/\#.*/, '')))
      },
      parseParam: function() {
        var t = this.url.match(/\?([^\?]*)/)
        null != (t = t ? t[1] : null) &&
          ((this.query = this.getNameValuePair(t)),
          (this.hrParam = this.getNameValuePair(t, '-')),
          (this.url = this.url.replace(/\?([^\?]*)/, '')))
      },
      parseInfo: function() {
        var t,
          e,
          i,
          n = this.url.match(/(\w+):\/\/([^\/:]+):?(\d*)((?:\/|$).*)/)
        n &&
          ((t = n[1]),
          (e = n[2]),
          (i = n[3]),
          (n = n[4]),
          (this.info = { protocol: t, host: e, port: i, path: n }))
      },
      getNameValuePair: function(t, e) {
        var n = {},
          e = e || '&',
          e = new RegExp('([^' + e + '=]*)(?:=([^' + e + ']*))?', 'gim')
        return (
          t.replace(e, function(t, e, i) {
            if ('' == e) return n
            n[e] = i || ''
          }),
          n
        )
      },
      getParam: function(t) {
        return this.query[t] || ''
      },
      serialize: function(t) {
        var e,
          i = []
        for (e in t)
          null == t[e] || '' == t[e] ? i.push(e + '=') : i.push(e + '=' + t[e])
        return i.join('&')
      }
    }),
    (window.lib = window.lib || {}),
    (lib.kit = lib.kit || {}),
    (lib.kit.Url = Url),
    window.$reg &&
      $reg('lib.kit.Url', function() {
        lib.kit.Url = Url
      }),
    (function() {
      function s(t, e) {
        return (t << e) | (t >>> (32 - e))
      }
      function d(t, e) {
        var i = 2147483648 & t,
          n = 2147483648 & e,
          o = 1073741824 & t,
          r = 1073741824 & e,
          t = (1073741823 & t) + (1073741823 & e)
        return o & r
          ? 2147483648 ^ t ^ i ^ n
          : o | r
          ? 1073741824 & t
            ? 3221225472 ^ t ^ i ^ n
            : 1073741824 ^ t ^ i ^ n
          : t ^ i ^ n
      }
      function h(t, e, i, n, o, r, a) {
        return (t = d(t, d(d((e & i) | (~e & n), o), a))), d(s(t, r), e)
      }
      function f(t, e, i, n, o, r, a) {
        return (t = d(t, d(d((e & n) | (i & ~n), o), a))), d(s(t, r), e)
      }
      function p(t, e, i, n, o, r, a) {
        return (t = d(t, d(d(e ^ i ^ n, o), a))), d(s(t, r), e)
      }
      function m(t, e, i, n, o, r, a) {
        return (t = d(t, d(d(i ^ (e | ~n), o), a))), d(s(t, r), e)
      }
      function g(t) {
        for (var e = '', i = '', n = 0; n <= 3; n++)
          e += (i = '0' + ((t >>> (8 * n)) & 255).toString(16)).substr(
            i.length - 2,
            2
          )
        return e
      }
      return function(t) {
        t += ''
        Array()
        for (
          var e,
            i,
            n,
            o,
            r = (function(t) {
              for (
                var e,
                  i = t.length,
                  n = i + 8,
                  n = 16 * (1 + (n - (n % 64)) / 64),
                  o = Array(n - 1),
                  r = 0,
                  a = 0;
                a < i;

              )
                (r = (a % 4) * 8),
                  (o[(e = (a - (a % 4)) / 4)] = o[e] | (t.charCodeAt(a) << r)),
                  a++
              return (
                (o[(e = (a - (a % 4)) / 4)] =
                  o[e] | (128 << (r = (a % 4) * 8))),
                (o[n - 2] = i << 3),
                (o[n - 1] = i >>> 29),
                o
              )
            })(
              (t = (function(t) {
                t = t.replace(/\x0d\x0a/g, '\n')
                for (var e = '', i = 0; i < t.length; i++) {
                  var n = t.charCodeAt(i)
                  n < 128
                    ? (e += String.fromCharCode(n))
                    : (e =
                        127 < n && n < 2048
                          ? (e += String.fromCharCode((n >> 6) | 192)) +
                            String.fromCharCode((63 & n) | 128)
                          : (e =
                              (e += String.fromCharCode((n >> 12) | 224)) +
                              String.fromCharCode(((n >> 6) & 63) | 128)) +
                            String.fromCharCode((63 & n) | 128))
                }
                return e
              })(t))
            ),
            a = 1732584193,
            s = 4023233417,
            c = 2562383102,
            l = 271733878,
            u = 0;
          u < r.length;
          u += 16
        )
          (a = h((e = a), (i = s), (n = c), (o = l), r[u + 0], 7, 3614090360)),
            (l = h(l, a, s, c, r[u + 1], 12, 3905402710)),
            (c = h(c, l, a, s, r[u + 2], 17, 606105819)),
            (s = h(s, c, l, a, r[u + 3], 22, 3250441966)),
            (a = h(a, s, c, l, r[u + 4], 7, 4118548399)),
            (l = h(l, a, s, c, r[u + 5], 12, 1200080426)),
            (c = h(c, l, a, s, r[u + 6], 17, 2821735955)),
            (s = h(s, c, l, a, r[u + 7], 22, 4249261313)),
            (a = h(a, s, c, l, r[u + 8], 7, 1770035416)),
            (l = h(l, a, s, c, r[u + 9], 12, 2336552879)),
            (c = h(c, l, a, s, r[u + 10], 17, 4294925233)),
            (s = h(s, c, l, a, r[u + 11], 22, 2304563134)),
            (a = h(a, s, c, l, r[u + 12], 7, 1804603682)),
            (l = h(l, a, s, c, r[u + 13], 12, 4254626195)),
            (c = h(c, l, a, s, r[u + 14], 17, 2792965006)),
            (s = h(s, c, l, a, r[u + 15], 22, 1236535329)),
            (a = f(a, s, c, l, r[u + 1], 5, 4129170786)),
            (l = f(l, a, s, c, r[u + 6], 9, 3225465664)),
            (c = f(c, l, a, s, r[u + 11], 14, 643717713)),
            (s = f(s, c, l, a, r[u + 0], 20, 3921069994)),
            (a = f(a, s, c, l, r[u + 5], 5, 3593408605)),
            (l = f(l, a, s, c, r[u + 10], 9, 38016083)),
            (c = f(c, l, a, s, r[u + 15], 14, 3634488961)),
            (s = f(s, c, l, a, r[u + 4], 20, 3889429448)),
            (a = f(a, s, c, l, r[u + 9], 5, 568446438)),
            (l = f(l, a, s, c, r[u + 14], 9, 3275163606)),
            (c = f(c, l, a, s, r[u + 3], 14, 4107603335)),
            (s = f(s, c, l, a, r[u + 8], 20, 1163531501)),
            (a = f(a, s, c, l, r[u + 13], 5, 2850285829)),
            (l = f(l, a, s, c, r[u + 2], 9, 4243563512)),
            (c = f(c, l, a, s, r[u + 7], 14, 1735328473)),
            (s = f(s, c, l, a, r[u + 12], 20, 2368359562)),
            (a = p(a, s, c, l, r[u + 5], 4, 4294588738)),
            (l = p(l, a, s, c, r[u + 8], 11, 2272392833)),
            (c = p(c, l, a, s, r[u + 11], 16, 1839030562)),
            (s = p(s, c, l, a, r[u + 14], 23, 4259657740)),
            (a = p(a, s, c, l, r[u + 1], 4, 2763975236)),
            (l = p(l, a, s, c, r[u + 4], 11, 1272893353)),
            (c = p(c, l, a, s, r[u + 7], 16, 4139469664)),
            (s = p(s, c, l, a, r[u + 10], 23, 3200236656)),
            (a = p(a, s, c, l, r[u + 13], 4, 681279174)),
            (l = p(l, a, s, c, r[u + 0], 11, 3936430074)),
            (c = p(c, l, a, s, r[u + 3], 16, 3572445317)),
            (s = p(s, c, l, a, r[u + 6], 23, 76029189)),
            (a = p(a, s, c, l, r[u + 9], 4, 3654602809)),
            (l = p(l, a, s, c, r[u + 12], 11, 3873151461)),
            (c = p(c, l, a, s, r[u + 15], 16, 530742520)),
            (s = p(s, c, l, a, r[u + 2], 23, 3299628645)),
            (a = m(a, s, c, l, r[u + 0], 6, 4096336452)),
            (l = m(l, a, s, c, r[u + 7], 10, 1126891415)),
            (c = m(c, l, a, s, r[u + 14], 15, 2878612391)),
            (s = m(s, c, l, a, r[u + 5], 21, 4237533241)),
            (a = m(a, s, c, l, r[u + 12], 6, 1700485571)),
            (l = m(l, a, s, c, r[u + 3], 10, 2399980690)),
            (c = m(c, l, a, s, r[u + 10], 15, 4293915773)),
            (s = m(s, c, l, a, r[u + 1], 21, 2240044497)),
            (a = m(a, s, c, l, r[u + 8], 6, 1873313359)),
            (l = m(l, a, s, c, r[u + 15], 10, 4264355552)),
            (c = m(c, l, a, s, r[u + 6], 15, 2734768916)),
            (s = m(s, c, l, a, r[u + 13], 21, 1309151649)),
            (a = m(a, s, c, l, r[u + 4], 6, 4149444226)),
            (l = m(l, a, s, c, r[u + 11], 10, 3174756917)),
            (c = m(c, l, a, s, r[u + 2], 15, 718787259)),
            (s = m(s, c, l, a, r[u + 9], 21, 3951481745)),
            (a = d(a, e)),
            (s = d(s, i)),
            (c = d(c, n)),
            (l = d(l, o))
        return (g(a) + g(s) + g(c) + g(l)).toLowerCase()
      }
    })()),
  getDomain
;(window.lib = window.lib || {}),
  (lib.md5V2 = md5V2),
  (Object.extend = function(t, e) {
    for (var i in e) t[i] = e[i]
    return t
  }),
  (window.lib = window.lib || {}),
  lib.SITE_DOMAIN ||
    ((getDomain = function() {
      var t = window.location.hostname.split('.')
      return (t = t.slice(t.length - 2)).join('.')
    }),
    (lib.SITE_DOMAIN = getDomain())),
  (lib.PROJECT_VERSION = '201104221818300317'),
  (lib.action = lib.action || {}),
  (lib.action.Qa = function() {
    ;(this.init = function(t) {
      var e = this,
        i = lib.SITE_DOMAIN.match(/pps/)
      try {
        var n = navigator.userAgent.toLowerCase()
        ;(this.par = {}),
          (this.pars = []),
          (this.custom = {}),
          (this.filter = []),
          (this.time = 0),
          (this.w = window),
          (this.l = window.location),
          (this.d = window.document),
          (this.searchJson = this.queryToJson(this.l.href)),
          (this.urlMap = {
            rdm: 'rdm',
            qtcurl: 'qtcurl',
            rfr: 'rfr',
            lrfr: 'lrfr',
            jsuid: 'jsuid',
            qtsid: 'qtsid',
            ppuid: 'ppuid',
            platform: 'platform',
            weid: 'weid',
            pru: 'pru',
            flshuid: 'flshuid',
            fcode: 'fcode',
            ffcode: 'ffcode',
            coop: 'coop',
            odfrm: 'odfrm',
            fvcode: 'fvcode',
            nu: 'nu',
            mod: 'mod',
            pcau: 'pcau'
          }),
          (this.cookieMap = {
            flshuid: 'QC005',
            jsuid: 'QC006',
            pru: 'P00PRU',
            lrfr: 'QC007',
            qtsid: 'QC008',
            QY_FC: 'QC009',
            QY_FFC: 'QC014',
            gaflag: 'QC011',
            odfrm: 'QC132',
            QY_FV: 'QC142',
            EDM_FC: 'QCedm001',
            EDM_FV: 'QCedm002',
            pcau: 'PCAU'
          }),
          (t = t || {}),
          (this.times = t.times || 5),
          (this.timeouts = t.timeouts || 1e3),
          (this.url = t.url || '//msg-intl.qy.net/jspb.gif'),
          (this.newUrl = t.newUrl || '//msg-intl.qy.net/act')
        'global' === (window.intlPageInfo || {}).i18n &&
          ((this.url = t.url || '//msg-intl.qy.net/jspb.gif'),
          (this.newUrl = t.newUrl || '//msg-intl.qy.net/act')),
          -1 == this.url.indexOf('?')
            ? (this.url += '?')
            : '&' != this.url.slice(-1) && (this.url += '&'),
          -1 == this.newUrl.indexOf('?')
            ? (this.newUrl += '?')
            : '&' != this.newUrl.slice(-1) && (this.newUrl += '&'),
          (this.flag = t.flag || 'QC010'),
          (this.callback = t.callback || function() {}),
          'object' == typeof t.urlMap && Object.extend(this.urlMap, t.urlMap),
          'object' == typeof t.cookieMap &&
            Object.extend(this.cookieMap, t.cookieMap),
          'object' == typeof t.custom && Object.extend(this.custom, t.custom),
          t.filter instanceof Array && (this.filter = t.filter)
        var o,
          r,
          a,
          s,
          c,
          l = this.urlMap,
          u =
            ((this.par[l.rdm] = this.rand()),
            (this.par[l.qtcurl] = this.u(this.l.href)),
            (this.par[l.rfr] = this.u(this.d.referrer)),
            (this.par[l.lrfr] = this.getLrfr()),
            (this.par[l.jsuid] = this.getJsuid()),
            (this.par[l.qtsid] = this.getQtsid()),
            (this.par[l.ppuid] = this.getUserInfoUid()),
            (this.par[l.nu] = this.getNu()),
            (this.par[l.platform] = /ipad/i.test(n)
              ? '21'
              : /(iphone os)|(android)/i.test(n)
              ? '31'
              : '11'),
            i && (this.par[l.platform] = '20' + this.par[l.platform]),
            this.w.pingbackParams &&
              (Object.assign
                ? (this.par = Object.assign(
                    {},
                    this.w.pingbackParams,
                    this.par
                  ))
                : ((o = Object.extend({}, this.w.pingbackParams)),
                  (this.par = Object.extend(o, this.par)))),
            this.w.Q &&
              Q.PageInfo &&
              Q.PageInfo.playPageInfo &&
              Q.PageInfo.playPageInfo.videoTemplate &&
              (this.par.tmplt = Q.PageInfo.playPageInfo.videoTemplate || ''),
            (this.par[l.fcode] = this.getFc()),
            (this.par[l.ffcode] = this.getFfc()),
            (this.par[l.coop] = this.getCoop()),
            (this.par[l.weid] = this.getWeid()),
            (this.par[l.pru] = this.getPRU()),
            (this.par[l.fvcode] = this.getFv()),
            (this.par[l.mod] = this.getMod()),
            Object.extend(this.par, this.custom),
            this.searchJson),
          d = this.cookieMap[this.urlMap.odfrm],
          h = u[this.urlMap.odfrm] || this.cookieGet(d) || '',
          f =
            (h &&
              ((this.par[l.odfrm] = h),
              this.cookieSet(d, h, 0, '/', lib.SITE_DOMAIN),
              (r = this.d.getElementsByTagName('body')[0]),
              ((a = this.queryToJson(r.getAttribute('data-pb') || '') || {})[
                l.odfrm
              ] = h),
              (s = this.jsonToQuery(a)),
              r.setAttribute('data-pb', s)),
            document.getElementById('block-B')),
          p =
            (f &&
              f.getAttribute('data-pb') &&
              (c = f.getAttribute('data-pb').match(/(^|&)?tmplt=([^&]+)/i)) &&
              c[2] &&
              (e.par.tmplt = c[2]),
            /ipad/i.test(n) || /iphone os/i.test(n) || /lepad_hls/i.test(n))
        ;(e.par[l.flshuid] = p ? e.getJsuid() : e.getFlashId()),
          (e.par[l.pcau] = e.getPcau()),
          p ? e.par[l.flshuid] : e.getJsuid()
        this.getFingerPrint(function(t) {
          t && (e.par.dfp = t), e.setQC005(), e.post()
        })
      } catch (t) {}
      this.sendNewQa()
      var m,
        t = this.cookieGet('intl_t20_pb')
      t &&
        (((i = new Image()).src = t),
        (m = this.domainName()),
        i.complete
          ? e.cookieRemove('intl_t20_pb', '/', m)
          : ((i.onload = function() {
              e.cookieRemove('intl_t20_pb', '/', m)
            }),
            (i.onerror = function() {
              e.cookieRemove('intl_t20_pb', '/', m)
            }))),
        this.HandleEDMFcFV()
    }),
      (this.sendNewQa = function() {
        var i = this
        this.getHu(function(e) {
          i.getFingerPrint(function(t) {
            i.initNewPar(e, t), i.setQC005(), i.post(!0)
          })
        })
      }),
      (this.getPtid = function() {
        var t = navigator.userAgent.toLowerCase(),
          e = navigator.platform.toLowerCase(),
          i = '01',
          t =
            (/(iphone)|(android)/i.test(t)
              ? (i = '02')
              : /pad/i.test(t) && (i = '03'),
            '01')
        return (
          /android/i.test(e) && (t = '02'),
          /mac/i.test(e) && (t = '08'),
          i + (t = /ios/i.test(e) ? '03' : t) + '0021010000000000'
        )
      }),
      (this.extend = function(t, e) {
        for (var i, n = t || {}, o = 1, r = arguments.length; o < r; o++)
          if ((i = arguments[o]))
            for (var a in i) i.hasOwnProperty(a) && (n[a] = i[a])
        return n
      }),
      (this.initNewPar = function(t, e) {
        var i = '1_10_101',
          n = window.intlPageInfo || {},
          o =
            ('global' === n.i18n &&
              (-1 < (o = navigator.userAgent.toLowerCase()).indexOf('electron/')
                ? ((i = '1_11_223'), -1 < o.indexOf('mac') && (i = '1_11_222'))
                : ((i = '1_10_222'),
                  /(android)|(like mac os x)/i.test(o) && (i = '2_20_223'))),
            window.__global__info && window.__global__info.userInfo),
          o = o && o.hu,
          o = this.cookieGet('hu') || o,
          e =
            ((this.par = {
              u: this.cookieGet('u') || this.getFlashId(),
              pu: this.cookieGet('pu') || this.getUserInfoUid(),
              rn: this.rand(),
              p1: this.cookieGet('p1') || i,
              mkey: this.cookieGet('mkey') || '',
              de: this.cookieGet('de') || this.getQtsid(),
              stime: new Date().getTime(),
              ce: this.getWeid(),
              bstp: 0,
              t: 22,
              v: 1,
              rpage: '',
              hu: o || t,
              dfp: e,
              mod: this.getMod(),
              purl: this.u(this.l.href),
              nu: this.getNu(),
              vfm: this.searchJson.vfm || '',
              rfr: this.u(this.d.referrer),
              pcau: this.getPcau(),
              ptp: '',
              pagev: 'homepage_adv_v1',
              coop: this.getCoop(),
              lrfr: this.getLrfr(),
              ptid: this.getPtid()
            }),
            'global' === n.i18n &&
              (delete this.par.ptid,
              (this.par.v = this.cookieGet('v') || ''),
              isPWA() && (this.par.stuptype = 'shortcut'),
              (this.par.timezone = new Date().toUTCString()),
              (i = new lib.kit.Url(document.location.href)),
              (o = n.pbInfos || {}),
              (this.par.rpage = o.rpage || ''),
              (this.par.lang =
                i.getParam('lang') ||
                o.lang ||
                this.cookieGet('lang') ||
                'en_us'),
              (this.par.re = window.screen.width + '*' + window.screen.height),
              (this.par.s2 = i.getParam('frmrp') || ''),
              (this.par.s3 = i.getParam('frmb') || ''),
              o.pbShowParams &&
                (Object.assign
                  ? (this.par = Object.assign({}, this.par, o.pbShowParams))
                  : (this.par = this.extend({}, this.par, o.pbShowParams))),
              (t = this.getUtmPbParams()),
              (this.par.vfm = encodeURIComponent(t))),
            this.cookieGet('b_ext_ip') || '')
        e && (this.par.b_ext = JSON.stringify({ bip: e })),
          (n =
            this.w.Q && this.w.Q.PageInfo && this.w.Q.PageInfo.playPageInfo) &&
            (n.videoTemplate && (this.par.tmplt = n.videoTemplate || ''),
            void 0 !== n.cid && (this.par.c1 = n.cid),
            n.albumId) &&
            (this.par.aid = n.albumId),
          this.w.pingbackParams &&
            (Object.assign
              ? (this.par = Object.assign({}, this.par, this.w.pingbackParams))
              : ((i = Object.extend({}, this.par)),
                (this.par = Object.extend(i, this.w.pingbackParams))))
      })
    var noDfpTag = 1
    ;(this.getFingerPrint = function(e) {
      noDfpTag = 0
      var t = __getLocalDfp()
      t
        ? e(t)
        : window.dfp
        ? window.dfp.getFingerPrint(
            function(t) {
              e(t)
            },
            function(t) {
              e()
            }
          )
        : setTimeout(function() {
            noDfpTag && e()
          }, 1e4)
    }),
      (this.getHu = function(t) {
        var e = window.__global__info && window.__global__info.userInfo
        e && e.hu ? t(e.hu) : this.getUser(t)
      }),
      (this.getUser = function(t) {
        var e, i, n, o
        this.cookieGet('I00019') || ''
          ? (window.huQueue.push(t),
            window.vtypeSending ||
              ((window.vtypeSending = !0),
              (i = navigator.userAgent.toLowerCase()),
              (e = 3),
              (/(android)|(like mac os x)/i.test(i) ||
                (/(intel mac os x)/i.test(i) && 'ontouchend' in document)) &&
                (e = 4),
              (i = this.cookieGet('QC005') || ''),
              (n = this.cookieGet('mod') || 'intl'),
              (o = this.cookieGet('lang') || 'en_us'),
              lib.kit.util.jsLoad.jsonp(
                '//pcw-api.iq.com/api/vtype?batch=1&deviceId=' +
                  i +
                  '&modeCode=' +
                  n +
                  '&langCode=' +
                  o +
                  '&platformId=' +
                  e +
                  '&vipInfoVersion=5.0',
                {
                  onsuccess: function(t) {
                    var e = -1,
                      i = []
                    if (
                      ((window.__global__info = window.__global__info || {}),
                      t.data && t.data.all_vip && t.data.all_vip.length)
                    ) {
                      var n = t.data.all_vip
                      window.__allhu__ = n
                      for (var o = 0; o < n.length; o++) {
                        var r = n[o]
                        '1' == r.status && i.push(r.vipType)
                      }
                      ;(window.__global__info.userInfo = n[0]),
                        (e = i.length ? i.join(',') : -1)
                    } else
                      window.__global__info.userInfo =
                        window.__global__info.userInfo || {}
                    ;(window.__global__info.userInfo.hu = e),
                      (window.__hu__ = e),
                      (window.vtypeSending = !1)
                    for (o = 0; o < window.huQueue.length; o++)
                      window.huQueue[o] && window.huQueue[o](e)
                  },
                  onfailure: function() {
                    window.vtypeSending = !1
                    for (var t = 0; t < window.huQueue.length; t++)
                      window.huQueue[t] && window.huQueue[t](-1)
                  }
                }
              )))
          : t(-1)
      }),
      (this.getUserInfoUid = function() {
        try {
          var userInfoStorage =
              window.__global__info && window.__global__info.userInfo,
            uid = userInfoStorage && userInfoStorage.uid,
            userInfo,
            userInfo
          return (
            uid ||
            ((userInfo = this.cookieGet('I00002')),
            (userInfo =
              userInfo &&
              (userInfo == window.JSON
                ? window.JSON.parse(userInfo)
                : eval('(' + userInfo + ')'))),
            userInfo &&
              userInfo.data &&
              ((window.__global__info = window.__global__info || {}),
              (window.__global__info.userInfo =
                window.__global__info.userInfo || {}),
              (window.__global__info.userInfo.uid = userInfo.data.uid || ''),
              userInfo.data.uid)) ||
            ''
          )
        } catch (e) {
          return ''
        }
      }),
      (this.u = function(t) {
        try {
          var e = encodeURIComponent
          return (e instanceof Function ? e : escape)(t)
        } catch (t) {
          return ''
        }
      }),
      (this.hash = function(t) {
        try {
          var e,
            i = 1
          if (t)
            for (var i = 0, n = t.length - 1; 0 <= n; n--)
              i =
                0 !=
                (e =
                  266338304 &
                  (i =
                    ((i << 6) & 268435455) + (e = t.charCodeAt(n)) + (e << 14)))
                  ? i ^ (e >> 21)
                  : i
          return i
        } catch (t) {
          return ''
        }
      }),
      (this.rand = function(t) {
        try {
          var e = []
          if (isNaN(t)) e.push(Math.round(2147483647 * Math.random()))
          else
            for (var i = 0; i < t; i++)
              e.push(Math.round(2147483647 * Math.random()).toString(36))
          return e.join('')
        } catch (t) {
          return ''
        }
      }),
      (this.cookieGet = function(t) {
        try {
          return (
            ('string' ==
              typeof (t = (function(t) {
                if (
                  new RegExp(
                    '^[^\\x00-\\x20\\x7f\\(\\)<>@,;:\\\\\\"\\[\\]\\?=\\{\\}\\/\\u0080-\\uffff]+$'
                  ).test(t)
                ) {
                  t = new RegExp('(^| )' + t + '=([^;]*)(;|$)').exec(
                    document.cookie
                  )
                  if (t) return t[2] || ''
                }
                return ''
              })(t)) &&
              !(1 < t.length && 'deleted' == t) &&
              decodeURIComponent(t)) ||
            ''
          )
        } catch (t) {
          return ''
        }
      }),
      (this.cookieSet = function(t, e, i, n, o, r) {
        try {
          var a, s, c
          ;(window.funcCookieDisable &&
            ('QC173' === t ||
              'QC010' === t ||
              'QC008' === t ||
              'QC007' === t ||
              'QC006' === t)) ||
            ((a = []).push(t + '=' + encodeURIComponent(e)),
            i &&
              ((c = (s = new Date()).getTime() + 36e5 * i),
              s.setTime(c),
              a.push('expires=' + s.toGMTString())),
            n && a.push('path=' + n),
            o && a.push('domain=' + o),
            r && a.push(r),
            (document.cookie = a.join(';')))
        } catch (t) {
          return ''
        }
      }),
      (this.domainName = function() {
        return window.location.hostname
          .split(':')[0]
          .split('.')
          .splice(1)
          .join('.')
      }),
      (this.cookieRemove = function(t, e, i, n) {
        try {
          document.cookie =
            t +
            '=;expires=Fri, 31 Dec 1999 23:59:59 GMT;path=' +
            (e || '/') +
            ';domain=' +
            i
        } catch (t) {
          return ''
        }
      }),
      (this.getJsuid = function() {
        try {
          var t = this.cookieMap.jsuid,
            e = this.cookieGet(t)
          return (
            (e && isNaN(e)) || (e = this.rand(4)),
            this.cookieSet(t, e, 8760, '/', lib.SITE_DOMAIN),
            e
          )
        } catch (t) {
          return ''
        }
      }),
      (this.getQtsid = function() {
        try {
          function t() {
            return parseInt(new Date() / 1e3, 10).toString()
          }
          var e,
            i,
            n = this.cookieMap.qtsid
          return (
            (i = this.cookieGet(n)),
            this.cookieGet(this.flag) ||
              (/^\d{10}\.\d{10}\.\d{10}\.\d+$/.test(i)
                ? (((i = i.split('.'))[1] = i[2]),
                  (i[2] = t()),
                  (i[3] = parseInt(i[3], 10) + 1))
                : (i = [(e = t()), e, e, '1']),
              this.cookieSet(n, i.join('.'), 8760, '/', lib.SITE_DOMAIN)),
            i
          )
        } catch (t) {
          return ''
        }
      }),
      (this.getLrfr = function() {
        try {
          var t,
            e,
            i = this.cookieMap.lrfr,
            n = (n = this.d.referrer.match(/http[s]?:\/\/([^\/]*)/))
              ? n[1]
              : '',
            o = this.cookieGet(i),
            r = ((o = 'undefined' == o ? '' : o), this.l.hostname),
            a = n && n.match(/iq\.com/),
            s = o
          return (
            o
              ? this.d.referrer
                ? n === r ||
                  -1 !== n.indexOf(lib.SITE_DOMAIN) ||
                  a ||
                  (s = this.u(this.d.referrer))
                : (s = 'DIRECT')
              : (s =
                  !this.d.referrer || a ? 'DIRECT' : this.u(this.d.referrer)),
            'global' === (window.intlPageInfo || {}).i18n &&
              'embed_page' ===
                (t = new lib.kit.Url(document.location.href)).getParam(
                  'frmrp'
                ) &&
              (s =
                (e = (e = t.getParam('lrfr')) ? 'https://' + e : '') ||
                this.u(this.d.referrer) ||
                s),
            this.cookieSet(i, s, 0, '/', lib.SITE_DOMAIN),
            s
          )
        } catch (t) {
          return ''
        }
      }),
      (this.getFlashId = function() {
        var t = this.cookieMap.flshuid
        return this.cookieGet(t) || ''
      }),
      (this.getPcau = function() {
        var t = this.cookieMap.pcau
        return this.cookieGet(t) || '0'
      }),
      (this.setQC005 = function() {
        var t = this.cookieGet('QC005'),
          e = window.localStorage && localStorage.getItem('QC005')
        t && !e && window.localStorage && localStorage.setItem('QC005', t),
          !t && e && this.cookieSet('QC005', e)
      }),
      (this.getFc = function() {
        try {
          var t = this.l.search.match(/[\?&]fc=([^&]*)(&|$)/i),
            e = this.cookieMap.QY_FC,
            i = this.cookieGet(e)
          return 'b22dab601821a896' == i
            ? i
            : (t
                ? ((t = t[1]), this.cookieSet(e, t, 0, '/', lib.SITE_DOMAIN))
                : ((t = this.cookieGet(e)) && 'undefined' != t) || (t = ''),
              t)
        } catch (t) {
          return ''
        }
      }),
      (this.getFv = function() {
        try {
          var t,
            e = this.l.search.match(/[\?&]fv=([^&]*)(&|$)/i),
            i = this.cookieMap.QY_FV
          return (
            e
              ? (146 < (t = encodeURIComponent(e[1])).length &&
                  (t = t.substring(0, 146)),
                (e = t = decodeURIComponent(t)),
                this.cookieSet(i, e, 72, '/', lib.SITE_DOMAIN))
              : ((e = this.cookieGet(i)) && 'undefined' != e) || (e = ''),
            e
          )
        } catch (t) {
          return ''
        }
      }),
      (this.getFfc = function() {
        try {
          var t = this.l.search.match(/[\?&]ffc=([^&]*)(&|$)/i),
            e = this.cookieMap.QY_FFC
          return (
            t
              ? ((t = t[1]), this.cookieSet(e, t, 0, '/', lib.SITE_DOMAIN))
              : ((t = this.cookieGet(e)) && 'undefined' != t) || (t = ''),
            t
          )
        } catch (t) {
          return ''
        }
      }),
      (this.HandleEDMFcFV = function() {
        try {
          var t,
            e,
            i,
            n,
            o,
            r = this.l.search.match(/[\?&]utm_source=([^&]*)(&|$)/i)
          r &&
            r[1] &&
            'Free_PT_EDM_EDM' === r[1] &&
            ((t = this.l.search.match(/[\?&]fv=([^&]*)(&|$)/i)),
            (e = this.l.search.match(/[\?&]fc=([^&]*)(&|$)/i)),
            (i = this.cookieMap.EDM_FV),
            (n = this.cookieMap.EDM_FC),
            t &&
              (146 < (o = encodeURIComponent(t[1])).length &&
                (o = o.substring(0, 146)),
              (t = o = decodeURIComponent(o)),
              this.cookieSet(i, t, 72, '/', lib.SITE_DOMAIN)),
            e) &&
            ((e = e[1]), this.cookieSet(n, e, 72, '/', lib.SITE_DOMAIN))
        } catch (t) {
          return ''
        }
      }),
      (this.getCoop = function() {
        var t,
          e = ''
        return (
          'mini' == this.l.host.split('.')[0]
            ? (t = ((t = lib.$url(this.l.href, 'app')) && t.app) || '') &&
              (e = 'coop_' + t.replace('bdbrowser', 'bdexplorer'))
            : this.w.INFO &&
              this.w.INFO.flashVars &&
              this.w.INFO.flashVars.coop &&
              (e = this.w.INFO.flashVars.coop),
          e
        )
      }),
      (this.getWeid = function() {
        return (
          window.webEventID ||
          lib.md5V2(
            +new Date() + Math.round(2147483647 * Math.random()) + ''
          ) ||
          ''
        )
      }),
      (this.getNu = function() {
        return this.cookieGet('QC173') || 0
      }),
      (this.getPRU = function() {
        return this.cookieGet('P00PRU') || ''
      }),
      (this.getMod = function() {
        var t = (window.Q && Q.PageInfo) || {},
          e =
            ((t =
              'tw_t' !==
              (t = window.uniqy
                ? (window.uniqy && window.uniqy.PageInfo) || {}
                : t).i18n
                ? 'cn_s'
                : 'tw_t'),
            window.intlPageInfo || {})
        return (
          'global' === e.i18n &&
            ((e = e.pbInfos || {}),
            (t =
              new lib.kit.Url(document.location.href).getParam('mod') ||
              e.mod ||
              this.cookieGet('mod') ||
              'intl')),
          t
        )
      }),
      (this.post = function(t) {
        var e = this
        try {
          e.pars = []
          var i,
            n,
            o = e.filter.length
          if (0 === o) for (i in e.par) e.pars.push([i, e.par[i]].join('='))
          else
            for (i = 0; i < o; i++)
              (n = e.filter[i]), e.pars.push([n, e.par[n]].join('='))
          ;(e.pars = e.pars.join('&')),
            (window.jsQa = new Image(1, 1)),
            (window.jsQa.src = (t ? e.newUrl : e.url) + e.pars),
            e.cookieSet(e.flag, e.hash(e.pars), 0, '/', lib.SITE_DOMAIN),
            e.cookieSet(e.urlMap.nu, 0, 0, '/', lib.SITE_DOMAIN),
            e.callback()
        } catch (t) {
          return ''
        }
      }),
      (this.iframeRequest = function(t) {
        var e = document.createElement('iframe')
        ;(e.scrolling = 'no'),
          (e.style.display = 'none'),
          (e.frameborder = 0),
          (e.src = t),
          document.body.appendChild(e)
      }),
      (this.syncCookie = function(t, i, n) {
        var o,
          r = this
        ;-1 !== t.indexOf('iqiyi.com')
          ? (o = '//passport.pps.tv/pages/user/proxy.action')
          : -1 !== t.indexOf('pps.tv') &&
            (o = '//passport.iqiyi.com/pages/user/proxy.action'),
          o &&
            setTimeout(function() {
              var e = o + '#' + i + '=' + n
              try {
                window.JSHandler.logToConsole('xxx')
              } catch (t) {
                window.external.GetLoginJsonInfo || r.iframeRequest(e)
              }
            }, 0)
      }),
      (this.queryToJson = function(t) {
        for (
          var e,
            i,
            n,
            o =
              Array.isArray ||
              function(t) {
                return '[object Array]' == Object.prototype.toString.call(t)
              },
            r = (t = t || this.l.href)
              .substr(t.lastIndexOf('?') + 1)
              .split('&'),
            a = r.length,
            s = {},
            c = 0;
          c < a;
          c++
        )
          r[c] &&
            ((e = (n = r[c].split('=')).shift()),
            (n = n.join('=')),
            void 0 === (i = s[e])
              ? (s[e] = n)
              : o(i)
              ? i.push(n)
              : (s[e] = [i, n]))
        return s
      }),
      (this.jsonToQuery = function(t, e) {
        var i,
          n,
          o,
          r =
            Array.isArray ||
            function(t) {
              return '[object Array]' == Object.prototype.toString.call(t)
            },
          a = [],
          s =
            e ||
            function(t) {
              return String(t).replace(/[#%&+=\/\\\ \u3000\f\r\n\t]/g, function(
                t
              ) {
                return (
                  '%' +
                  (256 + t.charCodeAt())
                    .toString(16)
                    .substring(1)
                    .toUpperCase()
                )
              })
            },
          c = t,
          l = function(t, e) {
            if (r(t)) for (i = t.length; i--; ) a.push(e + '=' + s(t[i], e))
            else a.push(e + '=' + s(t, e))
          }
        if ('function' == typeof l)
          for (n in c)
            if (c.hasOwnProperty(n) && ((o = c[n]), !1 === l.call(c, o, n)))
              break
        return a.join('&')
      }),
      (this.getUtmPbParams = function() {
        var t = new lib.kit.Url(document.location.href),
          e = t.getParam('utm_source'),
          i = t.getParam('utm_medium'),
          n = t.getParam('utm_campaign'),
          o = t.getParam('utm_content'),
          r = t.getParam('utm_term'),
          a = t.getParam('version'),
          t = t.getParam('is_retargeting'),
          e = e ? `utm_source=${e}&` : '',
          i =
            ((e = (e =
              (e =
                (e =
                  e +
                  (i ? `utm_medium=${i}&` : '') +
                  (n ? `utm_campaign=${n}&` : '')) +
                (o ? `utm_content=${o}&` : '') +
                (r ? `utm_term=${r}&` : '')) +
              (a ? `version=${a}&` : '') +
              (t ? `is_retargeting=${t}&` : ''))
              ? e.substr(0, e.length - 1)
              : ''),
            this.cookieGet('intlPbVfm'))
        return (
          e && !window.disSetIntlPbVfmTag
            ? (this.cookieSet('intlPbVfm', e, 72, '/', lib.SITE_DOMAIN),
              (window.disSetIntlPbVfmTag = 1))
            : (e = e || i || ''),
          e
        )
      })
  }),
  !(function() {
    var a = new lib.action.Qa(),
      s = !1,
      c = null,
      t = '//msg-intl.qy.net/jpb.gif',
      e = '//msg-intl.qy.net/act'
    function i(t) {
      function e() {
        for (; 0 < r.length; )
          try {
            r.shift()()
          } catch (t) {}
      }
      var i = document.getElementsByTagName('HEAD').item(0),
        n = document.createElement('script'),
        o =
          ((n.type = 'text/javascript'),
          (n.src = '//security.iq.com/static/intl/cook/v1/cooksdk.js'),
          navigator.userAgent.toLowerCase()),
        r =
          (/ipad/i.test(o) || /iphone os/i.test(o) || /lepad_hls/i.test(o),
          i.appendChild(n),
          [])
      window.qaLoadingDfp = function(t) {
        r.push(t)
      }
      clearTimeout(c),
        (c = setTimeout(function() {
          s ||
            (clearTimeout(c), (s = !0), t(), (window.mainQaInstance = a), e())
        }, 3e3)),
        /msie/.test(o)
          ? (n.onreadystatechange = function() {
              ;/loaded|complete/.test(n.readyState) &&
                ((s = !0),
                clearTimeout(c),
                t(),
                (window.mainQaInstance = a),
                e())
            })
          : (n.onload = function() {
              ;(s = !0), clearTimeout(c), t(), (window.mainQaInstance = a), e()
            })
    }
    'global' === (window.intlPageInfo || {}).i18n &&
      ((t = '//msg-intl.qy.net/jpb.gif'), (e = '//msg-intl.qy.net/act'))
    try {
      __getLocalDfp()
        ? (a.init({ url: t, newUrl: e }),
          i(function() {}),
          (window.mainQaInstance = a))
        : i(function() {
            a.init({ url: t, newUrl: e })
          })
    } catch (t) {}
  })(),
  !(function() {
    var E = navigator.userAgent.toLowerCase(),
      t = '1',
      e = '10',
      i = '101',
      M =
        (/(android)|(like mac os x)/i.test(E) && ((t = '2'), (e = '20')),
        /(android)/i.test(E)
          ? (i = '201')
          : /(like mac os x)/i.test(E) &&
            (i = /(iphone)/i.test(E) ? '201' : '202'),
        '//msg-intl.qy.net/b?t=20&p=' +
          e +
          '&p1=' +
          (i = mainQaInstance ? mainQaInstance.cookieGet('p1') || i : i) +
          '&pf=' +
          t)
    window.uniqy &&
      (M = '//msg-intl.qy.net/b?t=20&p=' + e + '&p1=' + i + '&pf=' + t)
    'global' === (window.intlPageInfo || {}).i18n &&
      (-1 < (E = navigator.userAgent.toLowerCase()).indexOf('electron/')
        ? ((i = '1_11_223'), -1 < E.indexOf('mac') && (i = '1_11_222'))
        : ((i = '1_10_222'),
          /(android)|(like mac os x)/i.test(E) && (i = '2_20_223')),
      mainQaInstance && (i = mainQaInstance.cookieGet('p1') || i),
      (M = '//msg-intl.qy.net/act?t=20&p1=' + i))
    var T = function(t, e) {
      if ('body' !== t?.block) {
        ;(t = t || {}), -1 == e.indexOf('?') ? (e += '?') : (e += '&')
        var i,
          n = +new Date()
        if (((t._ = n), mainQaInstance)) {
          var o = mainQaInstance.cookieGet('abtest')
          if (o) {
            o = JSON.parse(o)
            let e = ''
            Object.keys(o).forEach(t => {
              e += o[t] + '-'
            }),
              (e = e.slice(0, e.length - 1)) && 0 < e.length && (t.abtest = e)
          }
        }
        for (i in t)
          t.hasOwnProperty(i) &&
            (e += encodeURIComponent(i) + '=' + encodeURIComponent(t[i]) + '&')
        '&' === e[e.length - 1] && (e = e.slice(0, -1))
        var r = new lib.action.Qa(),
          a = r.domainName(),
          n = (t.rlink && r.cookieSet('intl_t20_pb', e, 0, '/', a), new Image())
        ;(n.src = e),
          n.complete
            ? r.cookieRemove('intl_t20_pb', '/', a)
            : ((n.onload = function() {
                r.cookieRemove('intl_t20_pb', '/', a)
              }),
              (n.onerror = function() {
                r.cookieRemove('intl_t20_pb', '/', a)
              }))
      }
    }
    function r(t) {
      for (
        var e,
          i,
          n = [],
          o = (t = 'string' == typeof t ? t.split(',') : t).length,
          r = (String.fromCharCode, 0);
        r < o;
        r++
      ) {
        var a,
          s = 'block-' + (a = t[r].replace(/\s+/g, '')),
          c = '',
          l = (c = document.querySelectorAll
            ? document.querySelectorAll('*[id=' + s + ']')
            : querySelectorAllIE(s)).length
        if (l)
          if (1 < l)
            for (var u = 0, d = 1; u < l; )
              (e = c[u]),
                u
                  ? ((e.__bid__ = a + d), (e.id = s + d), d++)
                  : (e.__bid__ = a),
                n.push(e),
                u++
          else
            (i = s), ((e = document.getElementById(i)).__bid__ = a), n.push(e)
      }
      return n
    }
    function a() {
      function t(t) {
        return document.getElementById(t)
      }
      for (
        var e, i = [], n = 'A'.charCodeAt(), o = String.fromCharCode, r = 0;
        r < 26;
        r++
      ) {
        var a,
          s = 'block-' + (a = o(n + r)),
          c = '',
          l = (c = document.querySelectorAll
            ? document.querySelectorAll('*[id=' + s + ']')
            : querySelectorAllIE(s)).length
        if (l)
          if (1 < l)
            for (var u = 0, d = 1; u < l; )
              (e = c[u]),
                u
                  ? ((e.__bid__ = a + d), (e.id = s + d), d++)
                  : (e.__bid__ = a),
                i.push(e),
                u++
          else ((e = t(s)).__bid__ = a), i.push(e)
      }
      for (r = 0; r < 26; r++)
        for (var h = o(n + r), f = 0; f < 26; f++)
          (e = t((s = 'block-' + h + (a = o(n + f))))) &&
            ((e.__bid__ = h + a), i.push(e))
      return i
    }
    function j(t) {
      try {
        return (
          ('string' ==
            typeof (t = (function(t) {
              if (
                new RegExp(
                  '^[^\\x00-\\x20\\x7f\\(\\)<>@,;:\\\\\\"\\[\\]\\?=\\{\\}\\/\\u0080-\\uffff]+$'
                ).test(t)
              ) {
                t = new RegExp('(^| )' + t + '=([^;]*)(;|$)').exec(
                  document.cookie
                )
                if (t) return t[2] || ''
              }
              return ''
            })(t)) &&
            !(1 < t.length && 'deleted' == t) &&
            decodeURIComponent(t)) ||
          ''
        )
      } catch (t) {
        return ''
      }
    }
    function s(t) {
      var e = (t = t || window.event).target || t.srcElement
      if (
        (function(t, e, i) {
          if (!t._clickMapPBSent) {
            if (((t._clickMapPBSent = !0), e === i)) return (e._c = 1), 1
            if (!(1 <= e._c)) {
              if (
                ('number' != typeof e._c ? (e._c = 1) : e._c++,
                !e._adjustClickMap)
              ) {
                e._adjustClickMap = function() {
                  this._c = 0
                }.bind(e)
                try {
                  e.addEventListener
                    ? e.addEventListener('mousedown', e._adjustClickMap, !1)
                    : e.attachEvent('onmousedown', e._adjustClickMap)
                } catch (t) {}
              }
              return 1
            }
            e._c++
          }
        })(t, e, t.currentTarget || this)
      ) {
        var i,
          n,
          o =
            (document.documentElement && document.documentElement.scrollTop) ||
            document.body.scrollTop,
          r =
            (document.documentElement && document.documentElement.scrollLeft) ||
            document.body.scrollLeft,
          a =
            (document.documentElement &&
              document.documentElement.scrollWidth) ||
            document.body.scrollWidth,
          s =
            (document.documentElement &&
              document.documentElement.scrollHeight) ||
            document.body.scrollHeight,
          c =
            (document.documentElement &&
              document.documentElement.clientHeight) ||
            document.body.clientHeight,
          l =
            (document.documentElement &&
              document.documentElement.clientWidth) ||
            document.body.clientWidth,
          s = Math.max(s, c),
          c = Math.max(a, l),
          a = this.__bid__ || '',
          u = N(
            document.getElementsByTagName('body')[0].getAttribute('data-pb'),
            '&'
          ),
          d = N(this.getAttribute('data-pb'), '&')
        do {
          if (
            ((h = (i = e).getAttribute('rseat')),
            (n = i.tagName.toUpperCase()),
            (e = e.parentNode),
            i == this)
          )
            break
        } while (!h && 'A' !== n && 'IMG' !== n)
        var h,
          f,
          p,
          m = N(i.getAttribute('data-pb'), '&'),
          l = e && e.tagName && 'A' === e.tagName.toUpperCase(),
          l =
            (h &&
              ((f = h.match(/_AB_(.+)_TEST/)) &&
                ((h = (S = h.split(/_AB_.+_TEST/))[0] + (S[1] || '')),
                (f = f[1].split('--')),
                (f = /(android)|(like mac os x)/i.test(E) ? f[1] : f[0])),
              (m.rseat = h)),
            (S =
              'A' === n
                ? ((P = i.title || ''), (C = 'a'), i.getAttribute('href') || '')
                : ((C =
                    'IMG' === n
                      ? ((P = i.alt || ''), 'i')
                      : ((P = i.title || ''), 'e')),
                  '')),
            'A' !== n &&
              l &&
              e.getAttribute('href') &&
              (S =
                -1 == e.getAttribute('href').indexOf('javascript')
                  ? location.protocol +
                      e.getAttribute('href').replace(/^((http|https):)/, '') ||
                    ''
                  : e.getAttribute('href')),
            window.__global__info && window.__global__info.userInfo),
          l = l && l.hu,
          l = j('hu') || l,
          g = N(document.cookie, ';'),
          w = g.pu || g.P00003 || '',
          b = g.u || g.QC005 || '',
          _ = g.QC006 || '',
          v = g.v || '',
          I = g.mkey || '',
          k = l || -1,
          y = g.de
        try {
          p = JSON.parse(g.QYABEX || '{}')
        } catch (t) {
          p = {}
        }
        var C,
          P,
          S,
          l = (window.Q && Q.PageInfo) || {},
          l =
            'tw_t' !==
            (l = window.uniqy ? (window.uniqy && uniqy.PageInfo) || {} : l).i18n
              ? 'cn_s'
              : 'tw_t',
          A = {
            block: a,
            rt: C,
            r: P,
            rlink: S,
            pu: decodeURIComponent(w),
            u: decodeURIComponent(b),
            jsuid: decodeURIComponent(_),
            ce: window.webEventID || '',
            re: c + '*' + s,
            clkx: t.clientX + r,
            clky: t.clientY + o,
            mod: l,
            v: v,
            mkey: I,
            tm:
              window.__qlt && window.__qlt.statisticsStart
                ? new Date() - window.__qlt.statisticsStart
                : ''
          },
          a =
            (f && p[f] && p[f].abtest && (A.abtest = p[f].abtest),
            window.intlPageInfo || {})
        'global' === a.i18n &&
          ((C = a.pbInfos || {}),
          (P = new lib.kit.Url(document.location.href)),
          isPWA() && (A.stuptype = 'shortcut'),
          (A.lang = P.getParam('lang') || C.lang || ''),
          (A.mod = P.getParam('mod') || C.mod || ''),
          (A.rpage = C.rpage || ''),
          (A.purl = location.href),
          (A.rfr = document.referrer),
          (A.lrfr = g.QC007 || ''),
          C.pbClickParams && (window.pingbackParams = C.pbClickParams),
          (S = new lib.action.Qa().getUtmPbParams()),
          (A.vfm = S)),
          window.pingbackParams &&
            (A = Object.assign
              ? Object.assign({}, window.pingbackParams, A)
              : U({}, window.pingbackParams, A)),
          window.Q &&
            Q.PageInfo &&
            Q.PageInfo.playPageInfo &&
            Q.PageInfo.playPageInfo.videoTemplate &&
            (A.tmplt = Q.PageInfo.playPageInfo.videoTemplate || ''),
          'global' === a.i18n
            ? window.mainQaInstance &&
              window.mainQaInstance.getHu(function(e) {
                window.mainQaInstance.getFingerPrint(function(t) {
                  ;(A.dfp = t),
                    (A.hu = decodeURIComponent(k) || e),
                    (A.pu =
                      decodeURIComponent(w) ||
                      window.mainQaInstance.getUserInfoUid()),
                    (A.de =
                      decodeURIComponent(y) ||
                      window.mainQaInstance.getQtsid()),
                    (A.ce = window.mainQaInstance.getWeid()),
                    (A.stime = new Date().getTime()),
                    (A.timezone = new Date().toUTCString()),
                    T(U(A, u, d, m), M)
                })
              })
            : T(U(A, u, d, m), M)
      }
    }
    function o() {
      for (
        var t = a(),
          e = window.intlPageInfo || {},
          i =
            ('global' === e.i18n &&
              (e = e.pbInfos || {}).blockInfo &&
              (t = r(e.blockInfo)),
            (function() {
              for (
                var t,
                  e,
                  i = {},
                  n = [],
                  o = 'block-',
                  r = document.getElementsByTagName('qchunk'),
                  a = 0,
                  s = r.length;
                a < s;
                a++
              )
                if (
                  (e = (t = r[a]).getAttribute('data-id') || '')
                    .substr(0, o.length)
                    .toLowerCase() == o
                ) {
                  var c = e.substr(o.length)
                  if (i[c]) {
                    for (var l, u = ++i[c]; (l = c[0]), (l += u), u++, i[l]; );
                    ;(i[l] = 1), t.setAttribute('data-id', l), (c = l)
                  } else i[c] = 1
                  ;(t.__bid__ = c), n.push(t)
                }
              return n
            })()),
          n = 0,
          o = t.length;
        n < o;
        n++
      )
        -1 == i.indexOf(t[n]) && i.push(t[n])
      return (
        ((e = document.getElementsByTagName('body')[0]).__bid__ = 'body'),
        i.push(e),
        i
      )
    }
    function n(t) {
      var e = l || []
      if (t && t.data) {
        if (0 === (e = t.data.down('[data-block-name]') || []).length) return
        e.forEach(function(t) {
          t.__bid__ = t.id.substr('block-'.length)
        })
      }
      for (var i = 0, n = e.length; i < n; i++) {
        var o,
          r = e[i],
          a = ''
        window.Q ? (a = Q(r)) : window.jQuery && (a = $(r)),
          a.attr('data-asyn-pb') ||
            ((o = s.bind(r)),
            r.addEventListener
              ? r.addEventListener('mousedown', o, !1)
              : r.attachEvent('onmousedown', o),
            a.attr('data-asyn-pb', 'true'))
      }
    }
    function c() {
      var t = l || []
      if (t.length)
        for (var e = 0, i = t.length; e < i; e++) {
          var n,
            o = t[e]
          o.getAttribute('data-asyn-pb') ||
            ((n = s.bind(o)),
            o.addEventListener
              ? o.addEventListener('mousedown', n, !1)
              : o.attachEvent('onmousedown', n),
            o.setAttribute('data-asyn-pb', 'true'))
        }
    }
    var l = o()
    if (window.Q && Q.$)
      Q.$(window).on('scroll', n),
        Q.$(window).on('resize', n),
        Q.event.customEvent.on('bindingPingback', n),
        n()
    else if (window.jQuery)
      try {
        $(window).on('scroll', n), $(window).on('resize', n), n()
      } catch (t) {}
    else c()
    function N(t, e) {
      var i = {}
      if (((e = e || '&'), t))
        for (var n, o = t.split(e), r = 0, a = o.length; r < a; r++)
          (n = o[r]) &&
            (n = n.split(/\s*=\s*/g))[0] &&
            (i[n[0].replace(/^\s*|\s*$/g, '')] = n[1] || '')
      return i
    }
    function U(t, e) {
      for (var i, n = t || {}, o = 1, r = arguments.length; o < r; o++)
        if ((i = arguments[o]))
          for (var a in i) i.hasOwnProperty(a) && (n[a] = i[a])
      return n
    }
    ;(lib.action.Qa.prototype.reloadClickMap = function() {
      l = o()
      var t = (this.qchunks = l)
      if (t.length)
        for (var e = 0, i = t.length; e < i; e++) {
          var n = t[e]
          n.getAttribute('data-asyn-pb') && n.removeAttribute('data-asyn-pb')
        }
      c()
    }),
      window.lib &&
        window.lib.action &&
        window.lib.action.Qa &&
        window.postMessage &&
        window.postMessage('qaLoad', location.href)
  })()
