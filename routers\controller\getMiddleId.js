const { getVideoDecodeId } = require('../../server/decodeId')

const getMiddleIdController = async (ctx, next) => {
  const paramsId = ctx.params.id
  const pid = ctx.query.pid
  const queryId = ctx.query.id
  const mod = ctx.params.mod
  const id = paramsId || queryId

  if (mod && id) {
    ctx._custom.uri = `${ctx.protocol}://${ctx.host}/shortMiddle/${id}`
    ctx.status = 301
  }

  if (id) {
    const idObj = getVideoDecodeId(id)
    global.decodeId = idObj[id] ? idObj[id].tvId || idObj[id].albumId : 0
  }
  ctx.query.id = id
  ctx.query.pid = pid
  ctx._custom.path = '/shortMiddle'
  await next()
}

module.exports = getMiddleIdController
