import React from 'react'
import { connect } from 'react-redux'
import DeviceCtx from '@/components/context'
import Meta from '@/components/common/Meta'
import TongJiPb from '@/components/common/TongJiPb'
// import { locUrl } from '@/kit/common'
import LoginTvWrapper from '@/style/loginTv'
import otpCodeLogin from '@/utils/otpCodeLogin'
import Fengkong from '@/components/common/Fengkong'
import { getCookies } from '@/kit/cookie'
import { hostName } from '@/kit/common'
import { sendClickPb, sendBlockPb } from '@/utils/pingBack'
import { fetchUserInfoSsrAction } from '@/store/reducers/user/user'

const reg = /^[0-9a-zA-Z]{6}$/
class LoginTv extends React.Component {
  static contextType = DeviceCtx

  constructor(props, context) {
    super(props)
    const { ptid } = context
    this.state = {
      codeValue: '',
      isError: false,
      errorMg: '',
      ptid,
      lang: getCookies('lang'),
      fkToken: '',
      dfp: '',
      isShowFk: false,
      isFocus: false,
      countdownTime: 3
    }

    this.inputRef = React.createRef()

    this.onChange = this.onChange.bind(this)
    this.handleSubmit = this.handleSubmit.bind(this)
    this.verifySliderSus = this.verifySliderSus.bind(this)
  }

  static async getInitialProps({ ctx }) {
    const { query, store } = ctx
    const vizioLogin = query.isVizio || false
    const hasSuccess = query.success || false
    const isVip = await store.dispatch(
      fetchUserInfoSsrAction({ param: { fields: 'userinfo' }, ctx, _catch: {} })
    )
    return { hasSuccess, isVip, vizioLogin }
  }

  componentDidMount() {
    const { hasSuccess, isVip, vizioLogin } = this.props
    if (!isVip && hasSuccess && !vizioLogin) {
      sendBlockPb('nonvip_joinvip', {
        rpage: 'global-pssdk-login-tv-success',
        fc: 'b9614f66d942eeb3'
      })
      this.countDown()
    }
  }

  fetchScript(url) {
    const script = document.createElement('script')
    script.src = url
    script.type = 'text/javascript'
    script.addEventListener('load', () => {
      this.setState({
        dfp: (window.dfp && window.dfp.tryGetFingerPrint()) || ''
      })
    })
    document.body.appendChild(script)
  }

  fetchLink(url) {
    const link = document.createElement('link')

    link.type = 'text/css'
    link.rel = 'stylesheet'
    link.href = url

    document.body.appendChild(link)
  }

  onFocus() {
    this.setState({
      isFocus: true,
      isError: false,
      errorMg: ''
    })
  }

  onBlur() {
    const { langPkg } = this.props
    const { codeValue } = this.state
    const trueLangPkg = langPkg.toJS()

    if (codeValue.length < 6 || !reg.test(codeValue)) {
      this.setState({
        isError: true,
        isFocus: false,
        errorMg: trueLangPkg['PCW_FRONTEND_1692865648171_119']
      })
    } else {
      this.setState({
        isError: false,
        isFocus: false
      })
    }
  }

  async handleSubmit({ fkToken }) {
    const { codeValue, ptid } = this.state
    const { langPkg, netErrorLangPkg, vizioLogin } = this.props
    const trueLangPkg = langPkg.toJS()

    if (!reg.test(codeValue)) {
      this.setState({
        isError: true,
        errorMg: trueLangPkg['PCW_FRONTEND_1692865648171_119']
      })
      return
    }

    try {
      const res = await otpCodeLogin({
        code: codeValue,
        ptid,
        fkToken
      })

      if (res.code === 'A00000') {
        window.location.href = vizioLogin
          ? `${hostName()}tv?success=1&isVizio=1`
          : `${hostName()}tv?success=1`
      } else if (res.code === 'A00001') {
        // 未登录
        window.location.href = vizioLogin
          ? `${hostName()}login?otp=1&is_login_page=1&code=${codeValue}&isVizio=1`
          : `${hostName()}login?otp=1&is_login_page=1&code=${codeValue}`
      } else if (res.code === 'P01012') {
        // 激活码不存在
        this.setState({
          isError: true,
          errorMg: trueLangPkg['PCW_FRONTEND_1692784705964_916']
        })
      } else if (res.code === 'P01009') {
        // 激活码已使用
        this.setState({
          isError: true,
          errorMg: trueLangPkg['PCW_FRONTEND_1692784866490_756']
        })
      } else if (res.code === 'P01010') {
        // 激活码已过期
        this.setState({
          isError: true,
          errorMg: trueLangPkg['PCW_FRONTEND_1692784866490_756']
        })
      } else if (res.code === 'P00159') {
        // 高危
        this.setState({
          isError: true,
          errorMg: trueLangPkg['PCW_FRONTEND_1692866013950_125  ']
        })
      } else if (res.code === 'P00223') {
        // 中低
        this.setState({
          isShowFk: true,
          fkToken: res.data.token
        })
      } else {
        this.setState({
          isError: true,
          errorMg: netErrorLangPkg
        })
      }
    } catch (err) {
      this.setState({
        isError: true,
        errorMg: netErrorLangPkg
      })
    }
  }

  onChange(e) {
    const key = e.target.value.trim()
    // const { codeValue } = this.state

    this.setState({
      codeValue: key
    })
  }

  verifySliderSus() {
    this.handleSubmit({
      fkToken: this.state.fkToken
    })

    this.setState({
      isShowFk: false
    })
  }

  countDown = () => {
    const timer = setInterval(() => {
      const { countdownTime } = this.state
      const time = countdownTime - 1
      this.setState({
        countdownTime: time
      })
      if (time === 0) {
        clearInterval(timer)
        sendClickPb({
          rpage: 'global-pssdk-login-tv-success',
          rseat: 'nonvip_joinvip_auto',
          block: 'nonvip_joinvip_auto',
          fc: 'b9614f66d942eeb3'
        })
        window.location.href = '//www.iq.com/vip/order?fc=b9614f66d942eeb3'
      }
    }, 1000)
  }

  render() {
    const { langPkg, hasSuccess, isVip, vizioLogin } = this.props
    const {
      codeValue,
      isError,
      errorMg,
      lang,
      fkToken,
      dfp,
      isShowFk,
      isFocus,
      countdownTime
    } = this.state
    const trueLangPkg = langPkg.toJS()

    return (
      <>
        <Meta
          desc={trueLangPkg.home_html_description}
          title={trueLangPkg.home_html_title}
        />
        <LoginTvWrapper>
          <div
            id="login-sdk-container"
            style={{
              position: 'relative',
              background: '#fff',
              minHeight: '600px'
            }}
          >
            <div
              className="section1"
              style={{ display: `${hasSuccess ? 'none' : 'block'}` }}
            >
              <div className="desc">
                {trueLangPkg?.PCW_FRONTEND_1692079999407_573}
              </div>
              <div
                className={`${isError ? 'error' : ''} ${
                  isFocus ? 'focus' : ''
                } input-box`}
              >
                <input
                  className="input"
                  ref={this.inputRef}
                  autoComplete="off"
                  onChange={this.onChange}
                  onFocus={e => this.onFocus(e)}
                  onBlur={e => this.onBlur(e)}
                  type="text"
                  // placeholder={hotPlaceholder || 'search'}
                  value={codeValue}
                  maxLength="6"
                  // onKeyDown={this.handleKeyDown}
                  // rseat=""
                />
                <div className="tip">
                  {trueLangPkg?.PCW_FRONTEND_1692080042095_425}
                </div>
              </div>
              <div
                className="error-tip"
                style={{ display: `${isError ? 'block' : 'none'}` }}
              >
                {errorMg}
              </div>
              <div
                className="button"
                onClick={this.handleSubmit}
                role="button"
                tabIndex="0"
                rseat="continue"
                data-pb="block=continue&rpage=global-pssdk-login-tv-code"
              >
                {trueLangPkg?.PCW_FRONTEND_1692080076568_375}
              </div>
            </div>
            {isShowFk && (
              <div className="ibd-fengkong-wrapper">
                <div className="ibd-fengkong-outer">
                  <div id="sign_fengkong" />
                  <Fengkong
                    id="sign_fengkong"
                    dfp={dfp}
                    lang={lang}
                    token={fkToken}
                    // ptid={getPtid()}
                    verifySliderSus={this.verifySliderSus}
                  />
                </div>
              </div>
            )}
            <div
              className="section2"
              style={{ display: `${hasSuccess ? 'block' : 'none'}` }}
            >
              <div className="icon">
                <img
                  alt="icon"
                  src="//www.iqiyipic.com/lequ/20230821/<EMAIL>"
                />
              </div>
              {vizioLogin ? (
                <div className="desc">
                  {trueLangPkg.PCW_FRONTEND_1692079695170_213}
                </div>
              ) : (
                <div className="desc">
                  {isVip
                    ? trueLangPkg?.PCW_FRONTEND_1692079695170_213
                    : trueLangPkg?.PCW_FRONTEND_1712462952541_825}
                </div>
              )}
              {vizioLogin ? (
                <a
                  className="button"
                  rseat={isVip ? '' : 'nonvip_joinvip_button'}
                  data-pb="block=nonvip_joinvip_button&rpage=global-pssdk-login-tv-success&fc=b9614f66d942eeb3"
                  href={`
                     //www.iq.com
                    `}
                >
                  {trueLangPkg.PCW_FRONTEND_1692079846155_783}
                </a>
              ) : (
                <a
                  className="button"
                  rseat={isVip ? '' : 'nonvip_joinvip_button'}
                  data-pb={
                    isVip
                      ? ''
                      : 'block=nonvip_joinvip_button&rpage=global-pssdk-login-tv-success&fc=b9614f66d942eeb3'
                  }
                  href={`${
                    isVip
                      ? '//www.iq.com'
                      : '//www.iq.com/vip/order?fc=b9614f66d942eeb3'
                  }`}
                >
                  {isVip
                    ? trueLangPkg?.PCW_FRONTEND_1692079846155_783
                    : trueLangPkg?.PCW_FRONTEND_1712463005850_223}
                </a>
              )}
              {/* <a
                className="button"
                rseat={isVip ? '' : 'nonvip_joinvip_button'}
                data-pb={
                  isVip
                    ? ''
                    : 'block=nonvip_joinvip_button&rpage=global-pssdk-login-tv-success&fc=b9614f66d942eeb3'
                }
                href={`${
                  isVip
                    ? '//www.iq.com'
                    : '//www.iq.com/vip/order?fc=b9614f66d942eeb3'
                }`}
              >
                {isVip
                  ? trueLangPkg?.PCW_FRONTEND_1692079846155_783
                  : trueLangPkg?.PCW_FRONTEND_1712463005850_223}
              </a> */}
              {!isVip && !vizioLogin && (
                <div className="non-vip-desc">
                  {trueLangPkg?.PCW_FRONTEND_1712463066136_935.replace(
                    '%s',
                    countdownTime
                  )}
                </div>
              )}
            </div>
          </div>
        </LoginTvWrapper>
        <TongJiPb
          rpage={
            hasSuccess
              ? 'global-pssdk-login-tv-success'
              : 'global-pssdk-login-tv-code'
          }
        />
        {!hasSuccess && (
          <>
            <script
              type="text/javascript"
              src="//security.iq.com/static/iq/v2/verifycenter/js/verifycenter.js"
              async
            />
            <link
              rel="stylesheet"
              type="text/css"
              async
              href="//security.iq.com/static/iq/v2/verifycenter/css/verifycenter.css"
            />
          </>
        )}
      </>
    )
  }
}

const mapStateToProps = state => ({
  langPkg: state.getIn(['language', 'langPkg']),
  netErrorLangPkg: state.getIn([
    'vip',
    'cashierLangPkg',
    'PCW_CASHIER_1650957546922_650'
  ]),
  userInfo: state.getIn(['user', 'userInfo']),
  langs: state.getIn(['language', 'modeLangObj'])
})

export default connect(mapStateToProps)(LoginTv)
