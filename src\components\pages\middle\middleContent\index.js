import React, { useEffect, useRef, useState } from 'react'
import { connect } from 'react-redux'
import { getCookies } from '@/kit/cookie'
import { queryFromUrl } from '@/kit/url'

import { ContentWrapper } from './style'

const MiddleContent = props => {
  const { albumInfo, langPkg, qipuId, rpage, curVideoInfo } = props
  const albumData = albumInfo.toJS()
  const curVideoInfoData = curVideoInfo.toJS()
  const isShort = albumData.isShort

  // 创建 ref 来引用 DOM 元素
  const parentRef = useRef(null)
  const [linkUrl, setLinkUrl] = useState('#')

  useEffect(() => {
    const url = getOneLink()
    setLinkUrl(url)
  }, [])

  const getOneLink = () => {
    const url = window.location.href

    let href = `https://iq.onelink.me/mY1d?`

    const params = {
      pid: queryFromUrl(url, 'pid'),
      af_siteid: '__CSITE__',
      c: queryFromUrl(url, 'c'),
      af_channel: queryFromUrl(url, 'af_channel'),
      af_c_id: queryFromUrl(url, 'af_c_id'),
      af_adset: queryFromUrl(url, 'af_adset'),
      af_adset_id: queryFromUrl(url, 'af_adset_id'),
      af_ad: queryFromUrl(url, 'af_ad'),
      af_ad_id: queryFromUrl(url, 'af_ad_id'),
      af_click_lookback: '7d',
      af_dp: 'iqyinter://mobile/register_business/qyclient',
      // af_dp: queryFromUrl(url, 'af_dp'),
      // af_sub2: setDeepLinkValue(),
      deep_link_value: setDeepLinkValue(),
      fbclid: queryFromUrl(url, 'fbclid'),
      ttclid: queryFromUrl(url, 'ttclid'),
      // af_ad_type: '__CTYPE__',
      af_sub6: new Date().getTime()
      // os: '__OS__',
      // af_ip: '__IP__',
      // af_ua: '__UA__',
      // af_lang: '__SL__'
    }
    const filteredParams = Object.keys(params)
      .filter(key => {
        return params[key] !== ''
      })
      .reduce((acc, key) => {
        acc[key] = params[key]
        return acc
      }, {})

    const queryString = Object.keys(filteredParams)
      .map(
        key =>
          `${encodeURIComponent(key)}=${encodeURIComponent(
            filteredParams[key]
          )}`
      )
      .join('&')

    if (queryString) {
      href += queryString
    }
    console.log(href)

    return href
  }

  const setDeepLinkValue = () => {
    if (isShort) {
      const bizParams = `aid=${albumData?.albumId ||
        curVideoInfoData?.albumId}&tvid=${qipuId}`
      const paramObj = {
        biz_id: '102',
        biz_plugin: 'qiyiplayer',
        biz_params: {
          biz_params: bizParams,
          biz_sub_id: '1022'
        }
      }

      let paramStr = JSON.stringify(paramObj)
      paramStr = encodeURIComponent(paramStr)

      return paramStr
    } else {
      const albumId = curVideoInfo.get('albumId') || 0
      let tvId = curVideoInfo.get('tvId') || 0
      const mod = getCookies('mod') || ''
      if (tvId === albumId) {
        tvId = 0
      }
      const bizParams =
        `aid=${albumId}&tvid=${tvId}&ctype=0&` +
        `pc=0&video_type=0&from_type=27&` +
        `from_sub_type=10&mod=${mod}`
      const paramObj = {
        biz_id: '102',
        biz_plugin: 'qiyiplayer',
        biz_params: {
          biz_url: window.location.href,
          biz_sub_id: '101',
          biz_params: bizParams,
          biz_extend_params: '',
          biz_dynamic_params: '',
          biz_statistics: ''
        }
      }

      let paramStr = JSON.stringify(paramObj)
      paramStr = encodeURIComponent(paramStr)

      return paramStr
    }
  }

  const handleCallApp = () => {
    // sendClickPb({
    //   rpage,
    //   rseat: 'download',
    //   block: 'shortmiddle'
    // })

    window.open(linkUrl, '_blank')
    // window.location.href = linkUrl
  }

  let thumbnailUrl2 =
    albumData?.thumbnailUrl2 || curVideoInfoData?.thumbnailUrl2 || ''
  if (thumbnailUrl2 && thumbnailUrl2.indexOf('_260_360') !== -1) {
    thumbnailUrl2 = thumbnailUrl2.replace('_260_360', '_490_654')
  }

  return (
    <ContentWrapper ref={parentRef}>
      <div rel="noreferrer">
        <div className="button-section">
          <div onClick={handleCallApp} role="button" tabIndex={0}>
            <img
              alt="logo-icon"
              src="https://www.iqiyipic.com/global/<EMAIL>"
              className="logo-icon"
              rseat="download"
              data-pb={`rpage=${rpage}&block=shortmiddle`}
            />
          </div>
          <span
            className="download-button"
            onClick={handleCallApp}
            rseat="download"
            data-pb={`rpage=${rpage}&block=shortmiddle`}
            role="button"
            tabIndex={0}
          >
            <img
              alt="download"
              src="https://www.iqiyipic.com/global/<EMAIL>"
            />
            APP
          </span>
        </div>
        <div
          className="image-container"
          role="button"
          tabIndex="0"
          onClick={handleCallApp}
          style={{
            backgroundImage: `url(${thumbnailUrl2?.replace('http:', 'https:') ||
              ''})`
          }}
        >
          <div className="image-content">
            <div className="card">
              <img
                alt="main-img"
                className="card-img"
                src={thumbnailUrl2?.replace('http:', 'https:')}
                rseat="download"
                data-pb={`rpage=${rpage}&block=shortmiddle`}
              />
              <img
                alt="play-icon"
                className="play-icon"
                src="https://www.iqiyipic.com/lequ/20250423/<EMAIL>"
                rseat="download"
                data-pb={`rpage=${rpage}&block=shortmiddle`}
              />
            </div>
          </div>
        </div>
        <p className="image-title">
          {albumData?.name || curVideoInfoData?.name}
        </p>
        <div className="desc-container">
          <div className="desc-key">{langPkg.get('play_introduction')}</div>
          <p className="desc">
            {albumData?.desc || curVideoInfoData?.description}
          </p>
          <div className="mask" />
        </div>
        <div
          className="bottom-btn"
          onClick={handleCallApp}
          role="button"
          tabIndex="0"
          rseat="download"
          data-pb={`rpage=${rpage}&block=shortmiddle`}
        >
          <div>
            {isShort
              ? langPkg.get('PCW_FRONTEND_1739881846437_986')
              : langPkg.get('PCW_FRONTEND_1751614251510_387')}
          </div>
        </div>
      </div>
    </ContentWrapper>
  )
}

const mapStateToProps = state => ({
  langPkg: state.getIn(['language', 'langPkg'])
  // modeLangObj: state.getIn(['language', 'modeLangObj'])
})

export default connect(mapStateToProps)(MiddleContent)
