import React from 'react'
import { connect } from 'react-redux'
import Mobile from '@/components/pages/vip/Mobile'
import VipPageContainer from '@/components/pages/vip/VipPageContainer'
import { locUrl } from '@/kit/common'
import Meta from '@/components/common/Meta'
import { queryFromUrl } from '@/kit/url'
import { utmPbParams } from '@/utils/pingBack'
import { getAbtest } from '@/components/pages/vip/api'

class Page extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      mobile: null
    }
  }

  componentDidMount() {
    const _localUrl = locUrl()
    this.setState({
      mobile: window.atob(
        decodeURIComponent(queryFromUrl(_localUrl, 'mobile')) || ''
      )
    })
    this.sendPb()
  }

  sendPb() {
    const varScript = document.createElement('script')
    const rpage = 'gopay_getphone'
    const { modeLangObj } = this.props
    const lang = modeLangObj.get('lang')
    const mod = modeLangObj.get('mod')
    const _localUrl = locUrl()
    const urlabtest = queryFromUrl(_localUrl, 'abtest') || ''
    const fc = queryFromUrl(_localUrl, 'fc') || ''
    const fv = queryFromUrl(_localUrl, 'fv') || ''

    const testRes = getAbtest()
    const abtest = `page_cashier${testRes ? `,${testRes}` : ''}${
      urlabtest ? ',' + urlabtest : ''
    }`
    const { vfm, encodeVfm } = utmPbParams()

    varScript.innerHTML = `
      window.intlPageInfo = window.intlPageInfo || {};
      intlPageInfo.i18n = 'global';
      intlPageInfo.pbInfos = {
        "rpage": "${rpage}",
        "lang": "${lang}",
        "mod": "${mod}"
      }
      intlPageInfo.pbInfos.pbShowParams = intlPageInfo.pbInfos.pbShowParams || {}
      intlPageInfo.pbInfos.pbClickParams = intlPageInfo.pbInfos.pbClickParams || {}
      intlPageInfo.pbInfos.pbShowParams.bstp = 56
      intlPageInfo.pbInfos.pbShowParams.abtest = '${abtest}'
      intlPageInfo.pbInfos.pbShowParams.fc = '${fc || ''}'
      intlPageInfo.pbInfos.pbShowParams.fv = '${fv || ''}'
      if("${vfm}") {
        intlPageInfo.pbInfos.pbShowParams.vfm = "${encodeVfm}"
        intlPageInfo.pbInfos.pbClickParams.vfm = "${vfm}"
      }
    `
    document.body.append(varScript)
    // document.body.append(script)
  }

  render() {
    const cashierLangPkg = this.props.cashierLangPkg.toJS()

    const _localUrl = locUrl()
    // const mobile = decodeURIComponent(queryFromUrl(_localUrl, 'mobile')) || ''

    const pageInfo = {
      order: queryFromUrl(_localUrl, 'order') || '',
      vipOrder: queryFromUrl(_localUrl, 'vipOrder') || '',
      typeName:
        decodeURIComponent(queryFromUrl(_localUrl, 'vipTypeName')) || '',
      name: decodeURIComponent(queryFromUrl(_localUrl, 'text3')) || '',
      currencySymbol:
        decodeURIComponent(queryFromUrl(_localUrl, 'currencySymbol')) || '',
      price: queryFromUrl(_localUrl, 'price') || '',
      originalPrice: queryFromUrl(_localUrl, 'originalPrice') || '',
      autorenewTip:
        decodeURIComponent(queryFromUrl(_localUrl, 'autorenewTip')) || '',
      detail: decodeURIComponent(queryFromUrl(_localUrl, 'desc')) || '',
      cashierType:
        decodeURIComponent(queryFromUrl(_localUrl, 'cashierType')) || '',
      mobile: this.state.mobile,
      // payTypeIcon:
      //   decodeURIComponent(queryFromUrl(_localUrl, 'payTypeIcon')) || '',
      // payTypeName:
      //   decodeURIComponent(queryFromUrl(_localUrl, 'payTypeName')) || '',
      selectedId:
        decodeURIComponent(queryFromUrl(_localUrl, 'selectedId')) || ''
      // returnUrl: decodeURIComponent(queryFromUrl(_localUrl, 'returnUrl')) || ''
    }
    const urlabtest = queryFromUrl(_localUrl, 'abtest') || ''
    const testRes = getAbtest()
    const abtest = `page_cashier${testRes ? `,${testRes}` : ''}${
      urlabtest ? ',' + urlabtest : ''
    }`
    return (
      <>
        <Meta
          desc={cashierLangPkg.cashier_html_description}
          title={cashierLangPkg.upgrade_VIP}
        />
        <VipPageContainer autoWidth>
          <Mobile pageInfo={pageInfo} abtest={abtest} />
        </VipPageContainer>
      </>
    )
  }
}

const mapStateToProps = state => {
  return {
    modeLangObj: state.getIn(['language', 'modeLangObj']),
    cashierLangPkg: state.getIn(['vip', 'cashierLangPkg']),
    vipLangPkg: state.getIn(['vip', 'vipLangPkg'])
  }
}
export default connect(mapStateToProps)(Page)
