import { put, take, fork, select } from 'redux-saga/effects'
import $http from '@/kit/fetch'
import { isServer } from '@/kit/device'
import { isAlbumId, isBodanId, isVideoId } from '@/utils/video'
import { getCookies } from '@/kit/cookie'
import {
  FETCH_AV_LIST,
  FETCH_SV_LIST,
  getVideoInfoAction,
  getAlbumInfoAction,
  getCurVideoInfoAction,
  cachePlayListAction,
  curVideoPageNoAction,
  curEpisodeTagInfoAction,
  FETCH_VIDEO_INFO,
  FETCH_SIMILAR_PLAY,
  getSimilarPlayAction,
  setPlayAjaxSimilarListAction,
  changeVideoPageTagAction,
  getPlayStatusCodeAction,
  FETCH_PLAY_AD_INFO,
  getPlayAdInfoAction,
  getPlayScoreInfoAction,
  FETCH_PLAY_BANNER_INFO,
  getPlayBannerInfoAction,
  getBodanInfoAction,
  FETCH_SHORT_SIMILAR_LIST,
  getShortSimilarListAction,
  FETCH_CUR_EPG_INFO,
  // getSuperSeasonsAction,
  getPlayReserveShow
} from '@/store/reducers/play/play'
import {
  INTL_EPG_INFO,
  // EPISODE_LIST,
  SIMILAR_PLAY,
  SIMILAR_PLAY_JS,
  DECODE_ID,
  JUJI_EPISODE_LIST_V2,
  JUJI_EPISODE_LIST_QAE_V2,
  VIP_SHOW_AD,
  BODAN_LIST,
  EPISODE_LIST_PAGING_V2,
  EPISODE_LIST_PAGING_QAE_V2,
  // FUNCS_API_TEST,
  FUNCS_API_QAE,
  GET_SCORE_QAE,
  VIDEO_FATHER_COLLECTION_QAE,
  SHORT_SIMILAR_PLAY_JS,
  CLIENT_INTL_EPG_INFO,
  // SUPER_SERIES_ALBUM,
  EPG_FILTER
} from '@/constants/interfaces'
import {
  commonDeviceIdParams,
  iqSwitchPlatformId,
  showPlayAlbumReserveButton,
  handleFilterFailData
} from '@/kit/common'
import { checkQipuIdStr } from '@/kit/url'
import {
  handleVideoInfo,
  handleCurVideoInfo,
  handlePlayAlbumInfo,
  handleJujiList,
  handleLaiyuanList,
  handleSimiplayPlayList,
  handlePlayAdInfo,
  handleBodanList,
  handlePlayBannerInfo
  // handleSuperSeries
} from './handleData'
import { getIsVipSSR } from '../user/user'

export function* getAvList(params) {
  const { aid, pageNo, from, to, msg, tag, jsReq, ctx } = params
  try {
    const state = yield select()
    const cachePlayList = state.getIn(['play', 'cachePlayList']).toJS()
    const curList = cachePlayList[pageNo] || []
    if (curList.length) {
      if (tag !== 'preload') {
        yield put(curVideoPageNoAction({ pageNo, msg }))
        yield put(curEpisodeTagInfoAction({ pageNo }))
      }
      yield put(changeVideoPageTagAction(tag))
      return
    }
    const options = {
      ctx
    }
    const params = commonDeviceIdParams(state)
    options.params = params
    options.params.endOrder = to
    options.params.startOrder = from
    const isVip = yield getIsVipSSR({ param: { fields: 'userinfo' }, ctx })
    options.params.isVip = isVip
    let url
    if (jsReq) {
      url = JUJI_EPISODE_LIST_V2 + aid
    } else {
      url = JUJI_EPISODE_LIST_QAE_V2 + aid
    }
    // http://pcw.gateway.prod.online.qiyi.qae/api/v2/episodeListSource/4882482702868101?platformId=3&modeCode=hk&langCode=zh_cn&deviceId=9850a487c4b6ba308e44a629d71bc385&endOrder=40&startOrder=1
    // console.log('=====cachePlayList========',url)
    const data = yield $http(url, options)
    if (data.code === '0') {
      params.aid = aid
      const langPkg = state.getIn(['language', 'langPkg']).toJS()
      delete options.ctx
      data.data.urlParams = url + '=' + JSON.stringify(options.params)
      const jujiData = yield handleJujiList(data.data, params, langPkg)
      const { list, lockAid } = jujiData
      const listObj = {}
      listObj[pageNo] = list
      if (tag !== 'preload') {
        yield put(curVideoPageNoAction({ pageNo, msg }))
        yield put(curEpisodeTagInfoAction({ pageNo }))
      }
      yield put(cachePlayListAction({ pageNo, list, lockAid }))
      yield put(changeVideoPageTagAction(tag))
    }
  } catch (e) {
    console.log('request error' + e)
    if (tag !== 'preload') {
      yield put(curVideoPageNoAction({ pageNo, msg }))
      yield put(curEpisodeTagInfoAction({ pageNo }))
    }
  }
}

export function* getSvList(params) {
  try {
    // const aid = videoInfo.albumId
    const { videoInfo, albumInfo, pageIndex, msg, tag, jsReq, ctx } = params
    const options = {
      ctx
    }
    let pageNo = pageIndex
    const state = yield select()
    const cachePlayList = state.getIn(['play', 'cachePlayList']).toJS()
    const curList = cachePlayList[pageNo] || []
    if (curList.length) {
      if (tag !== 'preload') {
        yield put(curVideoPageNoAction({ pageNo, msg }))
        yield put(curEpisodeTagInfoAction({ pageNo }))
      }
      yield put(changeVideoPageTagAction(tag))
      return
    }
    options.params = commonDeviceIdParams(state)
    // options.params.size = 60
    // options.params.pos = 0
    // const url = EPISODE_LIST + aid
    options.params.albumId = videoInfo.albumId
    options.params.tvid = videoInfo.tvId
    options.params.size = 50
    if (pageNo) {
      options.params.pageIndex = pageNo
    }
    const url = jsReq ? EPISODE_LIST_PAGING_V2 : EPISODE_LIST_PAGING_QAE_V2
    const data = yield $http(url, options)
    if (data.code === '0') {
      pageNo = data.data.pageIndex || pageNo
      // const params = { aid }
      // params.modeCode = options.params.modeCode
      data.data.urlParams = url + '=' + JSON.stringify(options.params)
      data.data.params = options.params
      const langPkg = state.getIn(['language', 'langPkg']).toJS()
      const { list, pageRange } = handleLaiyuanList(data.data, {
        ctx,
        pkg: langPkg
      })
      // const listObj = {}
      // listObj[pageNo] = list
      // 首屏渲染检测当前视频(仅正片)不在播放列表里将其添加到第一个
      if (!jsReq) {
        const curId = videoInfo.tvId
        const filterArr = list.filter(item => {
          return curId === item.tvId
        })
        if (filterArr.length === 0 && videoInfo.contentType === 1) {
          list.unshift(videoInfo)
        }
      }
      if (tag !== 'preload') {
        yield put(curVideoPageNoAction({ pageNo, msg }))
        yield put(curEpisodeTagInfoAction({ pageNo }))
      }
      albumInfo.total = data.data.total
      albumInfo.totalPageRange = pageRange
      yield put(getAlbumInfoAction(albumInfo))
      yield put(cachePlayListAction({ pageNo, list }))
      yield put(changeVideoPageTagAction(tag))
    }
  } catch (e) {
    console.log('request error' + e)
  }
}

export function* decodeIdFunc(id, optionsParams) {
  try {
    let qipuId
    const { ctx, params } = optionsParams
    const options = {
      ctx
    }
    delete params['deviceId']
    options.params = params
    const url = DECODE_ID + id
    const data = yield $http(url, options)
    if (data.code === '0') {
      qipuId = data.data
    } else {
      qipuId = isServer ? global.decodeId : 0
    }
    return qipuId
  } catch (e) {
    console.log('decodeIdFunc request error' + e)
    return isServer ? global.decodeId : 0
  }
}

export function* getVideoInfo(param) {
  try {
    let videoInfo = {}
    let albumInfo = {}
    let bodanData = {}
    const options = {}
    const ctx = param.ctx || {}
    const id = param.id
    const finalId = checkQipuIdStr(id)
    if (!finalId) {
      yield put(getPlayStatusCodeAction(404))
      param._catch.resolve(404)
    }
    const isMobile = param.isMobile
    delete param.isMobile
    const urlParam = { encodeId: finalId }
    const state = yield select()
    options.params = commonDeviceIdParams(state)
    options.params.isSupportSEO = param.isSupportSEO
    const mod = options.params.modeCode || 'intl'
    // const lang = options.params.langCode
    urlParam.mod = mod
    // if (isServer) {
    //   options.params.clientIp = global.clientIp
    // }
    options.ctx = ctx
    // const resUrl = 'http://www.iq.com' + ctx.req.url + '&h_pltf=13'
    // const langCode = getCookies('lang', ctx) || 'en_us'
    // let tinyURLData = yield $http(
    //   `${TINYURL}?langCode=${langCode}&modeCode=${mod}&platformId=${platformId()}`,
    //   {
    //     method: 'POST',
    //     contentType: 'json',
    //     bodyData: { url: resUrl }
    //   }
    // )
    // tinyURLData = tinyURLData?.data
    // let tinyurl = ''
    // if (tinyURLData && +tinyURLData.code === 0) {
    //   tinyurl = tinyURLData?.data?.tinyurl
    // }
    const decodeId = yield decodeIdFunc(finalId, options)
    if (isBodanId(decodeId)) {
      bodanData = yield getBodanList(decodeId, urlParam, param)
      videoInfo = bodanData.firstVideo || {}
      // if (videoInfo.payMark !== 'VIP_MARK') {
      //   yield setAbTest(ctx)
      // }
      param._catch.resolve(videoInfo)
      yield put(getVideoInfoAction(videoInfo))
      yield put(getCurVideoInfoAction(videoInfo))
    } else if (isAlbumId(decodeId)) {
      albumInfo = yield getAlbumInfo(decodeId, ctx)
      const { defaultTvId } = albumInfo
      if (isVideoId(defaultTvId)) {
        const aurl = INTL_EPG_INFO + defaultTvId
        const albumData = yield $http(aurl, options)
        if (albumData.code === '0') {
          const langPkg = state.getIn(['language', 'langPkg']).toJS()
          videoInfo = handleVideoInfo(albumData.data, urlParam, langPkg)
          param._catch.resolve(videoInfo)
          // if (videoInfo.payMark !== 'VIP_MARK') {
          //   yield setAbTest(ctx)
          // }
          // videoInfo.tinyUrl = tinyurl
          yield put(getVideoInfoAction(videoInfo))
          yield put(getCurVideoInfoAction(videoInfo))
        } else {
          yield put(getPlayStatusCodeAction(404))
          param._catch.resolve(404)
        }
      } else {
        yield put(getPlayStatusCodeAction(404))
        param._catch.resolve(404)
      }
    } else if (isVideoId(decodeId)) {
      const vurl = INTL_EPG_INFO + decodeId
      const videoData = yield $http(vurl, options)
      if (videoData.code === '0') {
        const langPkg = state.getIn(['language', 'langPkg']).toJS()
        videoInfo = handleVideoInfo(videoData.data, urlParam, langPkg)
        // videoInfo.tinyUrl = tinyurl
        param._catch.resolve(videoInfo)
        // if (videoInfo.payMark !== 'VIP_MARK') {
        //   yield setAbTest(ctx)
        // }
        yield put(getVideoInfoAction(videoInfo))
        yield put(getCurVideoInfoAction(videoInfo))
        if (videoInfo.videoType !== 'singleVideo') {
          // 获取专辑信息
          albumInfo = yield getAlbumInfo(videoInfo.albumId, ctx)
        }
      } else {
        yield put(getPlayStatusCodeAction(404))
        param._catch.resolve(404)
      }
    } else {
      yield put(getPlayStatusCodeAction(404))
      param._catch.resolve(404)
    }
    if (videoInfo.videoType === 'bodan') {
      const pageNo = 1
      const list = bodanData.bodanList
      const listObj = {}
      listObj[pageNo] = list
      yield put(curVideoPageNoAction({ pageNo }))
      yield put(cachePlayListAction({ pageNo, list }))
    } else if (videoInfo.videoType === 'laiyuan') {
      // 来源
      yield getSvList({ videoInfo, albumInfo, ctx })
    } else if (videoInfo.videoType === 'juji') {
      // 剧集
      let to = videoInfo.to
      let total = to
      // http://pms.qiyi.domain/browse/GLOBALLINEBUG-11070
      if (albumInfo.total > albumInfo.maxOrder) {
        total = albumInfo.total
      } else {
        total = albumInfo.maxOrder
      }
      to = to > total ? total : to
      yield getAvList({
        aid: videoInfo.albumId,
        pageNo: videoInfo.pageNo,
        from: videoInfo.from,
        to,
        ctx
      })
    } else if (videoInfo.videoType === 'shortVideo') {
      yield getVideoCollection(videoInfo, ctx)
    } else {
      // 单视频
    }
    // 播放页是否展示预约按钮http://wiki.qiyi.domain/pages/viewpage.action?pageId=1055919344

    const newState = yield select()
    const episodeList = newState.getIn(['play', 'cachePlayList']).toJS()
    const curEpisodePageNo = newState
      .getIn(['play', 'curEpisodeTagInfo'])
      .get('pageNo')
    let reserveShow = {
      flag: false,
      qipuid: ''
    }
    if (videoInfo.videoType === 'laiyuan' || videoInfo.videoType === 'juji') {
      reserveShow = yield showPlayAlbumReserveButton(
        episodeList[curEpisodePageNo]
      )
    } else if (
      (videoInfo.videoType === 'shortVideo' ||
        videoInfo.videoType === 'singleVideo') &&
      videoInfo.fatherEpisodeId
    ) {
      options.params.qipuIds = videoInfo.fatherEpisodeId
      const filterData = yield $http(EPG_FILTER, options)
      const filterTag = handleFilterFailData(
        filterData,
        videoInfo.fatherEpisodeId
      )
      delete options.params.qipuIds
      reserveShow = {
        flag: !!filterTag,
        qipuid: decodeId
      }
    }
    yield put(getPlayReserveShow(reserveShow))

    // 为了seo，相关推荐改为ssr
    const aid = videoInfo.albumId || videoInfo.tvId
    const cid = videoInfo.channelId
    if (aid && cid) {
      const filterId = videoInfo.tvId
      yield getSimilarPlay({
        aid,
        cid,
        filterId,
        ctx,
        isMobile
      })
    }
    // schema 评分
    if (aid && videoInfo.videoType !== 'shortVideo') {
      yield getPlayScoreInfo(aid, ctx)
    }
    // 超剧集
    // const fatherCollectionIds =
    //   albumInfo.fatherCollectionIds || videoInfo.fatherCollectionIds || []
    // if (fatherCollectionIds.length && isAlbumId(aid)) {
    //   const url = SUPER_SERIES_ALBUM
    //   delete options.params.isSupportSEO
    //   options.params.qipuId = aid
    //   const superData = yield $http(url, options)
    //   if (superData.code === '0') {
    //     const langPkg = state.getIn(['language', 'langPkg']).toJS()
    //     superData.aid = aid
    //     const superInfo = handleSuperSeries(superData, langPkg)
    //     yield put(getSuperSeasonsAction(superInfo))
    //   }
    // }
  } catch (e) {
    console.log('getVideoInfo request error' + e)
    yield put(getPlayStatusCodeAction(404))
    param._catch.resolve(404)
  }
  param._catch.resolve(param)
}

export function* getPlayScoreInfo(aid, ctx) {
  try {
    const options = {
      ctx
    }
    const state = yield select()
    const params = commonDeviceIdParams(state)
    options.params = {
      platform_id: iqSwitchPlatformId(),
      app_v: '1.0.0',
      lang: params.langCode,
      app_lm: params.modeCode
    }
    const res = yield $http(FUNCS_API_QAE, options)
    if (res && res.code === 0) {
      const funcs = res.data || {}
      if (funcs.rate_mod) {
        options.params = params
        options.params.platformId = 3
        options.params.qipuId = aid
        const scoreInfo = yield $http(GET_SCORE_QAE, options)
        if (scoreInfo.code === '0') {
          const { score, totalVotes, ratio } = scoreInfo.data
          yield put(
            getPlayScoreInfoAction({
              isShowScore: true,
              score: score.toFixed(1),
              totalVotes,
              ratio
            })
          )
        }
      }
    }
  } catch (e) {
    console.log('getPlayScoreInfo error=====' + e)
  }
}

export function* getAlbumInfo(aid, ctx) {
  try {
    // const query = ctx.query || {}
    const options = {
      ctx
    }
    const state = yield select()
    options.params = commonDeviceIdParams(state)
    const url = INTL_EPG_INFO + aid

    const data = yield $http(url, options)
    if (data.code === '0') {
      const albumInfo = handlePlayAlbumInfo(data.data)
      yield put(getAlbumInfoAction(albumInfo))
      return albumInfo
    } else if (data.code > 200 && data.code < 300) {
      /**
       * http://pms.qiyi.domain/browse/GLOBALLINEDEV-4144
       * 处理不存在的老台湾站数据
       */
      const albumInfo = {
        dirArr: [],
        actorArr: [],
        actor: [],
        vipInfo: {},
        totalPageRange: []
      }
      yield put(getAlbumInfoAction(albumInfo))
      return albumInfo
    }
    return ''
  } catch (e) {
    console.log('getAlbumInfo request error' + e)
    return ''
  }
}

export function* getBodanList(id, urlParam, param) {
  try {
    const options = {
      ctx: param.ctx
    }
    const state = yield select()
    const commonParams = commonDeviceIdParams(state)
    options.params = Object.assign(commonParams, {
      pos: 0,
      num: 60
    })
    const url = BODAN_LIST + id
    const data = yield $http(url, options)
    const bdData = data.data || {}
    const epgData = bdData.epg || []
    if (data.code === '0' && epgData.length) {
      delete options.ctx
      bdData.urlParams = url + '=' + JSON.stringify(options.params)
      const langPkg = state.getIn(['language', 'langPkg']).toJS()
      const bodanData = handleBodanList(
        bdData,
        urlParam,
        commonParams,
        langPkg,
        param.ctx
      )
      return bodanData
    } else {
      yield put(getPlayStatusCodeAction(404))
      if (param) {
        param._catch.resolve(404)
      }
    }
    return {}
  } catch (e) {
    console.log('getBodanList request error' + e)
    yield put(getPlayStatusCodeAction(404))
    if (param) {
      param._catch.resolve(404)
    }
    return {}
  }
}

export function* getSimilarPlay(param) {
  try {
    const { aid, cid, filterId, ctx, isMobile, jsReq } = param
    const options = {
      ctx
    }
    const state = yield select()
    options.params = commonDeviceIdParams(state)
    options.params.pspStatus = 1
    options.params.qipuId = aid
    const time = new Date().getTime()
    options.params.sid = options.params.deviceId + '_' + time
    // options.params.vnum = 20;
    options.params.size = 30
    options.params.tab = ''
    options.params.channelId = cid
    // 应后端要求 这里改成优先 albumId
    options.params.filters = aid || filterId
    options.params.vip = 0
    const url = jsReq ? SIMILAR_PLAY_JS : SIMILAR_PLAY
    const data = yield $http(url, options)
    if (data.code === '0') {
      delete options.ctx
      data.data.lang = options.params.langCode
      data.data.urlParams = url + '=' + JSON.stringify(options.params)
      const { list, pbParam } = handleSimiplayPlayList(
        {
          data: data.data,
          mod: options.params.modeCode,
          isMobile
        },
        ctx
      )
      // 播放页为你推荐card 如果是非ssr 单独储存处理
      if (jsReq) {
        yield put(setPlayAjaxSimilarListAction({ list }))
      }
      yield put(getSimilarPlayAction({ list, pbParam }))
    }
  } catch (e) {
    console.log('getSimilarPlay request error' + e)
  }
}

export function* getPlayAdInfo(param = {}) {
  try {
    const options = {}
    const state = yield select()
    const initParams = commonDeviceIdParams(state)
    options.params = Object.assign(
      initParams,
      {
        longitude: 0,
        latitude: 0
      },
      param
    )
    const url = VIP_SHOW_AD
    const data = yield $http(url, options)
    if (data.code === '0') {
      const vdata = data.data || []
      const info = vdata[0] || {}
      const interfaceData = info.interfaceData || {}
      if (interfaceData.respCode === 'A00000') {
        const adInfo = handlePlayAdInfo(
          info.interfaceCode,
          interfaceData.respData
        )
        yield put(getPlayAdInfoAction(adInfo))
      }
    }
  } catch (e) {
    console.log('getPlayAdInfo request error' + e)
  }
}

export function* getPlayBannerInfo(param = {}) {
  try {
    const options = {}
    const state = yield select()
    const initParams = commonDeviceIdParams(state)
    options.params = Object.assign(
      initParams,
      {
        longitude: 0,
        latitude: 0
      },
      param
    )
    const url = VIP_SHOW_AD
    const data = yield $http(url, options)
    if (data.code === '0') {
      const vdata = data.data || []
      const info = vdata[0] || {}
      const interfaceData = info.interfaceData || {}
      if (interfaceData.respCode === 'A00000') {
        const bannerInfo = handlePlayBannerInfo(
          info.interfaceCode,
          interfaceData.respData
        )
        yield put(getPlayBannerInfoAction(bannerInfo))
      }
    }
  } catch (e) {
    console.log('getPlayBannerInfo request error' + e)
  }
}

export function* getVideoCollection(videoInfo, ctx) {
  try {
    const options = {
      ctx
    }
    const state = yield select()
    const commonParams = commonDeviceIdParams(state)
    options.params = Object.assign(commonParams, {
      pos: 0,
      num: 60
    })
    const url = VIDEO_FATHER_COLLECTION_QAE + videoInfo.tvId
    const data = yield $http(url, options)
    const colData = data.data || {}
    const epgData = colData.epg || []
    const pageNo = 1
    let colList = []
    if (data.code === '0' && epgData.length) {
      delete options.ctx
      colData.urlParams = url + '=' + JSON.stringify(options.params)
      colData.params = options.params
      colData.pageType = 'shortVideo'
      const langPkg = state.getIn(['language', 'langPkg']).toJS()
      const { list } = handleLaiyuanList(colData, { ctx, pkg: langPkg })
      colList = list
      const bodanName = colData.name || colData.shortName
      yield put(getBodanInfoAction({ name: bodanName, desc: colData.desc }))
    } else {
      colList.push(videoInfo)
    }
    yield put(curVideoPageNoAction({ pageNo }))
    yield put(cachePlayListAction({ pageNo, list: colList }))
  } catch (e) {
    const colList = []
    const pageNo = 1
    colList.push(videoInfo)
    yield put(curVideoPageNoAction({ pageNo }))
    yield put(cachePlayListAction({ pageNo, list: colList }))
    console.log('getBodanList request error' + e)
  }
}

export function* getShortSimilarList(param = {}) {
  try {
    const { cid, aid, blocks, filterId } = param
    const options = {}
    const state = yield select()
    options.params = commonDeviceIdParams(state)
    options.params.pspStatus = 1
    options.params.qipuId = aid
    options.params.size = 100
    options.params.vip = 0
    options.params.tab = ''
    options.params.channelId = cid
    options.params.blocks = blocks
    options.params.filters = filterId
    options.params.sid = getCookies('QC005') + Date.now()
    const similarUrl = SHORT_SIMILAR_PLAY_JS
    const data = yield $http(similarUrl, options)
    if (data.code === '0') {
      data.data.lang = options.params.langCode
      data.data.urlParams = similarUrl + '=' + JSON.stringify(options.params)
      const { list, pbParam } = handleSimiplayPlayList({
        data: data.data,
        pageType: 'playHighLight'
      })
      const videoInfo = state.getIn(['play', 'videoInfo'])
      if (
        videoInfo.get('videoType') === 'laiyuan' &&
        videoInfo.get('contentType') !== 1
      ) {
        const curId = videoInfo.get('tvId')
        const filterArr = list.filter(item => {
          return curId === item.tvId
        })
        if (filterArr.length === 0) {
          list.unshift(videoInfo)
        }
      }
      yield put(cachePlayListAction({ pageNo: 'highLight', list }))
      yield put(getShortSimilarListAction({ list, pbParam }))
    } else {
      yield put(getShortSimilarListAction({ list: [], pbParam: {} }))
    }
  } catch (e) {
    yield put(getShortSimilarListAction({ list: [], pbParam: {} }))
    console.log('getShortSimilarList request error' + e)
  }
}

export function* getCurEpgInfo(id) {
  try {
    const url = CLIENT_INTL_EPG_INFO + id
    const options = {}
    const state = yield select()
    options.params = commonDeviceIdParams(state)
    const data = yield $http(url, options)
    if (data.code === '0') {
      const langPkg = state.getIn(['language', 'langPkg']).toJS()
      const info = handleCurVideoInfo(data.data, langPkg)
      yield put(getCurVideoInfoAction(info))
    }
  } catch (e) {
    console.log('getEpgInfo request error' + e)
  }
}

export function* watchGetAvList() {
  while (true) {
    const action = yield take(FETCH_AV_LIST)
    yield getAvList(action.param)
  }
}

export function* watchGetSvList() {
  while (true) {
    const action = yield take(FETCH_SV_LIST)
    yield getSvList(action.param)
  }
}

export function* watchGetVideoInfo() {
  while (true) {
    const action = yield take(FETCH_VIDEO_INFO)
    yield getVideoInfo(action.param)
  }
}

export function* watchSimilarPlay() {
  while (true) {
    const action = yield take(FETCH_SIMILAR_PLAY)
    yield getSimilarPlay(action.param)
  }
}

export function* watchPlayAdInfo() {
  while (true) {
    const action = yield take(FETCH_PLAY_AD_INFO)
    yield getPlayAdInfo(action.param)
  }
}

export function* watchPlayBannerInfo() {
  while (true) {
    const action = yield take(FETCH_PLAY_BANNER_INFO)
    yield getPlayBannerInfo(action.param)
  }
}

export function* watchShortSimilarList() {
  while (true) {
    const action = yield take(FETCH_SHORT_SIMILAR_LIST)
    yield getShortSimilarList(action.param)
  }
}

export function* watchCurEpgInfo() {
  while (true) {
    const action = yield take(FETCH_CUR_EPG_INFO)
    yield getCurEpgInfo(action.param)
  }
}

export default [
  fork(watchGetAvList),
  fork(watchGetSvList),
  fork(watchGetVideoInfo),
  fork(watchSimilarPlay),
  fork(watchPlayAdInfo),
  fork(watchPlayBannerInfo),
  fork(watchShortSimilarList),
  fork(watchCurEpgInfo)
]
