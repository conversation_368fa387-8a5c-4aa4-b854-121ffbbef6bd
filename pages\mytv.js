// @cuibo 2024-07-11 TV收银台改版TV未登录跳转页http://pms.qiyi.domain/browse/GLOBALLINEDEV-9210
import React from 'react'
import { connect } from 'react-redux'
import DeviceCtx from '@/components/context'
import Meta from '@/components/common/Meta'
import TongJiPb from '@/components/common/TongJiPb'
import LoginTvWrapper from '@/style/loginTv'
import otpCodeLogin, { getOtpVipInfo } from '@/utils/otpCodeLogin'
import Fengkong from '@/components/common/Fengkong'
import { getCookies } from '@/kit/cookie'
import { rebuildCommonUrl } from '@/kit/common'
import { isLogin } from '@/utils/userInfo'
import UserWrap from '@/components/common/Header/userWrap'
import { serialize } from '@/kit/fetch'
import { sendBlockPb } from '@/utils/pingBack'

const reg = /^[0-9a-zA-Z]{6}$/
class LoginTv extends React.Component {
  static contextType = DeviceCtx

  constructor(props, context) {
    super(props)
    const { ptid } = context
    this.state = {
      codeValue: '',
      isError: false,
      errorMg: '',
      ptid,
      lang: getCookies('lang'),
      fkToken: '',
      dfp: '',
      isShowFk: false,
      isFocus: false
    }

    this.inputRef = React.createRef()

    this.onChange = this.onChange.bind(this)
    this.handleSubmit = this.handleSubmit.bind(this)
    this.verifySliderSus = this.verifySliderSus.bind(this)
    this.setError = this.setError.bind(this)
    this.bindLoginRef = this.bindLoginRef.bind(this)
  }

  static async getInitialProps({ ctx }) {
    const { query } = ctx
    const scanToken = query.token || ''
    return { scanToken }
  }

  async componentDidMount() {
    if (isLogin()) {
      await this.loginRef.handleUserLogout()
    }
    const { scanToken } = this.props
    if (scanToken) {
      window.location.href = rebuildCommonUrl(
        `login?otp=1&is_login_page=1&token=${scanToken}&mytv=true`
      )
    }
  }

  onFocus() {
    this.setState({
      isFocus: true,
      isError: false,
      errorMg: ''
    })
  }

  onBlur() {
    const { langPkg } = this.props
    const { codeValue } = this.state
    const trueLangPkg = langPkg.toJS()

    if (codeValue.length < 6 || !reg.test(codeValue)) {
      this.setState({
        isError: true,
        isFocus: false,
        errorMg: trueLangPkg['PCW_FRONTEND_1692865648171_119']
      })
    } else {
      this.setState({
        isError: false,
        isFocus: false
      })
    }
  }

  setError(errorMg, code) {
    this.setState({
      isError: true,
      errorMg
    })
    code &&
      sendBlockPb(code, {
        rpage: 'tv_login_no_H5_otp'
      })
  }

  async handleSubmit({ fkToken }) {
    const { codeValue, ptid } = this.state
    const { langPkg, netErrorLangPkg } = this.props
    const trueLangPkg = langPkg.toJS()

    if (!reg.test(codeValue)) {
      this.setState({
        isError: true,
        errorMg: trueLangPkg['PCW_FRONTEND_1692865648171_119']
      })
      return
    }

    try {
      const res = await otpCodeLogin({
        code: codeValue,
        ptid,
        fkToken
      })

      if (res.code === 'A00000') {
        // 走不到这里，产品逻辑是先退登，输入key之后未登录会跳到登录页,以防万一写上处理代码
        const vipInfo = await getOtpVipInfo({ extraKey: codeValue })
        if (vipInfo) {
          window.location.href = rebuildCommonUrl(
            `vip/order?mytv=true&key=${vipInfo?.key}&${
              vipInfo?.extraInfo ? serialize(JSON.parse(vipInfo.extraInfo)) : ''
            }&H5=true`
          )
        } else {
          this.setError(trueLangPkg['webtv_cashier_otp_keyNotValid'], 'A00000')
        }
      } else if (res.code === 'A00001') {
        // 未登录
        window.location.href = rebuildCommonUrl(
          `login?otp=1&is_login_page=1&code=${codeValue}&mytv=true`
        )
      } else if (res.code === 'P01012') {
        // 激活码不存在
        this.setError(trueLangPkg['PCW_FRONTEND_1692784705964_916'], res.code)
      } else if (res.code === 'P01009') {
        // 激活码已使用
        this.setError(trueLangPkg['PCW_FRONTEND_1692784866490_756'], res.code)
      } else if (res.code === 'P01010') {
        // 激活码已过期
        this.setError(trueLangPkg['PCW_FRONTEND_1692784866490_756'], res.code)
      } else if (res.code === 'P00159') {
        // 高危
        this.setError(trueLangPkg['PCW_FRONTEND_1692866013950_125'], res.code)
      } else if (res.code === 'P00223') {
        // 中低
        this.setState({
          isShowFk: true,
          fkToken: res.data.token
        })
      } else {
        this.setError(netErrorLangPkg, res.code)
      }
    } catch (err) {
      this.setState({
        isError: true,
        errorMg: netErrorLangPkg
      })
    }
  }

  onChange(e) {
    const key = e.target.value.trim()
    // const { codeValue } = this.state

    this.setState({
      codeValue: key
    })
  }

  verifySliderSus() {
    this.handleSubmit({
      fkToken: this.state.fkToken
    })

    this.setState({
      isShowFk: false
    })
  }

  bindLoginRef(ref) {
    this.loginRef = ref
  }

  render() {
    const { langPkg, scanToken } = this.props
    const {
      codeValue,
      isError,
      errorMg,
      lang,
      fkToken,
      dfp,
      isShowFk,
      isFocus
    } = this.state
    const trueLangPkg = langPkg.toJS()

    return (
      <>
        <Meta
          desc={trueLangPkg.home_html_description}
          title={trueLangPkg.home_html_title}
        />
        <div className="user-wrapper">
          <UserWrap isVipButton="true" onRef={this.bindLoginRef} />
        </div>
        <LoginTvWrapper>
          <div
            style={{
              position: 'relative',
              background: '#fff',
              minHeight: '600px'
            }}
          >
            {scanToken ? (
              <></>
            ) : (
              <div className="section1">
                <div className="desc">
                  {trueLangPkg?.PCW_FRONTEND_1692079999407_573}
                </div>
                <div
                  className={`${isError ? 'error' : ''} ${
                    isFocus ? 'focus' : ''
                  } input-box`}
                >
                  <input
                    className="input"
                    ref={this.inputRef}
                    autoComplete="off"
                    onChange={this.onChange}
                    onFocus={e => this.onFocus(e)}
                    onBlur={e => this.onBlur(e)}
                    type="text"
                    value={codeValue}
                    maxLength="6"
                  />
                  <div className="tip">
                    {trueLangPkg?.PCW_FRONTEND_1692080042095_425}
                  </div>
                </div>
                <div
                  className="error-tip"
                  style={{ display: `${isError ? 'block' : 'none'}` }}
                >
                  {errorMg}
                </div>
                <div
                  className="button"
                  onClick={this.handleSubmit}
                  role="button"
                  tabIndex="0"
                  rseat="click"
                  data-pb="block=continue&rpage=tv_login_no_H5_otp"
                >
                  {trueLangPkg?.PCW_FRONTEND_1692080076568_375}
                </div>
              </div>
            )}
            {isShowFk && (
              <div className="ibd-fengkong-wrapper">
                <div className="ibd-fengkong-outer">
                  <div id="sign_fengkong" />
                  <Fengkong
                    id="sign_fengkong"
                    dfp={dfp}
                    lang={lang}
                    token={fkToken}
                    verifySliderSus={this.verifySliderSus}
                  />
                </div>
              </div>
            )}
          </div>
        </LoginTvWrapper>
        <TongJiPb rpage="tv_login_no_H5_otp" />
        <>
          <script
            type="text/javascript"
            src="//security.iq.com/static/iq/v2/verifycenter/js/verifycenter.js"
            async
          />
          <link
            rel="stylesheet"
            type="text/css"
            async
            href="//security.iq.com/static/iq/v2/verifycenter/css/verifycenter.css"
          />
        </>
      </>
    )
  }
}

const mapStateToProps = state => ({
  langPkg: state.getIn(['language', 'langPkg']),
  netErrorLangPkg: state.getIn([
    'vip',
    'cashierLangPkg',
    'PCW_CASHIER_1650957546922_650'
  ]),
  userInfo: state.getIn(['user', 'userInfo']),
  langs: state.getIn(['language', 'modeLangObj'])
})

export default connect(mapStateToProps)(LoginTv)
