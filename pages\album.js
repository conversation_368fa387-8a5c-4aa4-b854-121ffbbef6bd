import React from 'react'
import { connect } from 'react-redux'
import dynamic from 'next/dynamic'
import {
  fetchAlbumInfoAction
  //   getAlbumStatusCodeAction
} from '@/store/reducers/album/album'
import { GET_SUBTITLE_LANG, LANG_PKG_WEB } from '@/constants/interfaces'
import $http from '@/kit/fetch'
import { getCookies, getCtxDevice } from '@/kit/cookie'
import { commonDeviceIdParams, platformId } from '@/kit/common'
import AlbumWrapper from '@/style/albumStyle'
// import Footer from '@/components/common/Footer'
import Focus from '@/components/pages/album/focus/'
import FocusInfo from '@/components/pages/album/focus/info'
import Tab from '@/components/pages/album/tab'
import { getViewWidth } from '@/kit/dom'
import { getLangPkgAction } from '@/store/reducers/language/language'
import AlbumMeta from '@/components/pages/album/meta'
import TongJiPb from '@/components/common/TongJiPb'

import DeviceCtx from '@/components/context'
import HomeSchema from '@/components/pages/home/<USER>'
import OrganizationSchema from '@/components/pages/home/<USER>'
import Schema from '@/components/pages/album/schema/index'
// import ItemList from '@/components/pages/album/schema/itemList'
import GPTADTabBanner from '@/components/common/GPTADTabBanner'
import { cdnFrom, getS2S3S4, removeS2S3S4 } from '@/utils/common'
import { checkQipuIdStr } from '@/kit/url'
import { fetchConfigAction } from '@/store/reducers/commonConfig/commonConfig'
import Error from './_error'

const Footer = dynamic(() => import('@/components/common/Footer'), {
  ssr: false
})

class Album extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      fixTop: false,
      tabScrollY: 0,
      shortUrl: 'https://iqiyi.cn/cnqka' // 给一个默认值 预防接口失败
      //   isMobile: getDevice() === 'mobile',
      //   deviceType: getMobileType()
    }
    this.handleScroll = this.handleScroll.bind(this)
  }

  componentDidMount() {
    const { dispatch } = this.props
    dispatch(fetchConfigAction({ pageSt: 'album_web' }))
    const getLangPkg = async () => {
      const options = {}
      options.params = {
        langCode: this.props.modeLangObj.get('lang'),
        platformId: platformId()
      }
      const pkgData = await $http(LANG_PKG_WEB, options)
      this.props.dispatch(getLangPkgAction(pkgData.data))
    }
    // console.log('getLangPkg-->')
    getLangPkg()
    window.addEventListener('scroll', this.handleScroll)
    const sObj = getS2S3S4()
    window.albumS2 = sObj.s2
    window.albumS3 = sObj.s3
    window.albumS4 = sObj.s4
    removeS2S3S4()
    this.handleScroll()
    // this.getShortUrl()
  }

  //  static async getShortUrl() {

  //   }

  static async getInitialProps({ ctx }) {
    const { query, store, isServer, res } = ctx
    const { id, from, _from } = query
    const isMobile = getCtxDevice(ctx) === 'mobile'
    const curFrom = cdnFrom(_from, from)
    const curUrl = ctx.req.url.split('?')[0]
    let subtitleInfo = ''
    const headers = ctx.req.headers
    let isSeo = false
    if (headers['is-robot-query']) {
      // 增加对seo特殊处理逻辑
      isSeo = true
    }
    const mod = getCookies('mod', ctx) || 'intl'
    if (!id) {
      // await store.dispatch(getALbumStatusCodeAction(404))
      // res.setHeader('x-page-type', 'not-found')
      res.writeHead(302, {
        Location: `https://www.iq.com?source=album${curFrom}`
      })
      res.end()
    } else {
      const albumInfo = await store.dispatch(
        fetchAlbumInfoAction({
          id,
          isMobile,
          isSupportSEO: true,
          ctx,
          _catch: {}
        })
      )

      if (albumInfo && typeof albumInfo === 'number') {
        // res.setHeader('x-page-type', 'not-found')
        res.writeHead(302, {
          Location: `https://www.iq.com?source=album${curFrom}`
        })
        res.end()
      } else if (
        albumInfo &&
        typeof albumInfo?.fatherEpisodeIdStr === 'string' &&
        checkQipuIdStr(albumInfo?.fatherEpisodeIdStr || '')
      ) {
        res.writeHead(302, {
          Location: `https://www.iq.com/album/${albumInfo?.directLocSuffix ||
            albumInfo?.fatherEpisodeIdStr}`
        })
        res.end()
      }
      const params = {
        ...commonDeviceIdParams(),
        langCode: getCookies('lang', ctx) || 'en_us',
        modeCode: mod
      }
      const options = { params }
      options.ctx = ctx
      const tvId = albumInfo.tvId
      if (tvId) {
        const resLangList = await $http(GET_SUBTITLE_LANG + '/' + tvId, options)
        if (resLangList.code === '0') {
          const list = resLangList.data.subtitleLanguageDTOS.reduce(
            (subtitles, item) => {
              if (item.subtitle) return [...subtitles, item.subtitle]
              return subtitles
            },
            []
          )
          subtitleInfo = list.join(',')
        }
      }
    }
    return { isSeo, isServer, curUrl, subtitleInfo, mod, isMobile }
  }

  handleScroll() {
    const scrollTop =
      document.documentElement.scrollTop ||
      window.pageYOffset ||
      document.body.scrollTop ||
      -parseInt(document.body.style.top, 10) ||
      0
    const viewWidth = getViewWidth()
    if (viewWidth >= 1024) {
      return
    }
    let topHeight = 0
    let tabScrollY = 0
    if (this.albumDom && this.albumDom.children.length) {
      topHeight = this.albumDom.children[0].clientHeight
      tabScrollY = topHeight
    }
    if (this.contentDom && this.contentDom.children.length) {
      const contentHeight = this.contentDom.children[0].clientHeight
      const contentTop = this.contentDom.offsetTop
      const diffNum = topHeight - contentTop
      topHeight += contentHeight
      if (diffNum > 0) {
        tabScrollY = topHeight - diffNum
      } else {
        tabScrollY = topHeight
      }
    }
    // let topHeight = this.focusDom ? this.focusDom.style.height : 0
    // topHeight += this.focusInfoDom ? this.focusInfoDom.style.height : 0
    if (scrollTop > topHeight) {
      this.setState({
        fixTop: true,
        tabScrollY
      })
    } else {
      this.setState({
        fixTop: false,
        tabScrollY: 0
      })
    }
  }

  render() {
    const {
      albumStatusCode,
      albumInfo,
      videoInfo,
      langPkg,
      modeLangObj,
      curAlbumPageInfo,
      cacheAlbumList,
      albumScoreInfo,
      curUrl,
      subtitleInfo,
      urlAlbumTitle,
      mod,
      reason404,
      isMobile,
      isSeo,
      langCode
    } = this.props
    const { cookieSetHandler } = this.context
    const { fixTop, tabScrollY, shortUrl } = this.state
    const langPkgData = langPkg.toJS()
    const qipuId = albumInfo.get('qipuId')
    let albumPageWrapper
    if (albumStatusCode === 404) {
      albumPageWrapper = (
        <Error statusCode={albumStatusCode} reason404={reason404} />
      )
    } else {
      albumPageWrapper = (
        <>
          <AlbumMeta curUrl={curUrl} urlAlbumTitle={urlAlbumTitle} mod={mod} />
          <OrganizationSchema langCode={langCode} />
          <HomeSchema langCode={langCode} />
          <Schema
            albumInfo={albumInfo}
            videoInfo={videoInfo}
            langPkg={langPkgData}
            albumScoreInfo={albumScoreInfo}
            mod={modeLangObj.get('mod')}
            subtitleInfo={subtitleInfo}
          />
          <AlbumWrapper
            ref={dom => {
              this.albumDom = dom
            }}
          >
            <Focus
              albumInfo={albumInfo}
              isMobile={isMobile}
              langPkg={langPkg}
            />
            <div
              className="contentWrapper"
              ref={dom => {
                this.contentDom = dom
              }}
            >
              <FocusInfo
                albumInfo={albumInfo}
                langPkg={langPkg}
                shortUrl={shortUrl}
                isMobile={isMobile}
                modeLangObj={modeLangObj}
              />
              <Tab
                albumInfo={albumInfo}
                videoInfo={videoInfo}
                langPkg={langPkg}
                curAlbumPageInfo={curAlbumPageInfo}
                cacheAlbumList={cacheAlbumList}
                fixTop={fixTop}
                tabScrollY={tabScrollY}
                isSeo={isSeo}
              />
            </div>
            <GPTADTabBanner rpage="album" adNum="1" gptAdType="intro-banner" />
            <TongJiPb rpage="album" pbShowParams={{ rId: qipuId }} />
            {/*            <ItemList
              albumInfo={albumInfo}
              curAlbumPageInfo={curAlbumPageInfo}
              cacheAlbumList={cacheAlbumList}
            /> */}
          </AlbumWrapper>
        </>
      )
    }
    return (
      <>
        {albumPageWrapper}
        {<Footer cookieSetHandler={cookieSetHandler}/>}
      </>
    )
  }
}
Album.contextType = DeviceCtx
const mapStateToProps = state => ({
  langPkg: state.getIn(['language', 'langPkg']),
  modeLangObj: state.getIn(['language', 'modeLangObj']),
  albumInfo: state.getIn(['album', 'videoAlbumInfo']),
  videoInfo: state.getIn(['album', 'defaultVideoInfo']),
  cacheAlbumList: state.getIn(['album', 'cacheAlbumList']),
  curAlbumPageInfo: state.getIn(['album', 'curAlbumPageInfo']),
  albumStatusCode: state.getIn(['album', 'albumStatusCode']),
  reason404: state.getIn(['album', 'albumReason404']),
  albumScoreInfo: state.getIn(['album', 'albumScoreInfo'])
})

export default connect(mapStateToProps)(Album)
