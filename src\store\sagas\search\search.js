import { put, select, fork, take } from 'redux-saga/effects'
import $http from '@/kit/fetch'
import { commonDeviceIdParams, platformId } from '@/kit/common'
import { getDevice } from '@/kit/device'
import {
  FETCH_SEARCH_RESULT,
  getSearchResultAction,
  FETCH_HOT_SEARCH,
  getHotSearchAction,
  FETCH_INTENT_RESULT,
  getIntentSearchAction
} from '@/store/reducers/search/search'
import {
  SEARCH_URL,
  SEARCH_QAE_URL,
  HOT_SEARCH_URL
} from '@/constants/interfaces'
import {
  handleSearchResutlData,
  handleHotSearchData,
  handleSearchIntentEpg
} from './handleData'
import { getIsVipSSR } from '../user/user'

let total // 在此定义是因为：后端接口只有第一次返回的total才是准确的

function* fetchSearchData(param = {}) {
  const options = {}
  const url = param.ctx ? SEARCH_QAE_URL : SEARCH_URL
  const state = yield select()
  const initParams = commonDeviceIdParams(state)
  // const isMobile = getDevice() === 'mobile'
  const isVip = yield getIsVipSSR({
    param: { fields: 'userinfo' },
    ctx: param.ctx
  })
  options.params = Object.assign(
    {
      pn: state.getIn(['search', 'result', 'pn']),
      ps: 30,
      // ps: isMobile ? 10 : 30,
      intentPn: 1,
      intentPs: 20,
      chnId: 0,
      dataType: '1;should,2;should',
      platformId: platformId(),
      modeCode: initParams.modeCode,
      langCode: initParams.langCode,
      deviceId: initParams.deviceId,
      isVip
      // vip: 'vip'
    },
    param
  )
  return yield $http(url, options)
  // }catch(e){
  //   console.log(e)
  // }
}

export function* getSearchResult(param = {}) {
  try {
    // const options = {}
    // const url = SEARCH_URL
    const state = yield select()
    const initParams = commonDeviceIdParams(state)
    const isMobile = getDevice() === 'mobile'

    // options.params = Object.assign(
    //   {
    //     pn: 1,
    //     ps: 20,
    //     intentPn:1,
    //     intentPs:20,
    //     chnId: 0,
    //     dataType: '1;should,2;should',
    //     platformId: platformId(),
    //     modeCode: initParams.modeCode,
    //     langCode: initParams.langCode,
    //     deviceId: initParams.deviceId
    //     // vip: 'vip'
    //   },
    //   param
    // )
    // const data = yield $http(url, options)
    const data = yield fetchSearchData(param)
    if (data.code === '0') {
      const newData = handleSearchResutlData(
        data.data,
        param.key,
        initParams.langCode,
        initParams.modeCode
      )
      if (param.pn === 1) {
        // 首次请求接口，带上意图识别，之后翻页就各走各的逻辑
        const mod = initParams.modeCode
        const _data = handleSearchIntentEpg(data.data, { mod, ctx: param.ctx })
        newData['intentEpg'] = {
          intentList: _data,
          pingback: {
            rpage: 'search_rst',
            block: 'search_intent_recognize',
            position: data.data?.starsInfo?.qipuId ? 2 : 1
          }
        }
      }
      total = total === undefined ? newData.total : total
      // newData['hasMore'] =
      //   param.pn === 1 ? newData.videos.length >= 4 : newData.videos.length > 4
      newData['hasMore'] = newData.videos.length >= (isMobile ? 1 : 4)

      newData['pn'] = newData.pn
      // newData['intentPn'] = newData.intentPn
      newData['total'] = total
      yield put(getSearchResultAction(newData))
    }
  } catch (e) {
    console.log('request error')
    console.log(e)
  }
}

export function* getIntentData(param = {}) {
  try {
    const data = yield fetchSearchData(param.params)
    if (data.code === '0') {
      const mod = param.params.modeCode
      const _data = handleSearchIntentEpg(data.data, { mod })
      param._catch.resolve(_data)
      yield put(
        getIntentSearchAction({
          intentEpg: {
            intentList: _data,
            intentPn: param.params.intentPn
          }
        })
      )
    }
  } catch (e) {
    console.log('getIntentData* error:', e)
    param._catch.resolve([])
  }
  param._catch.resolve([])
}

export function* getHotSearch(param = {}) {
  try {
    const options = {}
    const isMobile = param.params.isMobile
    delete param.params.isMobile
    const url = HOT_SEARCH_URL
    const state = yield select()
    const initParams = commonDeviceIdParams(state)
    options.params = Object.assign(
      {
        platformId: platformId(),
        modeCode: initParams.modeCode,
        langCode: initParams.langCode,
        deviceId: initParams.deviceId
      },
      param.params
    )
    const data = yield $http(url, options)

    if (data.code === '0') {
      const newData = handleHotSearchData(data.data, {
        lang: initParams.langCode,
        mod: initParams.modeCode,
        isMobile
      })
      yield put(getHotSearchAction(newData))
    }
    param._catch.resolve(data)
  } catch (e) {
    param._catch.resolve({})
    console.log('request error')
    console.log(e)
  }
}

export function* watchGetSearchResult() {
  while (true) {
    const action = yield take(FETCH_SEARCH_RESULT)
    yield getSearchResult(action.param)
  }
}

export function* watchGetHotSearch() {
  while (true) {
    const action = yield take(FETCH_HOT_SEARCH)
    yield getHotSearch(action.param)
  }
}

export function* watchGetIntentData() {
  while (true) {
    const action = yield take(FETCH_INTENT_RESULT)
    yield getIntentData(action.param)
  }
}

export default [
  fork(watchGetSearchResult),
  fork(watchGetHotSearch),
  fork(watchGetIntentData)
]
