/* eslint-disable no-undef */
import App from 'next/app'
import React from 'react'
import { fromJS } from 'immutable'
//  import withReduxStore from "../store/with-redux-store";
import withRedux from 'next-redux-wrapper'
import withReduxSaga from 'next-redux-saga'
import { Provider } from 'react-redux'
import { getDevice, getMobileType } from '@/kit/device'
import DeviceCtx from '@/components/context'
import createStore from '@/store'
import Layout from '@/components/common/Layout'
import {
  fetchModeLangListAction,
  fetchVipModeLangListAction,
  fetchCashierModeLangListAction,
  fetchPrivilegeModeLangListAction
} from '@/store/reducers/language/language'
import { getParentalListAction } from '@/store/reducers/user/user'
import $http, { serialize } from '@/kit/fetch'
import {
  platformId,
  commonDeviceIdParams,
  iqSwitchPlatformId,
  // deleteAbtestFromCookie,
  getAbtestType
} from '@/kit/common'
import {
  MODE_LANG,
  MODE_PTID,
  GDPR_FETCH,
  GDPR_UID_FETCH,
  GDPR_SAVE,
  FUNCS_API_QAE
} from '@/constants/interfaces'
import { getCookies, setCookies, getCookie005Val } from '@/kit/cookie'
import { getUid } from '@/utils/userInfo'
import {
  getTabInfo
  // getBatchResInfo
} from '@/utils/apis'
import LayoutComponents from '@/components/common/LayoutComponents'
import { fetchFocusInfoAction } from '@/store/reducers/filmLibrary'
import {
  fetchConfigAction,
  fetchGenericMarkAction
} from '@/store/reducers/commonConfig/commonConfig'
import { setFeatureSwitchAction } from '@/store/reducers/home/<USER>'
// import { getTabCurInfoData } from '@/store/reducers/home/<USER>'

class MyApp extends App {
  constructor(props) {
    super(props)
    this.state = {
      isMobile: getDevice() === 'mobile',
      cookiePopupVisible: false,
      i18nShowCoupon: true,
      installpromptEvent: null
    }
    this.cookieSetting = false
  }

  async componentDidCatch(error) {
    const pingbackSrc = `//msg-intl.qy.net/qos?p1=1_10_222&t=9&ct=crash&u=${getCookies(
      'QC005'
    )}&mod=${getCookies('mod')}&lang=${getCookies('lang')}&err_msg=${
      error.stack
    }`
    const qosImg = document.createElement('img')
    qosImg.src = pingbackSrc
    document.body.appendChild(qosImg)
    qosImg.style.display = 'none'
  }

  async componentDidMount() {
    const { pageProps } = this.props
    const { pathname } = pageProps
    if (pathname !== '/tv-register') {
      // this.delUrlQuery()
      this.registSW()
      const isMobile = getDevice() === 'mobile'
      this.setState({
        isMobile
      })
      window.enterPageTime = Math.round(Date.now() / 1000) // 进入页面的具体时间
      // setCookies('mod', modeCode, {
      //   time: 60 * 60 * 24 * 365
      // })
      // setCookies('lang', langCode, {
      //   time: 60 * 60 * 24 * 365
      // })

      window.addEventListener('resize', () => {
        const { isMobile } = this.state
        const newIsMobile = getDevice() === 'mobile'
        if (isMobile !== newIsMobile) {
          this.setState({
            isMobile: newIsMobile
          })
        }
      })

      // The perfMetrics object is created by the code that goes in <head>.
      // eslint-disable-next-line
      window.perfMetrics &&
        window.perfMetrics.onFirstInputDelay(function ga(delay, evt) {
          if (window.ga) {
            window.ga('send', 'event', {
              eventCategory: 'Perf Metrics',
              eventAction: 'first-input-delay',
              eventLabel: evt.type,
              // Event values must be an integer.
              eventValue: Math.round(delay),
              // Exclude this event from bounce rate calculations.
              nonInteraction: true
            })
          }
        })
      // 功能配置
      let gdprCookie
      let i18nShowCoupon = true
      const funcsOptions = {}
      funcsOptions.params = {
        platform_id: iqSwitchPlatformId(),
        app_v: '1.0.0',
        lang: this.props.pageProps.langCode,
        app_lm: this.props.pageProps.modeCode
      }
      try {
        const funcs = this.props.pageProps.featureSwitchData
        if (funcs) {
          gdprCookie = funcs.gdpr_cookie
          i18nShowCoupon = funcs.i18n_switch_cashier_show_coupon
          this.setState({ i18nShowCoupon })
        }
      } catch (e) {
        console.log(e)
      }
      // 合规数据
      let funcCookieEnable = true
      let performCookieEnable = true
      let adCookieEnable = true
      // let comscoreCookieEnable = true
      const paths = ['/tv-register', '/share-short', '/shortMiddle'] // 无需合规弹窗的页面
      if (!paths.includes(pathname)) {
        this.addGtmCode(this.props.pageProps.modeCode)
      }
      if (gdprCookie && !paths.includes(pathname)) {
        funcCookieEnable = false
        performCookieEnable = false
        adCookieEnable = false
        const tm = new Date().getTime()
        const gdprOptions = {
          params: {
            deviceId: commonDeviceIdParams().deviceId,
            timestamp: tm
          }
        }
        const qdsf = require('@iqiyi/qdsf')

        let gdprData
        const uid = getUid()
        if (uid) {
          gdprOptions.params.uid = uid
          gdprOptions.headers = {
            qdsf: qdsf.qdsf(0, serialize(gdprOptions.params), tm / 1000)
          }
          gdprData = await $http(GDPR_UID_FETCH, gdprOptions)
          let cookiestr = ''
          if (gdprData && (gdprData.code === 0 || gdprData.code === '0')) {
            cookiestr = gdprData.data.cookie
          }

          if (!cookiestr && pathname !== '/tv-register') {
            const cookieslocal = localStorage.getItem('iqcookiesetting')
            if (cookieslocal) {
              /** 主动保存设置 */
              gdprData = {
                code: 0,
                data: {
                  cookie: cookieslocal
                }
              }
              console.log('iqcookiesetting-->', cookieslocal)
              this.saveCookieSetting(cookieslocal)
            } else {
              localStorage.removeItem('iqcookiesetting')
            }
          }
        } else {
          gdprOptions.params.platform = 'pcw'
          gdprOptions.headers = {
            qdsf: qdsf.qdsf(0, serialize(gdprOptions.params), tm / 1000)
          }
          gdprData = await $http(GDPR_FETCH, gdprOptions)
        }
        let cookie
        if (gdprData.code === 0 || gdprData.code === '0') {
          cookie = gdprData.data.cookie
          // us 網頁端CCPA&GPC合規 有可能修改cookie数据
          const localkey = gdprOptions.params.deviceId
          // gdprOptions.params.uid + '-' + gdprOptions.params.deviceId //因后端接口以deviceId为主存储 前端暂时不用uid+deviceId
          const newstr = await this.checkShareCookie(cookie, localkey)
          console.log(`${cookie} -> ${newstr}`)
          cookie = newstr
          // 最终数据存一下 为了开cookie设置时展示准确
          if (cookie) {
            localStorage.setItem('iqcookiesetting', cookie)
          }
          if (!cookie && pathname !== '/tv-register') {
            this.setState({ cookiePopupVisible: true })
          } else {
            funcCookieEnable = cookie?.indexOf('1') >= 0
            performCookieEnable = cookie?.indexOf('2') >= 0
            adCookieEnable = cookie?.indexOf('3') >= 0
          }
        }
      }
      // 合规数据处理逻辑
      if (!funcCookieEnable) {
        window.funcCookieDisable = !funcCookieEnable
        document.cookie =
          'QC173=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.iq.com;'
        document.cookie =
          'QC010=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.iq.com;'
        document.cookie =
          'QC008=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.iq.com;'
        document.cookie =
          'QC007=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.iq.com;'
        document.cookie =
          'QC006=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.iq.com;'
        // document.cookie =
        //   'QC005=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.iq.com;'
      }
      if (!performCookieEnable) {
        const script = document.createElement('script')
        script.id = 'perform'
        script.text = `window['ga-disable-MEASUREMENT_ID'] = true;
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date()); 
      gtag('config', 'MEASUREMENT_ID');
      `
        document.body.appendChild(script)
        this.addScriptCode(`
        // Define dataLayer and the gtag function.
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}

        // Set default consent to 'denied' as a placeholder
        // Determine actual values based on your own requirements
        gtag('consent', 'default', {
          'ad_storage': 'denied',
          'ad_user_data': 'denied',
          'ad_personalization': 'denied',
          'analytics_storage': 'denied',
          'wait_for_update': 500,
        });
      `)
        this.addAdJs('https://www.googletagmanager.com/gtag/js?id=G-VTJGB1PRBJ')
        this.addAdJs('https://www.googletagmanager.com/gtag/js?id=G-PCTZRE9688')
        this.addAdJs(
          'https://www.googletagmanager.com/gtag/js?id=UA-145147433-3'
        )
        this.addScriptCode(`
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-PCTZRE9688');
        gtag('config', 'G-VTJGB1PRBJ');
        gtag('config', 'UA-145147433-3', { 
          'transport_type': 'image',
          'custom_map': {
              'dimension1': 'mod',
              'dimension2': 'lang',
              'dimension3': 'pagename'
          }
        });
        function getCookie(name){
          var arr,reg=new RegExp("(^| )"+name+"=([^;]*)(;|$)");
          if(arr=document.cookie.match(reg)) 
          return unescape(arr[2]);
          else 
          return null;
        }
        var info = getCookie('I00002');
        var uid = null;
        if(info) uid = JSON.parse(info).data.uid;
        uid && gtag('set', {'user_id': uid});
      `)
        const script1 = document.createElement('script')
        // 徐晓婉需求******************
        script1.id = 'GAM'
        script1.src =
          'https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js'
        script1.setAttribute('data-ad-client', 'ca-pub-****************')
        script1.async = true
        document.getElementsByTagName('head')[0].appendChild(script1)
      } else {
        this.addGACode()
      }
      if (adCookieEnable) {
        setCookies('adcookie', 1)
        this.addScriptCode(`
        (adsbygoogle=window.adsbygoogle||[]).requestNonPersonalizedAds=0
        `)
      } else {
        this.addScriptCode(`
        (adsbygoogle=window.adsbygoogle||[]).requestNonPersonalizedAds=1
           window.googletag = window.googletag || { cmd: [] }
      // eslint-disable-next-line no-undef, func-names
      googletag.cmd.push(function() {
        // eslint-disable-next-line no-undef
        googletag.pubads().setPrivacySettings({
          restrictDataProcessing: true,
          nonPersonalizedAds: true
        })
      })
        `)
      }
      if (!paths.includes(pathname)) {
        this.addAw()
      }
    }
  }

  /**
   * 網頁端CCPA&GPC合規仅us 有可能修改cookie数据
   * 如果分析類或廣告類Cookies保存同意 且 GPC不允许 需访问接口修改数据
   * @param {*} cookiestr
   * @returns
   */
  async checkShareCookie(cookiestr, localkey) {
    let newcookie = cookiestr

    // true为用户设置了GPC不允许使用个人信息
    if (cookiestr && getCookies('mod') === 'us') {
      // gptInvalid:1表示用户操作过cookie了即使有gpc信号也失效 以用户操作为先
      const gptInvalid =
        window.localStorage && window.localStorage.getItem('gptInvalid')
          ? window.localStorage.getItem('gptInvalid').split(',')
          : []
      const currentIndex = gptInvalid.indexOf(localkey)
      if (
        navigator.globalPrivacyControl &&
        currentIndex === -1 &&
        (cookiestr.includes('2') || cookiestr.includes('3'))
      ) {
        // 分析類或廣告類如果有同意需清掉
        const cookie = cookiestr.includes('1') ? '1' : '' // 只保留第一个
        if (cookie) {
          const result = await this.saveCookieSetting(cookie)
          if (result) {
            newcookie = cookie
            localStorage.setItem('iqcookiesetting', cookie)
          }
        }
      }
      // 用户选择过cookie gpc关闭信号了 需清掉gptInvalid记录 为下一次gpc做准备
      if (!navigator.globalPrivacyControl && currentIndex > -1) {
        gptInvalid.splice(currentIndex, 1)
        window.localStorage.setItem('gptInvalid', gptInvalid.join(','))
      }
    }
    return newcookie
  }

  saveCookieSetting = async cookie => {
    const tm = new Date().getTime()
    const options = {
      params: {
        cookie,
        app_v: '1',
        uid: getUid(),
        qyid: commonDeviceIdParams().deviceId,
        platform: 'pcw',
        timestamp: tm
      }
    }
    const qdsf = require('@iqiyi/qdsf')
    options.headers = {
      qdsf: qdsf.qdsf(0, serialize(options.params), tm / 1000)
    }
    try {
      const res = await $http(GDPR_SAVE, options)
      if (res && res.code === 0) {
        console.log('保存成功')
        return true
      } else {
        console.log('network error')
        return false
      }
    } catch (e) {
      console.error(e)
      return false
    }
  }

  registSW = () => {
    // 注册 service worker
    import('@/../serviceWorkerRegistration').then(sw => {
      // sw.register(event => {
      //   this.setState({ installpromptEvent: event })
      // })
      // 卸载
      sw.unregister()
    })
  }

  cookieSetHandler = show => {
    this.cookieSetting = show
    this.setState({ cookiePopupVisible: show })
  }

  addAw = () => {
    const script = document.createElement('script')
    script.id = 'perform'
    script.text = `
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date()); 
    gtag('config', 'AW-339868652');
    `
    document.body.appendChild(script)
  }

  updateGACode = (adCookie, gaCookie) => {
    localStorage.setItem('consentGranted', 'true')
    // eslint-disable-next-line no-undef, prefer-rest-params
    function gtag() {
      // eslint-disable-next-line prefer-rest-params
      dataLayer.push(arguments)
    }
    const gtagData = {}
    if (adCookie) {
      gtagData['ad_personalization'] = 'granted'
    }
    if (gaCookie) {
      gtagData['ad_user_data'] = 'granted'
      gtagData['ad_storage'] = 'granted'
      gtagData['analytics_storage'] = 'granted'
    }
    gtag('consent', 'update', gtagData)
  }

  addGACode = () => {
    this.addAdJs('https://www.googletagmanager.com/gtag/js?id=G-VTJGB1PRBJ')
    this.addAdJs('https://www.googletagmanager.com/gtag/js?id=G-PCTZRE9688')
    this.addAdJs('https://www.googletagmanager.com/gtag/js?id=UA-145147433-3')
    this.addScriptCode(`
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-PCTZRE9688');
      gtag('config', 'G-VTJGB1PRBJ');

      gtag('config', 'UA-145147433-3', { 
        'transport_type': 'image',
        'custom_map': {
            'dimension1': 'mod',
            'dimension2': 'lang',
            'dimension3': 'pagename'
        }
      });
      function getCookie(name){
        var arr,reg=new RegExp("(^| )"+name+"=([^;]*)(;|$)");
        if(arr=document.cookie.match(reg)) 
        return unescape(arr[2]);
        else 
        return null;
      }
      var info = getCookie('I00002');
      var uid = null;
      if(info) uid = JSON.parse(info).data.uid;
      uid && gtag('set', {'user_id': uid});
    `)

    const script = document.createElement('script')
    // 徐晓婉需求******************
    script.id = 'GAM'
    script.src =
      'https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js'
    script.setAttribute('data-ad-client', 'ca-pub-****************')
    script.async = true
    document.getElementsByTagName('head')[0].appendChild(script)
  }

  addGtmCode = () => {
    this.addAdJs('//imasdk.googleapis.com/js/sdkloader/ima3.js')
    this.addAdJs(
      '//securepubads.g.doubleclick.net/tag/js/gpt.js?network-code=***********'
    )
    this.addAdJs('//accounts.google.com/gsi/client')
    let firebaseConfigStr = `var firebaseConfig = {apiKey: "AIzaSyBY1Rhu-vxfGIxRHUgEIiFrBYSA5aTF3bE",authDomain: "iqiyi-intl-firebase.firebaseapp.com",databaseURL: "https://iqiyi-intl-firebase.firebaseio.com",projectId: "iqiyi-intl-firebase",storageBucket: "iqiyi-intl-firebase.appspot.com",messagingSenderId: "************",appId: "1:************:web:936fc748dd75bbaebdc5af",measurementId: "G-16PS358MGW"}; `
    /** 真是的代码
     *
     */
    if (this.props.pageProps.isWebB) {
      firebaseConfigStr = `var firebaseConfig = {apiKey: "AIzaSyBY1Rhu-vxfGIxRHUgEIiFrBYSA5aTF3bE",authDomain: "iqiyi-intl-firebase.firebaseapp.com",databaseURL: "https://iqiyi-intl-firebase.firebaseio.com",projectId: "iqiyi-intl-firebase",storageBucket: "iqiyi-intl-firebase.appspot.com",messagingSenderId: "************",appId: "1:************:web:80e0efcf87dee806bdc5af",measurementId: "G-JYLD16EKVB"}; `
      /** 真是代码
       *
       */
    }
    this.addFireBase(firebaseConfigStr)
    this.addGoogleTongji()
  }

  addScriptCode = codeStr => {
    const scr = document.createElement('script')
    scr.setAttribute('type', 'text/javascript')
    try {
      scr.appendChild(document.createTextNode(codeStr))
    } catch (error) {
      scr.text = codeStr
    }
    const HEAD =
      document.getElementsByTagName('head').item(0) || document.documentElement
    HEAD.appendChild(scr)
  }

  addFireBase = firebaseConfigStr => {
    const scr = document.createElement('script')
    scr.setAttribute('type', 'text/javascript')
    try {
      scr.appendChild(
        document.createTextNode(`${firebaseConfigStr}           
          var performance_standalone = '//www.gstatic.com/firebasejs/6.4.0/firebase-performance-standalone.js';
          (function(sa,fbc){function load(f,c){var a=document.createElement('script');
          a.async=1;a.src=f;var s=document.getElementsByTagName('script')[0];
          s.parentNode.insertBefore(a,s);}load(sa);window.onload = function() {firebase.initializeApp(fbc).performance();};
          })(performance_standalone, firebaseConfig);`)
      )
    } catch (error) {
      scr.text = `${firebaseConfigStr}           
      var performance_standalone = '//www.gstatic.com/firebasejs/6.4.0/firebase-performance-standalone.js';
      (function(sa,fbc){function load(f,c){var a=document.createElement('script');
      a.async=1;a.src=f;var s=document.getElementsByTagName('script')[0];
      s.parentNode.insertBefore(a,s);}load(sa);window.onload = function() {firebase.initializeApp(fbc).performance();};
      })(performance_standalone, firebaseConfig);`
    }
    const HEAD =
      document.getElementsByTagName('head').item(0) || document.documentElement
    HEAD.appendChild(scr)
  }

  addGoogleTongji = () => {
    const scr = document.createElement('script')
    scr.setAttribute('type', 'text/javascript')
    try {
      scr.appendChild(
        document.createTextNode(`
              (function(){/*
 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/var aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ba="function"==typeof Object.create?Object.create:function(a){var b=function(){};b.prototype=a;return new b},k;if("function"==typeof Object.setPrototypeOf)k=Object.setPrototypeOf;else{var m;a:{var ca={a:!0},n={};try{n.__proto__=ca;m=n.a;break a}catch(a){}m=!1}k=m?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var p=k,q=function(a,b){a.prototype=ba(b.prototype);a.prototype.constructor=a;if(p)p(a,b);else for(var c in b)if("prototype"!=c)if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.v=b.prototype},r=this||self,da=function(){},t=function(a){return a};var u;var w=function(a,b){this.g=b===v?a:""};w.prototype.toString=function(){return this.g+""};var v={},x=function(a){if(void 0===u){var b=null;var c=r.trustedTypes;if(c&&c.createPolicy){try{b=c.createPolicy("goog#html",{createHTML:t,createScript:t,createScriptURL:t})}catch(d){r.console&&r.console.error(d.message)}u=b}else u=b}a=(b=u)?b.createScriptURL(a):a;return new w(a,v)};var A=function(){return Math.floor(2147483648*Math.random()).toString(36)+Math.abs(Math.floor(2147483648*Math.random())^Date.now()).toString(36)};var B={},C=null;var D="function"===typeof Uint8Array;function E(a,b,c){return"object"===typeof a?D&&!Array.isArray(a)&&a instanceof Uint8Array?c(a):F(a,b,c):b(a)}function F(a,b,c){if(Array.isArray(a)){for(var d=Array(a.length),e=0;e<a.length;e++){var f=a[e];null!=f&&(d[e]=E(f,b,c))}Array.isArray(a)&&a.s&&G(d);return d}d={};for(e in a)Object.prototype.hasOwnProperty.call(a,e)&&(f=a[e],null!=f&&(d[e]=E(f,b,c)));return d}
function ea(a){return F(a,function(b){return"number"===typeof b?isFinite(b)?b:String(b):b},function(b){var c;void 0===c&&(c=0);if(!C){C={};for(var d="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),e=["+/=","+/","-_=","-_.","-_"],f=0;5>f;f++){var h=d.concat(e[f].split(""));B[f]=h;for(var g=0;g<h.length;g++){var l=h[g];void 0===C[l]&&(C[l]=g)}}}c=B[c];d=Array(Math.floor(b.length/3));e=c[64]||"";for(f=h=0;h<b.length-2;h+=3){var y=b[h],z=b[h+1];l=b[h+2];g=c[y>>2];y=c[(y&3)<<
4|z>>4];z=c[(z&15)<<2|l>>6];l=c[l&63];d[f++]=""+g+y+z+l}g=0;l=e;switch(b.length-h){case 2:g=b[h+1],l=c[(g&15)<<2]||e;case 1:b=b[h],d[f]=""+c[b>>2]+c[(b&3)<<4|g>>4]+l+e}return d.join("")})}var fa={s:{value:!0,configurable:!0}},G=function(a){Array.isArray(a)&&!Object.isFrozen(a)&&Object.defineProperties(a,fa);return a};var H;var J=function(a,b,c){var d=H;H=null;a||(a=d);d=this.constructor.u;a||(a=d?[d]:[]);this.j=d?0:-1;this.h=null;this.g=a;a:{d=this.g.length;a=d-1;if(d&&(d=this.g[a],!(null===d||"object"!=typeof d||Array.isArray(d)||D&&d instanceof Uint8Array))){this.l=a-this.j;this.i=d;break a}void 0!==b&&-1<b?(this.l=Math.max(b,a+1-this.j),this.i=null):this.l=Number.MAX_VALUE}if(c)for(b=0;b<c.length;b++)a=c[b],a<this.l?(a+=this.j,(d=this.g[a])?G(d):this.g[a]=I):(d=this.l+this.j,this.g[d]||(this.i=this.g[d]={}),(d=this.i[a])?
G(d):this.i[a]=I)},I=Object.freeze(G([])),K=function(a,b){if(-1===b)return null;if(b<a.l){b+=a.j;var c=a.g[b];return c!==I?c:a.g[b]=G([])}if(a.i)return c=a.i[b],c!==I?c:a.i[b]=G([])},M=function(a,b){var c=L;if(-1===b)return null;a.h||(a.h={});if(!a.h[b]){var d=K(a,b);d&&(a.h[b]=new c(d))}return a.h[b]};J.prototype.toJSON=function(){var a=N(this,!1);return ea(a)};
var N=function(a,b){if(a.h)for(var c in a.h)if(Object.prototype.hasOwnProperty.call(a.h,c)){var d=a.h[c];if(Array.isArray(d))for(var e=0;e<d.length;e++)d[e]&&N(d[e],b);else d&&N(d,b)}return a.g},O=function(a,b){H=b=b?JSON.parse(b):null;a=new a(b);H=null;return a};J.prototype.toString=function(){return N(this,!1).toString()};var P=function(a){J.call(this,a)};q(P,J);function ha(a){var b,c=(a.ownerDocument&&a.ownerDocument.defaultView||window).document,d=null===(b=c.querySelector)||void 0===b?void 0:b.call(c,"script[nonce]");(b=d?d.nonce||d.getAttribute("nonce")||"":"")&&a.setAttribute("nonce",b)};var Q=function(a,b){b=String(b);"application/xhtml+xml"===a.contentType&&(b=b.toLowerCase());return a.createElement(b)},R=function(a){this.g=a||r.document||document};R.prototype.appendChild=function(a,b){a.appendChild(b)};var S=function(a,b,c,d,e,f){try{var h=a.g,g=Q(a.g,"SCRIPT");g.async=!0;g.src=b instanceof w&&b.constructor===w?b.g:"type_error:TrustedResourceUrl";ha(g);h.head.appendChild(g);g.addEventListener("load",function(){e();d&&h.head.removeChild(g)});g.addEventListener("error",function(){0<c?S(a,b,c-1,d,e,f):(d&&h.head.removeChild(g),f())})}catch(l){f()}};var ia=r.atob("aHR0cHM6Ly93d3cuZ3N0YXRpYy5jb20vaW1hZ2VzL2ljb25zL21hdGVyaWFsL3N5c3RlbS8xeC93YXJuaW5nX2FtYmVyXzI0ZHAucG5n"),ja=r.atob("WW91IGFyZSBzZWVpbmcgdGhpcyBtZXNzYWdlIGJlY2F1c2UgYWQgb3Igc2NyaXB0IGJsb2NraW5nIHNvZnR3YXJlIGlzIGludGVyZmVyaW5nIHdpdGggdGhpcyBwYWdlLg=="),ka=r.atob("RGlzYWJsZSBhbnkgYWQgb3Igc2NyaXB0IGJsb2NraW5nIHNvZnR3YXJlLCB0aGVuIHJlbG9hZCB0aGlzIHBhZ2Uu"),la=function(a,b,c){this.h=a;this.j=new R(this.h);this.g=null;this.i=[];this.l=!1;this.o=b;this.m=c},V=function(a){if(a.h.body&&!a.l){var b=
function(){T(a);r.setTimeout(function(){return U(a,3)},50)};S(a.j,a.o,2,!0,function(){r[a.m]||b()},b);a.l=!0}},T=function(a){for(var b=W(1,5),c=0;c<b;c++){var d=X(a);a.h.body.appendChild(d);a.i.push(d)}b=X(a);b.style.bottom="0";b.style.left="0";b.style.position="fixed";b.style.width=W(100,110).toString()+"%";b.style.zIndex=W(2147483544,2147483644).toString();b.style["background-color"]=ma(249,259,242,252,219,229);b.style["box-shadow"]="0 0 12px #888";b.style.color=ma(0,10,0,10,0,10);b.style.display=
"flex";b.style["justify-content"]="center";b.style["font-family"]=" SF Pro, Roboto, Noto Sans, sans-serif";c=X(a);c.style.width=W(80,85).toString()+"%";c.style.maxWidth=W(750,775).toString()+"px";c.style.margin="24px";c.style.display="flex";c.style["align-items"]="flex-start";c.style["justify-content"]="center";d=Q(a.j.g,"IMG");d.className=A();d.src=ia;d.style.height="24px";d.style.width="24px";d.style["padding-right"]="16px";var e=X(a),f=X(a);f.style["font-weight"]="bold";f.textContent=ja;var h=X(a);h.textContent=ka;Y(a,
e,f);Y(a,e,h);Y(a,c,d);Y(a,c,e);Y(a,b,c);a.g=b;a.h.body.appendChild(a.g);b=W(1,5);for(c=0;c<b;c++)d=X(a),a.h.body.appendChild(d),a.i.push(d)},Y=function(a,b,c){for(var d=W(1,5),e=0;e<d;e++){var f=X(a);b.appendChild(f)}b.appendChild(c);c=W(1,5);for(d=0;d<c;d++)e=X(a),b.appendChild(e)},W=function(a,b){return Math.floor(a+Math.random()*(b-a))},ma=function(a,b,c,d,e,f){return"rgb("+W(Math.max(a,0),Math.min(b,255)).toString()+","+W(Math.max(c,0),Math.min(d,255)).toString()+","+W(Math.max(e,0),Math.min(f,
255)).toString()+")"},X=function(a){a=Q(a.j.g,"DIV");a.className=A();return a},U=function(a,b){0>=b||null!=a.g&&0!=a.g.offsetHeight&&0!=a.g.offsetWidth||(na(a),T(a),r.setTimeout(function(){return U(a,b-1)},50))},na=function(a){var b=a.i;var c="undefined"!=typeof Symbol&&Symbol.iterator&&b[Symbol.iterator];b=c?c.call(b):{next:aa(b)};for(c=b.next();!c.done;c=b.next())(c=c.value)&&c.parentNode&&c.parentNode.removeChild(c);a.i=[];(b=a.g)&&b.parentNode&&b.parentNode.removeChild(b);a.g=null};var pa=function(a,b,c,d,e){var f=oa(c),h=function(l){l.appendChild(f);r.setTimeout(function(){f?(0!==f.offsetHeight&&0!==f.offsetWidth?b():a(),f.parentNode&&f.parentNode.removeChild(f)):a()},d)},g=function(l){document.body?h(document.body):0<l?r.setTimeout(function(){g(l-1)},e):b()};g(3)},oa=function(a){var b=document.createElement("div");b.className=a;b.style.width="1px";b.style.height="1px";b.style.position="absolute";b.style.left="-10000px";b.style.top="-10000px";b.style.zIndex="-10000";return b};var L=function(a){J.call(this,a)};q(L,J);var qa=function(a){J.call(this,a)};q(qa,J);var ra=function(a,b){this.l=a;this.m=new R(a.document);this.g=b;this.i=K(this.g,1);b=M(this.g,2);this.o=x(K(b,4)||"");this.h=!1;b=M(this.g,13);b=x(K(b,4)||"");this.j=new la(a.document,b,K(this.g,12))};ra.prototype.start=function(){sa(this)};
var sa=function(a){ta(a);S(a.m,a.o,3,!1,function(){a:{var b=a.i;var c=r.btoa(b);if(c=r[c]){try{var d=O(P,r.atob(c))}catch(e){b=!1;break a}b=b===K(d,1)}else b=!1}b?Z(a,K(a.g,14)):(Z(a,K(a.g,8)),V(a.j))},function(){pa(function(){Z(a,K(a.g,7));V(a.j)},function(){return Z(a,K(a.g,6))},K(a.g,9),K(a.g,10),K(a.g,11))})},Z=function(a,b){a.h||(a.h=!0,a=new a.l.XMLHttpRequest,a.open("GET",b,!0),a.send())},ta=function(a){var b=r.btoa(a.i);a.l[b]&&Z(a,K(a.g,5))};(function(a,b){r[a]=function(c){for(var d=[],e=0;e<arguments.length;++e)d[e-0]=arguments[e];r[a]=da;b.apply(null,d)}})("__h82AlnkH6D91__",function(a){"function"===typeof window.atob&&(new ra(window,O(qa,window.atob(a)))).start()});}).call(this);
 
window.__h82AlnkH6D91__("WyJwdWItMjg0ODQ2MDI4MDQ2NTIwMiIsW251bGwsbnVsbCxudWxsLCJodHRwczovL2Z1bmRpbmdjaG9pY2VzbWVzc2FnZXMuZ29vZ2xlLmNvbS9iL3B1Yi0yODQ4NDYwMjgwNDY1MjAyIl0sbnVsbCxudWxsLCJodHRwczovL2Z1bmRpbmdjaG9pY2VzbWVzc2FnZXMuZ29vZ2xlLmNvbS9lbC9BR1NLV3hVeWItaEhuVXVxeDFTWHhJYjdreDlBNjVoTU1qMHZWT2dldjh4VHhDbGUxT0VXT3JlUFBTYm5sbVlkR3BWdWhFU2dFUTRrUUM3b29tYnZMX3dOOTFqdE9BXHUwMDNkXHUwMDNkP3RlXHUwMDNkVE9LRU5fRVhQT1NFRCIsImh0dHBzOi8vZnVuZGluZ2Nob2ljZXNtZXNzYWdlcy5nb29nbGUuY29tL2VsL0FHU0tXeFh6MVY4aFlSNlRhR0hWUlg0dkRpc3VJdVg5TUV0OXJpT0FjQUlxeG5VeUNxUzJ2b2QyZkZiaFJfem5qNl9mck9pWUpHeTA5elBwOE93UTZQbFNjMHBuUUFcdTAwM2RcdTAwM2Q/YWJcdTAwM2QxXHUwMDI2c2JmXHUwMDNkMSIsImh0dHBzOi8vZnVuZGluZ2Nob2ljZXNtZXNzYWdlcy5nb29nbGUuY29tL2VsL0FHU0tXeFdiallPbDBfTk5rTGFRSUlCa0NJeWcyQ3dKbTBOUG9ta0RWTzU2NXlCd3JuSkhOZzhZaE9vWm1WR3NHMjN5NzR4NXhId092SURrTU1fRElVcmo1U2dBSGdcdTAwM2RcdTAwM2Q/YWJcdTAwM2QyXHUwMDI2c2JmXHUwMDNkMSIsImh0dHBzOi8vZnVuZGluZ2Nob2ljZXNtZXNzYWdlcy5nb29nbGUuY29tL2VsL0FHU0tXeFU0bW5jTEdrWHIzVzdZNVgzYlg3QmFQakMydXdOa1lVZGJNZnYyQzFqc2M0RTk3S2FoRTNnN09nRFJ1elBnWUFvWGl5QUUzRk4zM3V6a2xtbWlaMS10RFFcdTAwM2RcdTAwM2Q/c2JmXHUwMDNkMiIsImRpdi1ncHQtYWQiLDIwLDEwMCwiY0hWaUxUSTRORGcwTmpBeU9EQTBOalV5TURJXHUwMDNkIixbbnVsbCxudWxsLG51bGwsImh0dHBzOi8vd3d3LmdzdGF0aWMuY29tLzBlbW4vZi9wL3B1Yi0yODQ4NDYwMjgwNDY1MjAyLmpzP3VzcXBcdTAwM2RDQkEiXSwiaHR0cHM6Ly9mdW5kaW5nY2hvaWNlc21lc3NhZ2VzLmdvb2dsZS5jb20vZWwvQUdTS1d4V2l1Z1VGUS1rbi1kcVNFdjdldmUyN29Rd3JYV3NuM3hGOGc5Sk5XMjVaa1V6dVByeVlLVHJkVHVhSVpCTXdvSk51S2tEY002UWxITkxtemVYWEp2SkpIUVx1MDAzZFx1MDAzZCJd");
            `)
      )
    } catch (error) {
      scr.text = `
              (function(){/*
 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/var aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ba="function"==typeof Object.create?Object.create:function(a){var b=function(){};b.prototype=a;return new b},k;if("function"==typeof Object.setPrototypeOf)k=Object.setPrototypeOf;else{var m;a:{var ca={a:!0},n={};try{n.__proto__=ca;m=n.a;break a}catch(a){}m=!1}k=m?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var p=k,q=function(a,b){a.prototype=ba(b.prototype);a.prototype.constructor=a;if(p)p(a,b);else for(var c in b)if("prototype"!=c)if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.v=b.prototype},r=this||self,da=function(){},t=function(a){return a};var u;var w=function(a,b){this.g=b===v?a:""};w.prototype.toString=function(){return this.g+""};var v={},x=function(a){if(void 0===u){var b=null;var c=r.trustedTypes;if(c&&c.createPolicy){try{b=c.createPolicy("goog#html",{createHTML:t,createScript:t,createScriptURL:t})}catch(d){r.console&&r.console.error(d.message)}u=b}else u=b}a=(b=u)?b.createScriptURL(a):a;return new w(a,v)};var A=function(){return Math.floor(2147483648*Math.random()).toString(36)+Math.abs(Math.floor(2147483648*Math.random())^Date.now()).toString(36)};var B={},C=null;var D="function"===typeof Uint8Array;function E(a,b,c){return"object"===typeof a?D&&!Array.isArray(a)&&a instanceof Uint8Array?c(a):F(a,b,c):b(a)}function F(a,b,c){if(Array.isArray(a)){for(var d=Array(a.length),e=0;e<a.length;e++){var f=a[e];null!=f&&(d[e]=E(f,b,c))}Array.isArray(a)&&a.s&&G(d);return d}d={};for(e in a)Object.prototype.hasOwnProperty.call(a,e)&&(f=a[e],null!=f&&(d[e]=E(f,b,c)));return d}
function ea(a){return F(a,function(b){return"number"===typeof b?isFinite(b)?b:String(b):b},function(b){var c;void 0===c&&(c=0);if(!C){C={};for(var d="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),e=["+/=","+/","-_=","-_.","-_"],f=0;5>f;f++){var h=d.concat(e[f].split(""));B[f]=h;for(var g=0;g<h.length;g++){var l=h[g];void 0===C[l]&&(C[l]=g)}}}c=B[c];d=Array(Math.floor(b.length/3));e=c[64]||"";for(f=h=0;h<b.length-2;h+=3){var y=b[h],z=b[h+1];l=b[h+2];g=c[y>>2];y=c[(y&3)<<
4|z>>4];z=c[(z&15)<<2|l>>6];l=c[l&63];d[f++]=""+g+y+z+l}g=0;l=e;switch(b.length-h){case 2:g=b[h+1],l=c[(g&15)<<2]||e;case 1:b=b[h],d[f]=""+c[b>>2]+c[(b&3)<<4|g>>4]+l+e}return d.join("")})}var fa={s:{value:!0,configurable:!0}},G=function(a){Array.isArray(a)&&!Object.isFrozen(a)&&Object.defineProperties(a,fa);return a};var H;var J=function(a,b,c){var d=H;H=null;a||(a=d);d=this.constructor.u;a||(a=d?[d]:[]);this.j=d?0:-1;this.h=null;this.g=a;a:{d=this.g.length;a=d-1;if(d&&(d=this.g[a],!(null===d||"object"!=typeof d||Array.isArray(d)||D&&d instanceof Uint8Array))){this.l=a-this.j;this.i=d;break a}void 0!==b&&-1<b?(this.l=Math.max(b,a+1-this.j),this.i=null):this.l=Number.MAX_VALUE}if(c)for(b=0;b<c.length;b++)a=c[b],a<this.l?(a+=this.j,(d=this.g[a])?G(d):this.g[a]=I):(d=this.l+this.j,this.g[d]||(this.i=this.g[d]={}),(d=this.i[a])?
G(d):this.i[a]=I)},I=Object.freeze(G([])),K=function(a,b){if(-1===b)return null;if(b<a.l){b+=a.j;var c=a.g[b];return c!==I?c:a.g[b]=G([])}if(a.i)return c=a.i[b],c!==I?c:a.i[b]=G([])},M=function(a,b){var c=L;if(-1===b)return null;a.h||(a.h={});if(!a.h[b]){var d=K(a,b);d&&(a.h[b]=new c(d))}return a.h[b]};J.prototype.toJSON=function(){var a=N(this,!1);return ea(a)};
var N=function(a,b){if(a.h)for(var c in a.h)if(Object.prototype.hasOwnProperty.call(a.h,c)){var d=a.h[c];if(Array.isArray(d))for(var e=0;e<d.length;e++)d[e]&&N(d[e],b);else d&&N(d,b)}return a.g},O=function(a,b){H=b=b?JSON.parse(b):null;a=new a(b);H=null;return a};J.prototype.toString=function(){return N(this,!1).toString()};var P=function(a){J.call(this,a)};q(P,J);function ha(a){var b,c=(a.ownerDocument&&a.ownerDocument.defaultView||window).document,d=null===(b=c.querySelector)||void 0===b?void 0:b.call(c,"script[nonce]");(b=d?d.nonce||d.getAttribute("nonce")||"":"")&&a.setAttribute("nonce",b)};var Q=function(a,b){b=String(b);"application/xhtml+xml"===a.contentType&&(b=b.toLowerCase());return a.createElement(b)},R=function(a){this.g=a||r.document||document};R.prototype.appendChild=function(a,b){a.appendChild(b)};var S=function(a,b,c,d,e,f){try{var h=a.g,g=Q(a.g,"SCRIPT");g.async=!0;g.src=b instanceof w&&b.constructor===w?b.g:"type_error:TrustedResourceUrl";ha(g);h.head.appendChild(g);g.addEventListener("load",function(){e();d&&h.head.removeChild(g)});g.addEventListener("error",function(){0<c?S(a,b,c-1,d,e,f):(d&&h.head.removeChild(g),f())})}catch(l){f()}};var ia=r.atob("aHR0cHM6Ly93d3cuZ3N0YXRpYy5jb20vaW1hZ2VzL2ljb25zL21hdGVyaWFsL3N5c3RlbS8xeC93YXJuaW5nX2FtYmVyXzI0ZHAucG5n"),ja=r.atob("WW91IGFyZSBzZWVpbmcgdGhpcyBtZXNzYWdlIGJlY2F1c2UgYWQgb3Igc2NyaXB0IGJsb2NraW5nIHNvZnR3YXJlIGlzIGludGVyZmVyaW5nIHdpdGggdGhpcyBwYWdlLg=="),ka=r.atob("RGlzYWJsZSBhbnkgYWQgb3Igc2NyaXB0IGJsb2NraW5nIHNvZnR3YXJlLCB0aGVuIHJlbG9hZCB0aGlzIHBhZ2Uu"),la=function(a,b,c){this.h=a;this.j=new R(this.h);this.g=null;this.i=[];this.l=!1;this.o=b;this.m=c},V=function(a){if(a.h.body&&!a.l){var b=
function(){T(a);r.setTimeout(function(){return U(a,3)},50)};S(a.j,a.o,2,!0,function(){r[a.m]||b()},b);a.l=!0}},T=function(a){for(var b=W(1,5),c=0;c<b;c++){var d=X(a);a.h.body.appendChild(d);a.i.push(d)}b=X(a);b.style.bottom="0";b.style.left="0";b.style.position="fixed";b.style.width=W(100,110).toString()+"%";b.style.zIndex=W(2147483544,2147483644).toString();b.style["background-color"]=ma(249,259,242,252,219,229);b.style["box-shadow"]="0 0 12px #888";b.style.color=ma(0,10,0,10,0,10);b.style.display=
"flex";b.style["justify-content"]="center";b.style["font-family"]="SF Pro, Roboto, Noto Sans, sans-serif";c=X(a);c.style.width=W(80,85).toString()+"%";c.style.maxWidth=W(750,775).toString()+"px";c.style.margin="24px";c.style.display="flex";c.style["align-items"]="flex-start";c.style["justify-content"]="center";d=Q(a.j.g,"IMG");d.className=A();d.src=ia;d.style.height="24px";d.style.width="24px";d.style["padding-right"]="16px";var e=X(a),f=X(a);f.style["font-weight"]="bold";f.textContent=ja;var h=X(a);h.textContent=ka;Y(a,
e,f);Y(a,e,h);Y(a,c,d);Y(a,c,e);Y(a,b,c);a.g=b;a.h.body.appendChild(a.g);b=W(1,5);for(c=0;c<b;c++)d=X(a),a.h.body.appendChild(d),a.i.push(d)},Y=function(a,b,c){for(var d=W(1,5),e=0;e<d;e++){var f=X(a);b.appendChild(f)}b.appendChild(c);c=W(1,5);for(d=0;d<c;d++)e=X(a),b.appendChild(e)},W=function(a,b){return Math.floor(a+Math.random()*(b-a))},ma=function(a,b,c,d,e,f){return"rgb("+W(Math.max(a,0),Math.min(b,255)).toString()+","+W(Math.max(c,0),Math.min(d,255)).toString()+","+W(Math.max(e,0),Math.min(f,
255)).toString()+")"},X=function(a){a=Q(a.j.g,"DIV");a.className=A();return a},U=function(a,b){0>=b||null!=a.g&&0!=a.g.offsetHeight&&0!=a.g.offsetWidth||(na(a),T(a),r.setTimeout(function(){return U(a,b-1)},50))},na=function(a){var b=a.i;var c="undefined"!=typeof Symbol&&Symbol.iterator&&b[Symbol.iterator];b=c?c.call(b):{next:aa(b)};for(c=b.next();!c.done;c=b.next())(c=c.value)&&c.parentNode&&c.parentNode.removeChild(c);a.i=[];(b=a.g)&&b.parentNode&&b.parentNode.removeChild(b);a.g=null};var pa=function(a,b,c,d,e){var f=oa(c),h=function(l){l.appendChild(f);r.setTimeout(function(){f?(0!==f.offsetHeight&&0!==f.offsetWidth?b():a(),f.parentNode&&f.parentNode.removeChild(f)):a()},d)},g=function(l){document.body?h(document.body):0<l?r.setTimeout(function(){g(l-1)},e):b()};g(3)},oa=function(a){var b=document.createElement("div");b.className=a;b.style.width="1px";b.style.height="1px";b.style.position="absolute";b.style.left="-10000px";b.style.top="-10000px";b.style.zIndex="-10000";return b};var L=function(a){J.call(this,a)};q(L,J);var qa=function(a){J.call(this,a)};q(qa,J);var ra=function(a,b){this.l=a;this.m=new R(a.document);this.g=b;this.i=K(this.g,1);b=M(this.g,2);this.o=x(K(b,4)||"");this.h=!1;b=M(this.g,13);b=x(K(b,4)||"");this.j=new la(a.document,b,K(this.g,12))};ra.prototype.start=function(){sa(this)};
var sa=function(a){ta(a);S(a.m,a.o,3,!1,function(){a:{var b=a.i;var c=r.btoa(b);if(c=r[c]){try{var d=O(P,r.atob(c))}catch(e){b=!1;break a}b=b===K(d,1)}else b=!1}b?Z(a,K(a.g,14)):(Z(a,K(a.g,8)),V(a.j))},function(){pa(function(){Z(a,K(a.g,7));V(a.j)},function(){return Z(a,K(a.g,6))},K(a.g,9),K(a.g,10),K(a.g,11))})},Z=function(a,b){a.h||(a.h=!0,a=new a.l.XMLHttpRequest,a.open("GET",b,!0),a.send())},ta=function(a){var b=r.btoa(a.i);a.l[b]&&Z(a,K(a.g,5))};(function(a,b){r[a]=function(c){for(var d=[],e=0;e<arguments.length;++e)d[e-0]=arguments[e];r[a]=da;b.apply(null,d)}})("__h82AlnkH6D91__",function(a){"function"===typeof window.atob&&(new ra(window,O(qa,window.atob(a)))).start()});}).call(this);
 
window.__h82AlnkH6D91__("WyJwdWItMjg0ODQ2MDI4MDQ2NTIwMiIsW251bGwsbnVsbCxudWxsLCJodHRwczovL2Z1bmRpbmdjaG9pY2VzbWVzc2FnZXMuZ29vZ2xlLmNvbS9iL3B1Yi0yODQ4NDYwMjgwNDY1MjAyIl0sbnVsbCxudWxsLCJodHRwczovL2Z1bmRpbmdjaG9pY2VzbWVzc2FnZXMuZ29vZ2xlLmNvbS9lbC9BR1NLV3hVeWItaEhuVXVxeDFTWHhJYjdreDlBNjVoTU1qMHZWT2dldjh4VHhDbGUxT0VXT3JlUFBTYm5sbVlkR3BWdWhFU2dFUTRrUUM3b29tYnZMX3dOOTFqdE9BXHUwMDNkXHUwMDNkP3RlXHUwMDNkVE9LRU5fRVhQT1NFRCIsImh0dHBzOi8vZnVuZGluZ2Nob2ljZXNtZXNzYWdlcy5nb29nbGUuY29tL2VsL0FHU0tXeFh6MVY4aFlSNlRhR0hWUlg0dkRpc3VJdVg5TUV0OXJpT0FjQUlxeG5VeUNxUzJ2b2QyZkZiaFJfem5qNl9mck9pWUpHeTA5elBwOE93UTZQbFNjMHBuUUFcdTAwM2RcdTAwM2Q/YWJcdTAwM2QxXHUwMDI2c2JmXHUwMDNkMSIsImh0dHBzOi8vZnVuZGluZ2Nob2ljZXNtZXNzYWdlcy5nb29nbGUuY29tL2VsL0FHU0tXeFdiallPbDBfTk5rTGFRSUlCa0NJeWcyQ3dKbTBOUG9ta0RWTzU2NXlCd3JuSkhOZzhZaE9vWm1WR3NHMjN5NzR4NXhId092SURrTU1fRElVcmo1U2dBSGdcdTAwM2RcdTAwM2Q/YWJcdTAwM2QyXHUwMDI2c2JmXHUwMDNkMSIsImh0dHBzOi8vZnVuZGluZ2Nob2ljZXNtZXNzYWdlcy5nb29nbGUuY29tL2VsL0FHU0tXeFU0bW5jTEdrWHIzVzdZNVgzYlg3QmFQakMydXdOa1lVZGJNZnYyQzFqc2M0RTk3S2FoRTNnN09nRFJ1elBnWUFvWGl5QUUzRk4zM3V6a2xtbWlaMS10RFFcdTAwM2RcdTAwM2Q/c2JmXHUwMDNkMiIsImRpdi1ncHQtYWQiLDIwLDEwMCwiY0hWaUxUSTRORGcwTmpBeU9EQTBOalV5TURJXHUwMDNkIixbbnVsbCxudWxsLG51bGwsImh0dHBzOi8vd3d3LmdzdGF0aWMuY29tLzBlbW4vZi9wL3B1Yi0yODQ4NDYwMjgwNDY1MjAyLmpzP3VzcXBcdTAwM2RDQkEiXSwiaHR0cHM6Ly9mdW5kaW5nY2hvaWNlc21lc3NhZ2VzLmdvb2dsZS5jb20vZWwvQUdTS1d4V2l1Z1VGUS1rbi1kcVNFdjdldmUyN29Rd3JYV3NuM3hGOGc5Sk5XMjVaa1V6dVByeVlLVHJkVHVhSVpCTXdvSk51S2tEY002UWxITkxtemVYWEp2SkpIUVx1MDAzZFx1MDAzZCJd");
            `
    }
    const HEAD =
      document.getElementsByTagName('head').item(0) || document.documentElement
    HEAD.appendChild(scr)
  }

  addAdJs = src => {
    const HEAD =
      document.getElementsByTagName('head').item(0) || document.documentElement
    const scr = document.createElement('script')
    scr.setAttribute('type', 'text/javascript')
    scr.async = true
    let hasResult = false
    const onfunc = () => {
      if (!hasResult) {
        if (
          !scr.readyState ||
          scr.readyState === 'loaded' ||
          scr.readyState === 'complete'
        ) {
          // 请求成功后查api // 循环检测googletag
          hasResult = true
          if (src.includes('tag/js/gpt.js')) {
            window.googlewebgpthasLoaded = true // 代表已经加载过 js
          } else if (src.includes('js/sdkloader/ima3.js')) {
            window.googlewebima3Loaded = true // 代表已经加载过 js
          }
        }
      }
    }
    const onerror = () => {
      if (!hasResult) {
        hasResult = true
      }
    }
    scr.onload = onfunc()
    scr.onreadystatechange = onfunc()
    scr.onerror = onerror()
    scr.onstalled = onerror()
    scr.setAttribute('src', src)
    HEAD.appendChild(scr)
  }

  static async getInitialProps({ Component, ctx }) {
    // 判断是否是主页或者频道页
    const judgeHomeOrChannelPage = pathname => {
      const pageNameArr = [
        '/',
        '/drama',
        '/movie',
        '/variety-show',
        '/anime',
        '/kids',
        '/documentary',
        'yule'
      ]
      return pageNameArr.includes(pathname)
    }
    // 优化的页面
    const optimizePaths = ['/play', '/album']
    const { store, req, pathname } = ctx
    let mod = ctx.query.mod || 'intl'
    mod = encodeURIComponent(mod)
    const lang = ctx.query.lang || 'en_us'
    const isRN = +ctx.query['isRN'] === 1
    const isWebB = req.headers['x-test-plan'] === 'web-B'
    const isInApp = ctx.query['in_app'] === '1'
    const isLoginPage = ctx.query['is_login_page'] === '1'
    const isPrivilege = ctx.query['is_privilege'] === '1'
    // 频道页需要特殊处理footer
    // const isHomeOrChannelPage = judgeHomeOrChannelPage(ctx.pathname)
    let isMobile = false
    let isPad = false
    const headers = req.headers
    let isSeo = false
    if (headers['is-robot-query']) {
      isSeo = true
    }
    let userAgent = ''
    let abtestVal = ''
    let qc005val = getCookies('QC005', ctx)
    if (ctx.isServer) {
      global.ctx = ctx
      userAgent = req.headers['user-agent'] || ''
      global.deviceAgent = userAgent.toLowerCase()
      global.hostName = req.headers.host
      global.pageReqUrl = req.url
      global.reqHeaders = req.headers
      global.queryMod = mod
      if (!qc005val) {
        qc005val = getCookie005Val()
        setCookies('QC005', qc005val)
        // setCookies('SVR005', qc005val)
      }
      global.qc005Val = qc005val
      abtestVal = getAbtestType(qc005val)
      // setCookies()
      // console.log('res--->',  ctx.res)
      const ip =
        req.headers['x-qa-ip-key'] ||
        req.connection.remoteAddress ||
        req.socket.remoteAddress ||
        (req.connection.socket ? req.connection.socket.remoteAddress : '')
      const ipNum = ip.split(':')
      const realIp = ipNum[ipNum.length - 1].split(',')
      // 本地测试传参127.0.0.1 目前来看是有数据的，已经和李义商量过，可以这么搞
      global.clientIp = realIp[0]
      const acceptStr = req.headers['accept'] || ''
      global.AcceptTest = acceptStr

      isMobile = getDevice() === 'mobile'
      isPad = getMobileType() === 'ipad'
      // isAndroid = getMobileType() === 'android'
    }
    let curLang = lang
    const isDev = process.env.NODE_ENV !== 'production'
    if (isDev) {
      const modCookie = getCookies('mod', ctx)
      const langCookie = getCookies('lang', ctx)
      if (modCookie === mod && langCookie) {
        curLang = langCookie // 上次访问语言
      } else {
        curLang = lang // 浏览器默认语言
      }
    }
    const options = {
      ctx
    }
    options.params = {
      modeCode: mod,
      langCode: curLang,
      platformId: platformId(),
      ctx
    }
    const data = await $http(MODE_LANG, options)
    store.dispatch(fetchModeLangListAction({ mod, data, ctx }))
    store.dispatch(getParentalListAction({ ctx }))
    if (
      (!optimizePaths.includes(pathname) &&
        !judgeHomeOrChannelPage(pathname)) ||
      isSeo
    ) {
      store.dispatch(fetchVipModeLangListAction({ mod, data, ctx }))
      store.dispatch(fetchCashierModeLangListAction({ mod, data, ctx }))
      store.dispatch(fetchPrivilegeModeLangListAction({ mod, data, ctx }))
    }
    if (data.code === '0') {
      const langObj = data.data[0] || {}
      if (langObj.langCode) {
        curLang = langObj.langCode
      }
    }
    const ptidData = await $http(MODE_PTID, {
      params: {
        langCode: curLang,
        deviceId: global.qc005Val,
        modeCode: mod,
        platformId: platformId()
      },
      ctx
    })
    let ptid // ptid 对应会员业务platform
    let vipPid // 会员专用，存储对应模式下可用会员类型
    let bossCode // 激活码用到的platform值
    if (ptidData.code === '0') {
      ptid = ptidData.data.ptid
      vipPid = ptidData.data.cashierConfig
      bossCode = ptidData.data.bossCode
    }

    // 获取mark icon
    try {
      await store.dispatch(fetchGenericMarkAction())
    } catch (e) {
      console.log(e)
    }

    // 获取自定义配置header tab
    const curTabInfoData = await getTabInfo(ctx, curLang)
    const chnId = curTabInfoData?.chnId || ''
    const trueCommonConfig = store
      .getState()
      .get('commonConfig')
      .toJS()
    let curCommonConfigKey = ''
    let firstFocusImg // 焦点图第一张人物图
    let firstFocusBg // 焦点图第一张背景图
    let firstCardId // 渲染第一个card id
    let focusCardId // 焦点图card id
    const resIds = []

    if (pathname === '/') {
      curCommonConfigKey = 'home_web'
    } else {
      curCommonConfigKey = pathname.slice(1) + '_web'
      curCommonConfigKey = curCommonConfigKey.replace('-', '_')
    }
    // 播放页单独处理
    if (
      trueCommonConfig[curCommonConfigKey] &&
      ['play_web', 'live_web'].indexOf(curCommonConfigKey) === -1
    ) {
      const curPcwCommonData = await store.dispatch(
        fetchConfigAction({
          pageSt: curCommonConfigKey,
          channelId: chnId,
          rpage: pathname === '/' ? 'home' : pathname.slice(1),
          ctx,
          isMobile,
          isSeo,
          _catch: {}
        })
      )

      const curPcwCommonDataResources = curPcwCommonData?.data?.resources || []
      firstFocusImg = curPcwCommonData?.data?.firstFocusImg || ''
      firstFocusBg = curPcwCommonData?.data?.firstFocusBg || ''
      firstCardId = curPcwCommonData?.data?.firstCardId || ''
      focusCardId = curPcwCommonData?.data?.focusCardId || ''

      curPcwCommonDataResources.forEach(item => {
        if (item && item.id && item.type !== 'pcw_focus_banner') {
          resIds.push({
            id: item.id,
            type: item.type
          })
        }
      })
    }

    let filmLibraryData = [{ id: 1 }]
    if (pathname.includes('/film-library') > -1) {
      filmLibraryData = await store.dispatch(
        fetchFocusInfoAction({ _catch: {} })
      )
    }
    // 功能配置
    const funcsOptions = {}
    funcsOptions.params = {
      platform_id: iqSwitchPlatformId(),
      app_v: '1.0.0',
      lang: curLang,
      app_lm: mod
    }
    let featureSwitchData = {}
    try {
      const res = await $http(FUNCS_API_QAE, funcsOptions)
      if (res && res.code === 0) {
        featureSwitchData = res.data || {}
        store.dispatch(setFeatureSwitchAction(featureSwitchData))
      }
    } catch (e) {
      console.log(e)
    }

    let pageProps = {}
    if (Component.getInitialProps) {
      if (Component.isBeforeFetchData) {
        pageProps = await Component.getInitialProps({
          ctx,
          data: filmLibraryData,
          ptid
        })
      } else {
        pageProps = await Component.getInitialProps({ ctx, ptid })
      }
    }

    pageProps.featureSwitchData = featureSwitchData
    pageProps.isLoginPage = isLoginPage
    // pageProps.isHomeOrChannelPage = isHomeOrChannelPage
    pageProps.isPrivilege = isPrivilege
    pageProps.isInApp = isInApp
    pageProps.isRN = isRN
    pageProps.isWebB = isWebB
    pageProps.pathname = pathname
    pageProps.modeCode = mod
    pageProps.langCode = curLang
    pageProps.ptid = ptid
    pageProps.vipPid = vipPid
    pageProps.bossCode = bossCode
    pageProps.isMobile = isMobile
    pageProps.isPad = isPad
    pageProps.isSeo = isSeo
    pageProps.abtestVal = abtestVal
    pageProps.chnId = chnId
    pageProps.firstCardId = firstCardId
    pageProps.focusCardId = focusCardId

    if (ctx.isServer) {
      // 暂时挂到这里传给_document使用, 查到更好的方式再改
      // 需求pms:http://pms.qiyi.domain/browse/GLOBALLINEDEV-1003
      global.langCode = curLang
      global.modeCode = mod
      global.firstFocusImg = firstFocusImg
      global.firstFocusBg = firstFocusBg
      global.firstCardId = firstCardId
      global.pageSt = curCommonConfigKey
      global.chnId = chnId
      global.focusCardId = focusCardId
    }
    return {
      pageProps
    }
  }

  render() {
    const { Component, pageProps, store } = this.props
    const {
      cookiePopupVisible,
      i18nShowCoupon,
      installpromptEvent
    } = this.state

    return (
      <Provider store={store}>
        <DeviceCtx.Provider
          value={{
            isPad: pageProps.isPad,
            isMobile: pageProps.isMobile,
            pathname: pageProps.pathname,
            modeCode: pageProps.modeCode,
            langCode: pageProps.langCode,
            ptid: pageProps.ptid,
            vipPid: pageProps.vipPid,
            bossCode: pageProps.bossCode,
            cookieSetHandler: this.cookieSetHandler,
            i18nShowCoupon,
            installpromptEvent
          }}
        >
          <Layout
            isInApp={pageProps.isInApp}
            isRN={pageProps.isRN}
            isLoginPage={pageProps.isLoginPage}
            isPrivilege={pageProps.isPrivilege}
            cookieSetHandler={this.cookieSetHandler}
            // isHomeOrChannelPage={pageProps.isHomeOrChannelPage}
            isMobile={pageProps.isMobile}
            pathname={pageProps.pathname}
            isSeo={pageProps.isSeo}
            abtestVal={pageProps.abtestVal}
          >
            {/* 多个页面均有的组件放在这里写，例如cookie协议，导流toast等 */}
            <LayoutComponents
              cookieSetting={this.cookieSetting}
              updateGACode={this.updateGACode}
              cookieSetHandler={this.cookieSetHandler}
              cookiePopupVisible={cookiePopupVisible}
              isRN={pageProps.isRN}
              isInApp={pageProps.isInApp}
            >
              <Component {...pageProps} i18nShowCoupon={i18nShowCoupon} />
            </LayoutComponents>
          </Layout>
        </DeviceCtx.Provider>
      </Provider>
    )
  }
}

export default withRedux(createStore, {
  serializeState: state => state.toJS(),
  deserializeState: state => fromJS(state)
})(withReduxSaga(MyApp))
