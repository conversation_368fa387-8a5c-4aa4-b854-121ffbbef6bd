const { getVideoDecodeId } = require('../../server/decodeId')

const getShortIdController = async (ctx, next) => {
  const paramsId = ctx.params.id
  const queryId = ctx.query.id
  const mod = ctx.params.mod
  const id = paramsId || queryId
  if (mod && id) {
    ctx._custom.uri = `${ctx.protocol}://${ctx.host}/short/${id}`
    ctx.status = 301
  }

  if (id) {
    const idObj = getVideoDecodeId(id)
    global.decodeId = idObj[id] ? idObj[id].tvId || idObj[id].albumId : 0
  }
  ctx.query.id = id
  ctx._custom.path = '/short'
  await next()
}

module.exports = getShortIdController
