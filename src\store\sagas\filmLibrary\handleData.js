import { rebuildPlayUrl, canUseWebp, rebuildAlbumUrl } from '@/kit/common'
import { removeProtocol } from '@/kit/url'
import { changeAlbumUrl } from '@/utils/video'

export const handleLabelList = data => {
  try {
    return data.reduce((_data, item) => {
      const { tags = [] } = item
      const children = tags.map(s => {
        return {
          classify: s.sn,
          labelArr:
            (s.tags.length &&
              s.tags.map(tag => {
                return {
                  type: tag.v || '',
                  labelName: tag.n
                }
              })) ||
            []
        }
      })
      _data.push({
        id: item.id + '',
        name: item.name,
        children
      })
      return _data
    }, [])
  } catch (e) {
    console.log(e)
    return []
  }
}

export const handleVideoFilter = (data, params = {}) => {
  const {
    resId,
    block,
    position,
    rpage,
    s_ct: sCt,
    s_page: sPage,
    s_source: sSource,
    s_tag: sTag,
    s_mode: sMode,
    pbMsg
    // isMobile,
    // modeCode
  } = params
  const pbBlock = block
  const newData = {
    rowName: data.title,
    videos: [],
    chnid: '',
    // id: data.qipuId,
    pingback: {
      bkt: '',
      e: '',
      r_area: '',
      block: pbBlock,
      stype: '',
      position: position || 0
    }
  }
  const subarr = data.epg
  let tjPingback
  let chnid
  const webpTag = canUseWebp()
  for (let j = 0; j < subarr.length; j += 1) {
    chnid = subarr[j].chnId
    let paramConnTag = '?'
    // const videoUrl = rebuildPlayUrl(subarr[j].qipuIdStr) || ''
    let videoUrl =
      rebuildPlayUrl(subarr[j].playLocSuffix) ||
      rebuildPlayUrl(subarr[j].albumLocSuffix) ||
      rebuildPlayUrl(subarr[j].qipuIdStr) ||
      ''
    // if (changePcAlbumMod(modeCode) || isMobile) {
    videoUrl =
      rebuildAlbumUrl(subarr[j].albumLocSuffix) || changeAlbumUrl(videoUrl)
    // }
    if (videoUrl.indexOf('?') !== -1) {
      paramConnTag = '&'
    }
    let pbParam = `${paramConnTag}frmrp=${rpage}&frmb=${pbBlock}&frmrs=${j}`
    if (subarr[j].isAutoRecommend) {
      tjPingback = subarr[j].pingback
      pbParam = `${paramConnTag}frmrp=${rpage}&frmb=R:${resId}&frmrs=${j}`
    }
    if (!pbMsg) {
      pbParam = ''
    }
    let albumPic = subarr[j].albumPic
    if (/.gif$/.test(albumPic)) {
      // 为了兼容gif无图增加的逻辑
      albumPic = subarr[j].albumPic
    } else if (webpTag) {
      albumPic = subarr[j].albumWebpPic || subarr[j].albumPic
    }
    if (!subarr[j].publishTime) {
      subarr[j].publishTime = ''
    }
    newData.videos.push({
      name: subarr[j].name,
      desc: subarr[j].desc,
      chnId: subarr[j].chnId,
      isAutoRecommend: subarr[j].isAutoRecommend || false,
      albumPic: removeProtocol(albumPic),
      albumPicBak: removeProtocol(subarr[j].albumPic), // 增加兜底字段，如果竖图为/common路径，则横图用albumPicBak字段
      url: videoUrl + pbParam,
      sourceCode: subarr[j].sourceCode,
      tvCount: subarr[j].tvCount,
      total: subarr[j].total,
      year: subarr[j].publishTime.substring(0, 4),
      id: subarr[j].qipuId,
      vipInfo: subarr[j].vipInfo,
      // category: subarr[j].epg.categoryTags.unifiedType || '',
      region: subarr[j].categoryTags ? subarr[j].categoryTags.Place : '',
      updateDesc: {
        chnId: subarr[j].chnId || '',
        total: subarr[j].total || 0,
        tvCount: subarr[j].tvCount,
        publishTime: subarr[j].publishTime
      },
      categoryTagMap: subarr[j].categoryTagMap || {},
      defaultEpi: subarr[j].defaultEpi || {},
      focus: subarr[j].focus,
      playControl: subarr[j].controlStatus === 0,
      pingback: {
        bstp: 18,
        ht: 0,
        index: (sPage - 1) * 20 + j,
        block,
        s_bkt: data.bkt,
        position: j + 1,
        s_ct: sCt,
        s_page: sPage,
        s_st: data.time,
        s_e: data.eventId,
        s_source: sSource,
        s_tag: sTag,
        rpage,
        s_mode: sMode,
        colPb: `rpage=${rpage}&block=filter_result&rseat=collection`
      }
    })
  }
  if (tjPingback) {
    newData.pingback.block = 'R:' + resId
    newData.pingback.bkt = tjPingback.bucket
    newData.pingback.e = tjPingback.eventId
    newData.pingback.r_area = tjPingback.r_area
    newData.pingback.stype = 2
    newData.pingback.pbType = 'tj'
  }
  newData.chnid = chnid
  return newData
}
