/* eslint-disable no-unused-vars */
import React from 'react'
import Document, { Head, Main, NextScript, Html } from 'next/document'
import { ServerStyleSheet } from 'styled-components'
import { playerJS } from '@/utils/player/playerKit'
import { getMobileType, isInElectron } from '@/kit/device'
import { iqSwitchPlatformId } from '@/kit/common'
import { getTimeZone } from '@/utils/common'
import { getCookies } from '@/kit/cookie'
import {
  webpreData,
  getChannelPreDataUrl,
  isEsrRender
} from '@/utils/esrRender'

export default class MyDocument extends Document {
  static async getInitialProps(ctx) {
    const sheet = new ServerStyleSheet()
    const originalRenderPage = ctx.renderPage
    try {
      ctx.renderPage = () =>
        originalRenderPage({
          enhanceApp: App => props => sheet.collectStyles(<App {...props} />)
        })
      const initialProps = await Document.getInitialProps(ctx)
      const styleTags = sheet.getStyleElement()
      return { ...initialProps, styleTags, ctx }
    } finally {
      sheet.seal()
    }
  }

  render() {
    const { ctx } = this.props
    const { pathname } = ctx
    const lang = ctx.query.lang ? ctx.query.lang.split('_')[0] || 'en' : 'en'
    const user = ctx.query.user || ''
    const headers = ctx.req.headers
    let isNotSeo = true
    if (headers['is-robot-query']) {
      // 增加对seo特殊处理逻辑
      isNotSeo = false
    }

    const pingbackScript = isNotSeo ? (
      <script
        defer
        type="text/javascript"
        // src="/static/js/global_pcw_qa_v18.js"
        src="//static.iqiyi.com/lequ/20240701/global_pcw_qa_v18.min.js"
      />
    ) : (
      ''
    )
    const performanceHeaderJS = (
      <script
        dangerouslySetInnerHTML={{
          __html: `
          window.perFirstStartTime = window.performance.now()
          window.getCookie = function (name){
              var arr,reg=new RegExp("(^| )"+name+"=([^;]*)(;|$)");
              if(arr=document.cookie.match(reg)) 
              return unescape(arr[2]);
              else 
              return null;
            }
        `
        }}
      />
    )
    const performanceBodyJS = (
      <script
        dangerouslySetInnerHTML={{
          __html: `
          window.perBodyFirstStartTime = window.performance.now()
        `
        }}
      />
    )
    // const playerPreData = (
    //   <script
    //     id="__WEBPLAYDATA__"
    //     type="application/json"
    //     dangerouslySetInnerHTML={{
    //       __html: `{replaceData}`
    //     }}
    //   />
    // )

    // const channelPreData = (
    //   <script
    //     id="__CHANNELDATA__"
    //     type="application/json"
    //     dangerouslySetInnerHTML={{
    //       __html: `{http://api.intl.online.qiyi.qae/page/pcw_common?app_k=appk_pcw&app_lm=${
    //         global.modeCode
    //       }&app_t=i18nvideo&app_v=3.1.5&card_v=v1&dev_os=${getMobileType()}&dev_ua={replaceUserAgent}&lang=${
    //         global.langCode
    //       }&mod=${global.modeCode}&net_sts=1&page_st=${
    //         global.pageSt
    //       }&platform_id=${iqSwitchPlatformId()}&psp_cki={replacePspCki}&psp_status={replacePspStatus}&psp_uid={replacePspUid}&qyid={replaceDeviceId}&req_sn=1682303983920&req_times=1&secure_p=pcw&secure_v=1&sid=${getCookies(
    //         'QC005',
    //         ctx
    //       ) + Date.now()}&timezone=${getTimeZone(
    //         getCookies('mod')
    //       )}&pg_size=1&channel_id=${
    //         global.chnId
    //       }&customized=1&pg_num=1&card_id=${
    //         global.focusCardId ? global.focusCardId + ',' : ''
    //       }${global.firstCardId}}`
    //     }}
    //   />
    // )
    const performanceBodyEndJS = (
      <script
        dangerouslySetInnerHTML={{
          __html: `
          window.perBodyEndFirstStartTime = window.performance.now()
        `
        }}
      />
    )
    const sdk = (
      <>
        <style id="setSizeStyle" />
        <script
          dangerouslySetInnerHTML={{
            __html: `
              function randomString(e) {    
                  e = e || 32;
                  var t = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678",
                  a = t.length,
                  n = "";
                  for (i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a));
                  return n
              }

              window._performance = {
                  eventId: randomString(6)
              }
            `
          }}
        />
        <script
          dangerouslySetInnerHTML={{
            __html: `
            var topBannerHeight = 0
            function getClientInfo() {
              return {
                clientWidth: document.documentElement.clientWidth || document.body.clientWidth,
                clientHeight: document.documentElement.clientHeight || document.body.clientHeight
              }
            }

            function constructStyleString(selector, style) {
              var res = selector + ' {'
              var ks = Object.keys(style)
              for (var i = 0; i < ks.length; i++) {
                res += ks[i] + ': ' + style[ks[i]] + ';'
              }
              return res + '}'
            }

            function setSize(topBannerH) {
              var clientInfo = getClientInfo()
              var clientWidth = clientInfo.clientWidth;
              var clientHeight = clientInfo.clientHeight;

              topBannerHeight = topBannerH || 0;
              var baseAllSizeInfo = {
                1680: {
                  bottomGap: 90 + 44,
                  topGap: 70 + 20 + topBannerHeight,
                  rightSectionWidth: 366,
                  leftRightPadding: 80 * 2,
                  maxWidth: 366 + 1154 + 80 * 2,
                  minWidth: 366 + 688 + 80 * 2,
                },
                1280: {
                  bottomGap: 76 + 44,
                  topGap: 70 + 10 + topBannerHeight,
                  rightSectionWidth: 280,
                  leftRightPadding: 60 * 2,
                  maxWidth: 280 + 1154 + 60 * 2, // 其实是不会触发
                  minWidth: 280 + 688 + 60 * 2,
                },
                1024: {
                  bottomGap: 76 + 44,
                  topGap: 60 + 10 + topBannerHeight,
                  rightSectionWidth: 216,
                  leftRightPadding: 60 * 2,
                  maxWidth: 216 + 1154 + 60 * 2, // 其实是不会触发
                  minWidth: 216 + 688 + 60 * 2,
                }          
              }

              // 分辨率低于1024以下不处理
              if (clientWidth < 1024) {
                setSizeStyle.innerHTML = ''
                return
              }

              var curSizeInfo = baseAllSizeInfo[1280]
              if (clientWidth >= 1680) {
                curSizeInfo = baseAllSizeInfo[1680]
              } else if (clientWidth >= 1280) {
                curSizeInfo = baseAllSizeInfo[1280]
              } else if (clientWidth >= 1024) {
                curSizeInfo = baseAllSizeInfo[1024]
              } 

              let leftSectionWidth = clientWidth - curSizeInfo['leftRightPadding'] - curSizeInfo['rightSectionWidth'] // 以宽度计算 播放器理论宽度
              let leftSectionHeight = (leftSectionWidth * 9) / 16 // 以宽度计算 播放器理论高度
              let totalWidth = 0

              if (leftSectionHeight + curSizeInfo['topGap'] + curSizeInfo['bottomGap'] > clientHeight) {
                // 说明以宽度计算，此时放不下,
                leftSectionHeight = clientHeight - curSizeInfo['topGap'] - curSizeInfo['bottomGap'] // 以高度计算 播放器理论高度
                leftSectionWidth = (leftSectionHeight * 16) / 9 // 以高度计算 播放器理论宽度

                totalWidth = leftSectionWidth + curSizeInfo['rightSectionWidth'] + curSizeInfo['leftRightPadding'] //播放器容器理论宽度
              } else {
                totalWidth = 0
              }

              if (totalWidth > 0) {
                if (totalWidth < curSizeInfo['minWidth']) {
                  totalWidth = curSizeInfo['minWidth']
                }
                if (totalWidth > curSizeInfo['maxWidth']) {
                  totalWidth = curSizeInfo['maxWidth']
                }

                var styleString = constructStyleString('#setSizeStyle', {
                  width: totalWidth + 'px'
                })

                styleString += constructStyleString('.intl-video-wrap', {
                  width: totalWidth - curSizeInfo['rightSectionWidth'] - curSizeInfo['leftRightPadding'] + 'px'
                })

                setSizeStyle.innerHTML = styleString
              } else {
                setSizeStyle.innerHTML = ''
              }
            }
            window.setSize = setSize
            if (window.location.href.indexOf('/play') > -1) {
              window.setSizeStyle=document.getElementById("setSizeStyle")
              setSize()
              window.addEventListener('resize', function() {
                setSize()
              })
            }
          `
          }}
        />
        <script
          dangerouslySetInnerHTML={{
            __html: `
          !function(n,e){var t,o,i,c=[],f={passive:!0,capture:!0},r=new Date,a="pointerup",u="pointercancel";function p(n,c){t||(t=c,o=n,i=new Date,w(e),s())}function s(){o>=0&&o<i-r&&(c.forEach(function(n){n(o,t)}),c=[])}function l(t){if(t.cancelable){var o=(t.timeStamp>1e12?new Date:performance.now())-t.timeStamp;"pointerdown"==t.type?function(t,o){function i(){p(t,o),r()}function c(){r()}function r(){e(a,i,f),e(u,c,f)}n(a,i,f),n(u,c,f)}(o,t):p(o,t)}}function w(n){["click","mousedown","keydown","touchstart","pointerdown"].forEach(function(e){n(e,l,f)})}w(n),self.perfMetrics=self.perfMetrics||{},self.perfMetrics.onFirstInputDelay=function(n){c.push(n),s()}}(addEventListener,removeEventListener);  
          `
          }}
        />

        {/* 龙源统计新用户需求,uid为空则设置QC173为1,PV发送成功后则值变为0 */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
          (function (win) { var localStorageQC005 = window.localStorage && localStorage.getItem("QC005"); var setRaw = function (key, value, options) { options = options || {}; var expires = options.expires; if ("number" == typeof options.expires) { expires = new Date(); expires.setTime(expires.getTime() + options.expires) } document.cookie = key + "=" + value + (options.path ? "; path=" + options.path : "") + (expires ? "; expires=" + expires.toGMTString() : "") + (options.domain ? "; domain=" + options.domain : "") + (options.secure ? "; secure" : "") }; var getRaw = function (key) { var reg = new RegExp("(^| )" + key + "=([^;]*)(;|\x24)"), result = reg.exec(document.cookie); if (result) { return result[2] || null } return null }; var set = function (key, value, options) { setRaw(key, encodeURIComponent(value), options) }; var get = function (key) { var value = getRaw(key); if ("string" == typeof value) { value = decodeURIComponent(value); return value } return null }; var setNewUser = function (isNewuser) { set("QC173", isNewuser, { path: "/", domain: "iq.com", expires: 90 * 365 * 24 * 3600 * 1000 }) }; var getLocal005 = function () { if (localStorageQC005) { set("QC005", localStorageQC005, { path: "/", domain: "iq.com", expires: 90 * 365 * 24 * 3600 * 1000 }); setNewUser(0); return true } return false }; var setCookie005 = function () { const chars = [ '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f' ]; let nums = ''; const expiresDate = new Date(); expiresDate.setTime(expiresDate.getTime() + 90 * 365 * 24 * 3600 * 1000); for (let i = 0; i < 32; i++) { const id = Math.floor(Math.random() * 16); nums += chars[id]; } return nums; }; var generate = function () { if (!getLocal005()) { uid = get('QC005');if (!uid) { uid = setCookie005(); } set("QC005", uid, { path: "/", domain: "iq.com", expires: 90 * 365 * 24 * 3600 * 1000 }); if (window.localStorage) { localStorage.setItem("QC005", uid) } setNewUser(1) } }; if (!get("QC005")) { generate() } else { if (localStorageQC005) { set("QC005", localStorageQC005, { path: "/", domain: "iq.com", expires: 90 * 365 * 24 * 3600 * 1000 }) } setNewUser(0) } })(window)
        `
          }}
        />
        <script
          dangerouslySetInnerHTML={{
            __html: `
           window.isNotSeo = ${isNotSeo}
        `
          }}
        />
      </>
    )
    const tvPing = (
      <>
        <script
          dangerouslySetInnerHTML={{ __html: ` window.isNotSeo = ${isNotSeo}` }}
        />
      </>
    )
    const polyfill = (
      <>
        {/* scrollTo兼容低版本 */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
            if (!window.scrollTo) {window.scrollTo = function(option) {window.scrollLeft = option.left;window.scrollTop = option.top}}
            if (!document.body.scrollTo) {Element.prototype.scrollTo = function(option) {this.scrollLeft = option.left;this.scrollTop = option.top}}
            `
          }}
        />
      </>
    )
    const monitorJs = (
      <script
        dangerouslySetInnerHTML={{
          __html: `
          (function(w) {
            w._reqQueue = []
            let _hasLoad
            let _reqTimmer
            let _lastErrorArgs
            function getCookie(name){
              var arr,reg=new RegExp("(^| )"+name+"=([^;]*)(;|$)");
              if(arr=document.cookie.match(reg)) 
              return unescape(arr[2]);
              else 
              return null;
            }
            function send() {
              if (!_reqTimmer) {
                _reqTimmer = setTimeout(function () {
                  if (!_reqTimmer) return
                  clearTimeout(_reqTimmer)
                  _reqTimmer = null
                  while (data = w._reqQueue.shift()) {
                    var image = new Image()
                    const url = '//msg-intl.qy.net/qos?p1=1_10_222&t=9&ct=js_error&u=' + getCookie(
                      'QC005') + '&mod=' + getCookie('mod') + '&lang=' + getCookie('lang') + '&diy_host=' + window.location.hostname + '&diy_errormsg=' + data.msg + '&ec=' + data.name + '&diy_extra=' + data.stack + '&diy_filename=' + data.filename + '&diy_site=' + data.position + '&diy_selector=' + data.tagname
                    image.src = url
                    image = null;
                  }
              }, 3000)
              }
            }
            function errorHandler(event) {
              // 防止重复监听导致的重复上报，一秒内的重复错误自动过滤
              var currArgs = (event + (Date.now() / 1e3 | 1));
              if (_lastErrorArgs === currArgs) return
              _lastErrorArgs = currArgs
              // 相同错误增加times，减少服务负载
              var last = w._reqQueue[w._reqQueue.length - 1];
              if (last && last.name === 'ScriptError' && event.message === last.msg) {
                last.times++;
                return
              }
              if (event.target && (event.target.src || event.target.href)) {
                w._reqQueue && w._reqQueue.push({
                  msg: event.message || '',
                  name: 'resourceError',
                  times: 1,
                  filename: event.target.src || event.target.href,
                  tagname: event.target.tagName
                })
              } else {
                w._reqQueue && w._reqQueue.push({
                  msg: event.message || '',
                  stack: (event.error && event.error.stack) || '',
                  name: 'ScriptError',
                  times: 1,
                  filename: event.filename,
                  position: (event.lineno || 0) + ":" + (event.colno || 0)
                })
              }
              if (_hasLoad) {
                send()
              }
            }
            w.addEventListener && w.addEventListener('error', function (event) { errorHandler(event) }, true)
            w.addEventListener && w.addEventListener('load', function () {
              _hasLoad = true
              send()
            })
          })(window);
        `
        }}
      />
    )

    // 删除盗版插件的button样式
    const deleteCuanGaiHou = (
      <>
        <script
          dangerouslySetInnerHTML={{
            __html: `;(function() {
              setTimeout(function(){
                try {
                  var cuangaihoudom1 = document.querySelector('#zhmIcon')
                  if (cuangaihoudom1) {
                    cuangaihoudom1.remove()
                  }
                  var cuangaihoudom2 = document.querySelector('.vip_icon')
                  if (cuangaihoudom2 && cuangaihoudom2.parentElement && cuangaihoudom2.parentElement.id.indexOf('vip_jx_box') !== -1) {
                    cuangaihoudom2.parentElement.remove()
                  }
                } catch (error) {
                  
                }
              }, 2000)
            })()`
          }}
        />
      </>
    )

    // const lang = global.langCode || 'en'

    // 按需加载播放器js文件
    const _playerJS = playerJS(ctx.pathname)

    // Deng Xiaofu esr render
    const esrRender = isEsrRender(ctx.pathname)
    const channelPreDataUrl = getChannelPreDataUrl({
      modeCode: global.modeCode,
      langCode: global.langCode,
      pageSt: global.pageSt,
      chnId: global.chnId,
      focusCardId: global.focusCardId,
      firstCardId: global.firstCardId,
      ctx
    })
    const isElectron = isInElectron()

    return (
      <Html lang={lang}>
        {performanceHeaderJS}
        {_playerJS}
        <Head>
          {esrRender ? null : webpreData}
          {esrRender ? null : channelPreDataUrl}
          <link rel="preload" href={global.firstFocusImg} as="image" />
          <link rel="preload" href={global.firstFocusBg} as="image" />
          {/* <meta name="robots" content="noindex,nofollow" /> */}
          <meta
            name="google-site-verification"
            content="FGhS66EHlVHk15YWu1wZc6oVIb4rDffnXU-MhvehrJ0"
          />
          <meta
            name="naver-site-verification"
            content="06f41da904c08d7d1cde33b625db8ba86373e91f"
          />
          {user ? null : <link rel="manifest" href="/manifest.json" />}
          <link rel="icon" href="/favicon.ico" />
          <link rel="shortcut icon" href="/favicon.ico" type="image/ico" />
          {/* fix /apple-touch-icon 404 */}
          <link
            rel="apple-touch-icon-precomposed"
            sizes="152x152"
            href="/apple-touch-icon-152_152.png"
          />
          <link
            rel="apple-touch-icon-precomposed"
            sizes="120x120"
            href="/apple-touch-icon-120_120.png"
          />
          <link
            rel="apple-touch-icon-precomposed"
            sizes="114x114"
            href="/apple-touch-icon-114_114.png"
          />
          <link
            rel="apple-touch-icon-precomposed"
            sizes="72x72"
            href="/apple-touch-icon-72_72.png"
          />
          <link
            rel="apple-touch-icon-precomposed"
            sizes="57x57"
            href="/apple-touch-icon-57_57.png"
          />

          <link rel="dns-prefetch" href="//cache-video.iq.com" />
          <link rel="dns-prefetch" href="//intl-api.iq.com" />
          <link rel="dns-prefetch" href="//pcw-api.iq.com" />
          <meta name="theme-color" content="#1a1a1a" />
          {this.props.styleTags}
          {pathname !== '/tv-register' ? monitorJs : null}
          {pathname !== '/tv-register' ? sdk : tvPing}
        </Head>
        <body
          style={{ fontFamily: 'SF Pro,Roboto,Noto Sans,sans-serif;' }}
          className={`${isElectron ? 'electron' : ''}`}
        >
          {performanceBodyJS}
          {/* 给seo用的，看看到底有多少seo在爬页面 */}
          {!isNotSeo ? (
            ''
          ) : (
            <a
              href="https://www.iq.com/play/best-episode"
              style={{ display: 'none' }}
            />
          )}
          <Main />
          <NextScript />
          {performanceBodyEndJS}
        </body>
        {/* qa统计 */}
        {pathname !== '/tv-register' ? pingbackScript : null}
        {polyfill}
        {deleteCuanGaiHou}
      </Html>
    )
  }
}
