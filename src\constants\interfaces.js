/* eslint-disable camelcase */
//  播放页视频相关
export const INTL_EPG_INFO = '//pcw.gateway.prod.online.qiyi.qae/api/epgInfo/'
// export const INTL_EPG_INFO = '//pcw.gateway.staging.online.qiyi.qae/api/epgInfo/'

export const CLIENT_INTL_EPG_INFO = '//pcw-api.iq.com/api/epgInfo/'
export const JUJI_EPISODE_LIST = '//pcw-api.iq.com/api/episodeListSource/'
export const JUJI_EPISODE_LIST_QAE =
  '//pcw.gateway.prod.online.qiyi.qae/api/episodeListSource/'
export const EPISODE_LIST =
  '//pcw.gateway.prod.online.qiyi.qae/api/episodeList/'
export const EPISODE_LIST_PAGING = '//pcw-api.iq.com/api/episode-list-paging'
export const EPISODE_LIST_PAGING_QAE =
  '//pcw.gateway.prod.online.qiyi.qae/api/episode-list-paging' // 线上链接
// export const EPISODE_LIST_PAGING_QAE =
//   '//pcw.gateway.staging.online.qiyi.qae/api/episode-list-paging' // staging 链接
export const JUJI_EPISODE_LIST_V2 = '//pcw-api.iq.com/api/v2/episodeListSource/'
export const JUJI_EPISODE_LIST_QAE_V2 =
  '//pcw.gateway.prod.online.qiyi.qae/api/v2/episodeListSource/'
export const EPISODE_LIST_PAGING_V2 =
  '//pcw-api.iq.com/api/v2/episode-list-paging'
export const EPISODE_LIST_PAGING_QAE_V2 =
  '//pcw.gateway.prod.online.qiyi.qae/api/v2/episode-list-paging' // 线上链接
// export const EPISODE_LIST_PAGING_QAE_V2 =
//   '//pcw.gateway.staging.online.qiyi.qae/api/v2/episode-list-paging' // staging 链接
export const SIMILAR_PLAY =
  '//pcw.gateway.prod.online.qiyi.qae/api/lego/similarplay'
export const SIMILAR_PLAY_JS = '//pcw-api.iq.com/api/lego/similarplay'
export const SUPER_SERIES_ALBUM =
  '//pcw.gateway.prod.online.qiyi.qae/api/super-series-album'
export const SUPER_SERIES_VIDEO =
  '//pcw.gateway.prod.online.qiyi.qae/api/super-series-video'

export const DECODE_ID = '//pcw.gateway.prod.online.qiyi.qae/api/decode/'
export const CLIENT_DECODE_ID = '//pcw-api.iq.com/api/decode/'
export const BODAN_LIST = '//pcw.gateway.prod.online.qiyi.qae/api/pls/'
export const PLAY_STRATEGY = '//pcw-api.iq.com/api/play-strategy/'
export const VIDEO_FATHER_COLLECTION_QAE =
  '//pcw.gateway.prod.online.qiyi.qae/api/video-father-collection/'

export const SHORT_SIMILAR_PLAY =
  '//pcw.gateway.prod.online.qiyi.qae/api/lego/short-similarplay'
export const SHORT_SIMILAR_PLAY_JS =
  '//pcw-api.iq.com/api/lego/short-similarplay'

export const FILM_REC = '//pcw.gateway.prod.online.qiyi.qae/api/lego/film-rec'

// http://wiki.qiyi.domain/pages/viewpage.action?pageId=955750503 播放页排行榜接口
export const PLAY_RANKING = '//pcw-api.iq.com/api/play-ranking'
export const PLAY_RANKING_SEVR =
  '//pcw.gateway.prod.online.qiyi.qae/api/play-ranking'

export const LIVE_INFO = '//pcw.gateway.prod.online.qiyi.qae/api/liveInfo/'
export const EPG_FILTER = '//pcw.gateway.prod.online.qiyi.qae/api/epg-filter'

// 多语言
export const LANG_PKG = '//pcw.gateway.prod.online.qiyi.qae/api/langPkg'
export const LANG_PKG_WEB = '//pcw-api.iq.com/api/langPkg'
export const LANG_PKG_STAGING =
  '//pcw.gateway.staging.online.qiyi.qae/api/langPkg'
export const MODE_LANG = '//pcw.gateway.prod.online.qiyi.qae/api/modeLang'

// 首页相关的
export const HOME_HISTORY_URL = `//pcw-api.iq.com/api/play/played` // test-intl-pcw.iqiyi.com //intl-pcw.iqiyi.com

// export const FOCUS_INFO_URL = `//pcw.gateway.prod.online.qiyi.qae/api/res`
export const HOME_RECOMMEND_URL = `//pcw-api.iq.com/api/lego/recommend` // pcw-api.iq.com
export const HOME_MIXTOPICS_URL = `//pcw-api.iq.com/api/lego/mixtopics` // pcw-api.iq.com

// 频道页相关（后期频道页首页合并）
// export const RES_INFO_URL = `//pcw.gateway.staging.online.qiyi.qae/api/res`
// export const INTL_RES_INFO_URL = `//pcw.gateway.staging.online.qiyi.qae/api/res`
// export const CHANNEL_TOPICLIST_URL = `//pcw.gateway.staging.online.qiyi.qae/api/lego/topiclist`
// export const CHANNEL_RECOMMEND_URL = `//pcw.gateway.staging.online.qiyi.qae/api/lego/recommend`
export const RES_INFO_URL = `//pcw.gateway.prod.online.qiyi.qae/api/res`

export const INTL_RES_INFO_URL = `//pcw-api.iq.com/api/res`
export const CHANNEL_TOPICLIST_URL = `//pcw-api.iq.com/api/lego/topiclist`
// export const CHANNEL_RECOMMEND_URL = `//pcw.gateway.prod.online.qiyi.qae/api/lego/recommend`
// TODO: 上线前更新成上线接口
// export const RES_PEOPLE = `//pcw.gateway.prod.online.qiyi.qae/api/res-people`

// 个人中心相关接口
export const DEL_HISTORY_URL = '//rcd.iq.com/apis/mbd/v2/delete.action'
export const USER_HISTORY_URL = '//rcd.iq.com/apis/mbd/i18n/download.action'
export const USER_HEADERPIC = '//paopaoupload.iqiyi.com/passport_headpic_upload'
export const UPDATE_USER_INFO = '//passport.iq.com/intl/user/update_info.action'

// 上传播放记录接口  http://wiki.qiyi.domain/pages/viewpage.action?pageId=*********#id-%E6%92%AD%E6%94%BE%E8%AE%B0%E5%BD%95APISPEC-%E5%9B%BD%E9%99%85%E7%AB%99-2.%E4%B8%8A%E4%BC%A0%E6%92%AD%E6%94%BE%E8%AE%B0%E5%BD%95
export const UPLOAD_HISTORY_URL = '//rcd.iq.com/apis/mbd/v2/upload.action'
// 搜索结果相关 http://wiki.qiyi.domain/pages/viewpage.action?pageId=*********
// http://wiki.qiyi.domain/pages/viewpage.action?pageId=*********
// export const SEARCH_URL = '//pcw.gateway.staging.online.qiyi.qae/api/search2'
// export const SEARCH_URL = '//test-pcw-api.iq.com/api/search2'
export const SEARCH_URL = '//pcw-api.iq.com/api/search2'
export const SEARCH_QAE_URL = '//pcw.gateway.prod.online.qiyi.qae/api/search2'

export const SUGGEST_URL = '//pcw-api.iq.com/api/suggest'
export const HOT_SEARCH_URL = '//pcw-api.iq.com/api/lego/hot'

// 登录相关接口
export const RENEW_URL = '//passport.iq.com/intl/sso/renew_authcookie.action'

// 会员相关接口
export const VIP_KEY_TOKEN = '//i.vip.iq.com/api/expcard/initToken.action'
export const REDEEM_VIP_RES = '//i.vip.iq.com/pay/exp_pay.action'
export const BATCH_AUTH = '//pcw-api.iq.com/api/vip/auth/batch'
export const BATCH_AUTH_DOMAIN =
  '//pcw.gateway.prod.online.qiyi.qae/api/vip/auth/batch'

// 会员营销位
export const VIP_SHOW_AD = '//pcw-api.iq.com/api/vip/show'
// export const VIP_SHOW_AD = '//test-pcw-api.iq.com/api/vip/show'

// 获取会员ptid（platform）和pid
// TO DO: 上线该成prod环境
export const MODE_PTID = '//pcw.gateway.prod.online.qiyi.qae/api/conf-id'
export const MODE_PTID_WEB = '//pcw-api.iq.com/api/conf-id'
// export const REDEEM_VIP_RES = '*************:8098/api/expcard/initToken.action'

// export const CHECK_VIP_CODE = '//i.vip.iq.com/api/expCard/detail'
// export const CHECK_VIP_CODE = 'http://*************:8049/api/expCard/detail'

// 用户信息接口
export const USER_INFO_URL_SSR = '//pcw.gateway.prod.online.qiyi.qae/api/pvvp'
export const USER_INFO_URL = '//pcw-api.iq.com/api/pvvp' // '//pcw-api.iq.com/api/pvvp' // test-pcw-api.iq.com
export const USER_DOBULLE_VIP_INFO_URL = '//pcw-api.iq.com/api/pvp' // '//pcw-api.iq.com/api/pvp' // test-pcw-api.iq.com

// 是否收藏
export const ISCOLLECTION =
  '//subscription.iq.com/dingyue/api/isSubscribed.action'
// 收藏
export const COLLECTIONADD =
  '//subscription.iq.com/dingyue/api/subscribe.action'
// 取消收藏
export const COLLECTIONCANCEL =
  '//subscription.iq.com/dingyue/api/unsubscribe.action'
// 收藏列表
export const COLLECTIOMLIST =
  '//subscription.iq.com/apis/watchlater/i18n/groupList.action'
// 删除（批量）
export const DELCOLLECTION =
  '//subscription.iq.com/apis/mbd/reg/deletebatch.action'
// 天秤ABtest
export const AB_TEST = '//pcw.gateway.prod.online.qiyi.qae/api/ab/experiment'
// 热播榜
export const HOT_PLAY = '//pcw.gateway.prod.online.qiyi.qae/api/hotPlay' // test环境

/**
 * NEW_VIP
 * VIP代码库迁移接口
 */
// 套餐查询接口
// http://wiki.qiyi.domain/pages/viewpage.action?pageId=559481484
// export const intlVipCheckoutInterface =
//   '//i.vip.iq.com/client/store/i18n/pcw/checkout'
export const intlVipCheckoutInterface =
  '//global.vip.iq.com/vip-global-store/external/pcw/checkout'

// 新的套餐查询接口  http://wiki.qiyi.domain/pages/viewpage.action?pageId=559481484
// export const newintlVipCheckoutInterface =
//   '//i.vip.iq.com/client/store/i18n/pcw/ab/checkout'
export const newintlVipCheckoutInterface =
  '//global.vip.iq.com/vip-global-store/external/pcw/checkout'
// '/client/store/i18n/pcw/ab/checkout?user=yyh'
// 自动续费查询接口
// http://wiki.qiyi.domain/pages/viewpage.action?pageId=564464401
// http://wiki.qiyi.domain/pages/viewpage.action?pageId=782445118
export const intlVipSubscriptionInterface =
  // '//i.vip.iq.com/usergateway/services/i18n/query'
  '//serv.vip.iq.com/usergateway/services/i18n/query/v2'

// 取消自动续费接口
// http://wiki.qiyi.domain/pages/viewpage.action?pageId=564464474
// http://wiki.qiyi.domain/pages/viewpage.action?pageId=782445178
export const intlVipCancelSubscriptionInterface =
  // '//i.vip.iq.com/usergateway/services/i18n/cancelAutoRenew.action'
  '//serv.vip.iq.com/usergateway/services/i18n/cancelAutoRenew/v2'

// http://wiki.qiyi.domain/pages/viewpage.action?pageId=1425114789
export const salseVip = '//api.iq.com/control/salse_vip'

// 支付结果查询接口
// http://wiki.qiyi.domain/pages/viewpage.action?pageId=568918057
export const intlVipPayResultInterface =
  '//i.vip.iq.com/usergateway/payresult/i18n/query.action'
// '/usergateway/payresult/i18n/query.action?user=yyh'
// '//global-user-gateway-api20220414220631.test.qiyi.qae/usergateway/payresult/i18n/query.action?user=yyh'

// 支付结果页（card化版本）query接口 http://wiki.qiyi.domain/pages/viewpage.action?pageId=1451065521
// export const QUERY_CARD = '//i.vip.iq.com/payresult/i18n/queryV2'
export const QUERY_CARD =
  '//global.vip.iq.com/vip-global-payresult/external/card/queryV2'
// 支付接口
// http://wiki.qiyi.domain/pages/viewpage.action?pageId=*********
// export const doPayInterface = '//i.vip.iq.com/pay/dopay.action'
export const doPayInterface =
  '//global.vip.iq.com/vip-global-trade/external/pay/dopay'
// '/pay/dopay.action?user=yyh' // '//i.vip.iq.com/pay/dopay.action'
// '//vip-global-trade-api20220414220631.test.qiyi.qae/pay/dopay.action'
// 银行卡支付接口
// http://wiki.qiyi.domain/pages/viewpage.action?pageId=*********
// https://iq.feishu.cn/wiki/IbjJwRKT3itU10k1I3AcBPkzn6g?open_in_browser=true
export const bankDoPayInterface =
  // 'http://inter-test.pay.qiyi.domain/pay-product-international/mastercard/m4m/doPay'
  // '//pay-test.iqiyi.com/pay-product-international/mastercard/m4m/doPay'
  '//pay.iq.com/pay-product-international/mastercard/m4m/doPay'
// '//pay.iq.com/pay-product-international/mastercard/doPay'

export const SUBMIT_PAY_PHONE =
  '//pay.iq.com/pay-product-international/intl/coda/gopay/submitphone'
// '//inter-test.pay.qiyi.domain/pay-product-international/intl/coda/gopay/submitphone'

//  查询用户绑卡信息 https://iq.feishu.cn/wiki/Y5vFw7xY5iAcWEk7gsDcYspCnAc
// export const USER_BOUND_CARD =
//   '//pay-test.iqiyi.com/pay-product-international/mastercard/m4m/queryCardInfos'
export const USER_BOUND_CARD =
  '//pay.iq.com/pay-product-international/mastercard/m4m/queryCardInfos'

// 国际站的播放中间购买浮层数据服务
// http://wiki.qiyi.domain/pages/viewpage.action?pageId=*********
// export const contentBuyInterface =
//   '//serv.vip.iq.com/vip-global-query/contentBuy.action'
export const contentBuyInterface =
  '//serv.vip.iq.com/vip-global-query/contentBuy.action'
// '//intl-serv.vip.iqiyi.com/vip-global-query/contentBuy.action'
// 功能配置
// export const FUNCS_API = '//pcw-api.iq.com/api/funcs'
// export const FUNCS_API_QAE = '//pcw.gateway.prod.online.qiyi.qae/api/funcs'
// export const FUNCS_API_STAGE = '//pcw.gateway.staging.online.qiyi.qae/api/funcs'

// 功能配置：http://pms.qiyi.domain/browse/GLOBALREQ-5321
export const FUNCS_API = '//api.iq.com/control/feature_switch'
export const FUNCS_API_QAE =
  '//intl-mixer.online.qiyi.qae/control/feature_switch'
// export const FUNCS_API_QAE = '//api-test.iq.com/control/feature_switch' // testtest
export const FUNCS_API_TEST = '//api-test.iq.com/control/feature_switch'

// 分享解锁
export const SHAREUNLOCK_URL = '//pcw-api.iq.com/api/vip/act/unlock' // '//test-intl-pcw.iqiyi.com/api/vip/act/unlock' // /'//intl-pcw.iqiyi.com/api/vip/act/unlock'

// 解锁接口
// http://intl-activity-external-api-test.online.qiyi.qae/activity_external/swagger-ui.html#/
export const UNLOCK_USER_INFO =
  '//api.iq.com/activity_external/advanced_demand/drama_unlock' // api-test.iq.com

export const GET_SUBTITLE_LANG =
  '//pcw.gateway.prod.online.qiyi.qae/api/subtitle-language'
// 翻译列表接口
export const TRANS_MENU_LIST =
  '//api.iq.com/intl-translate-crowdsourcing-pcw/subtitle/query-delivery-menu'

export const FULL_EPISODE_TRANS_LIST =
  '//api.iq.com/intl-translate-crowdsourcing-pcw/full-episode-subtitle/query-delivery-menu'

export const GET_SUBTITLE_DETAIL =
  '//api.iq.com/intl-translate-crowdsourcing-pcw/subtitle/query-delivery-detail'

export const TRANS_RECOMMEND_LIST =
  '//api.iq.com/intl-translate-crowdsourcing-pcw/video/query-explore-video'

export const TRANS_ENCODE_QIPUID =
  '//api.iq.com/intl-translate-crowdsourcing-pcw/video/encode-qipu-id'

export const TRANS_RECOMMEND_FULL_EPISODE = '//pcw-api.iq.com/api/trans/rec'

// 一次请求多条资源位数据 http://wiki.qiyi.domain/pages/viewpage.action?pageId=776145851
// export const RES_BATCH = '//pcw.gateway.staging.online.qiyi.qae/api/res-batch'
export const RES_BATCH = '//pcw.gateway.prod.online.qiyi.qae/api/res-batch'

// 带榜单信息的资源位
export const RES_CHART = '//pcw.gateway.prod.online.qiyi.qae/api/res-chart'

// export const RES_BATCH_V2 =
//   '//pcw.gateway.prod.online.qiyi.qae/api/v2/res-batch'

// tabInfo接口 http://wiki.qiyi.domain/pages/viewpage.action?spaceKey=int&title=026+-+TABINFO
export const TEST_TAB_INFO = '//test-pcw-api.iq.com/api/tabinfo'
export const TAB_INFO = '//pcw.gateway.prod.online.qiyi.qae/api/tabinfo'
// export const TAB_INFO = '//pcw.gateway.staging.online.qiyi.qae/api/tabinfo'

// Feedback api
export const FEEDBACK_API = '//api-feedback.iq.com/feedbacks'
// 合规数据接口
export const GDPR_SAVE = '//api.iq.com/api-gdpr/device/save'
// export const GDPR_FETCH = '//intl-gdpr-api.online.qiyi.qae/api-gdpr/device-info' // 内网接口
export const GDPR_FETCH = '//api.iq.com/api-gdpr/device-info'
export const GDPR_UID_FETCH = '//api.iq.com/api-gdpr/user-device-info'
// h5选集切换跳转策略
export const H5_SWITCH_STRATEGY = '//pcw-api.iq.com/api/conf/switch/strategy'
// export const H5_SWITCH_STRATEGY = '//test-pcw-api.iq.com/api/conf/switch/strategy'
// 会员营销位 http://wiki.qiyi.domain/pages/viewpage.action?pageId=837554435
export const VIP_SELL = '//intl-pcw.iqiyi.com/api/vip/show'

export const GET_IMG_COLOR = '//servo.qiyi.domain/palette'

// export const BANNER_AD =
//   '//pcw.gateway.staging.online.qiyi.qae/api/conf/banner/ad'
export const BANNER_AD = '//pcw-api.iq.com/api/conf/banner/ad'

/**
 * 评分相关接口
 */
// 发布评分
export const PUBLISH_SCORE = '//pcw-api.iq.com/api/score-push'
// 获取评分
export const GET_SCORE = '//pcw-api.iq.com/api/video-score-info'
export const GET_SCORE_QAE =
  '//pcw.gateway.prod.online.qiyi.qae/api/video-score-info'
// 获取用户评分
export const GET_USER_SCORE = '//pcw-api.iq.com/api/user-score-info'
// 筛选标签列表
// export const FILMLIB_LABEL_FILTER='//pcw-api.iq.com/api/chnList'
export const FILMLIB_LABEL_FILTER =
  '//pcw.gateway.prod.online.qiyi.qae/api/chnList'

// 片库
export const FILMLIB_LIST = '//pcw-api.iq.com/api/albumList'
// export const FILMLIB_LIST = '//pcw.gateway.prod.online.qiyi.qae/api/albumList'

/**
 * 影人信息页接口
 */
// 获取人物信息和播单接口
// export const GET_STAR_INFO = '//pcw-api.iq.com/api/people-collection-list'
// export const GET_STAR_INFO = '//pcw-api.iq.com/api/people-collection-list'
// 批量获取播单接口
// TODO: 记得切换接口
// export const GET_BODAN = '//pcw-api.iq.com/api/people-pls'

// pcw-api.iq.com/api/api/people-pls
// 获取人物作品集接口
// export const GET_STAR_PLAY_LIST = '//pcw-api.iq.com/api/people-play-list'
// export const GET_STAR_PLAY_LIST = '//test-pcw-api.iq.com/api/people-play-list'
// 获取关联人物接口

export const GET_STAT_CON_INFO = '//pcw-api.iq.com/api/people-related-play-list'

// 获取即将上线接口 http://wiki.qiyi.domain/pages/viewpage.action?pageId=935104372
// export const UP_COMING = '//pcw.gateway.staging.online.qiyi.qae/api/up-comming'
// export const UP_COMING = '//pcw.gateway.prod.online.qiyi.qae/api/up-comming'

// 短带长接口  http://wiki.qiyi.domain/pages/viewpage.action?pageId=1024920745
// export const SHORT_TO_LONG = '//pcw-api.iq.com/api/short-to-long'

// ip获取，用于pingback：b_ext:{"bip":xxxx}
export const GET_IP = '//pcw-api.iq.com/ip'
export const IPV4 = '//pcw-api.iq.com/api/v2/benchmark'
export const IPV6 = '//api-test.iq.com/api/v2/benchmark'
// export const GET_IP = '//test-pcw-api.iq.com/ip'

// 即将上线预约 http://wiki.qiyi.domain/pages/viewpage.action?pageId=783614702
// export const BATCH_ISRESERVE =
//   '//subscription.iq.com/services/subscribe/countAndState.htm'
export const BATCH_ISRESERVE =
  '//subscription.iq.com/services/subscribe/countAndState.htm'

// export const RESERVEADD = '//subscription.iq.com/services/subscribe/add.htm'
export const RESERVEADD = '//api.iq.com/subscribe/add'

// export const RESERVECANCEL =
//   '//subscription.iq.com/services/subscribe/cancel.htm'
export const RESERVECANCEL = '//api.iq.com/subscribe/cancel'

// 根据传入的qipuId，出参为拼接好的播放页url,http://wiki.qiyi.domain/pages/viewpage.action?pageId=1131055020
export const GET_TRANSFORM_URL = '//pcw-api.iq.com/api/get-transform-url'
// http://wiki.qiyi.domain/pages/viewpage.action?pageId=602673058
export const CANCEL_AUTORENEW_CONTENT_DETAIN =
  '//i.vip.iq.com/usergateway/services/i18n/proxy/interact'

// http://wiki.qiyi.domain/pages/viewpage.action?pageId=*********
export const CANCEL_AUTORENEW_GIFT_DETAIN =
  '//serv.vip.iq.com/global-event/autorenew-marketing/benefit'

// http://wiki.qiyi.domain/pages/viewpage.action?pageId=*********
export const CANCEL_AUTORENEW_GIFT_REGISTER =
  '//serv.vip.iq.com/global-event/autorenew-marketing/register'

export const CROSS_SITE_USER_UPDATE_API =
  '//passport.iq.com/intl/guide/cross_site_login_allow_update.action'

export const PASSPORT_USER_INFO_API = '//passport.iq.com/intl/user/info.action'
// http://wiki.qiyi.domain/pages/viewpage.action?pageId=1120607280
export const RESERVELIST = '//pcw-api.iq.com/api/subscribe-page'
// http://wiki.qiyi.domain/pages/viewpage.action?pageId=*********
export const SAVEAUTHTOKEN =
  '//intl-passport.iqiyi.com/intl/thirdparty/save_auth_token.action'

// 获取短连接 http://wiki.qiyi.domain/pages/viewpage.action?pageId=1249708512
export const TINYURL = '//pcw-api.iq.com/api/tinyurl'

// 可配置化接口

// export const PCW_COMMON = '//api.intl.online.qiyi.qae/page/pcw_common' // 内网
// export const PCW_COMMON = '//api.iq.com/page/pcw_common'

// 收银台新加的接口

// export const EXCHANGE_COUPON = '//intl-pcell.qiyi.domain/exchange-code/api/exchange'
// 用户风险等级 http://wiki.qiyi.domain/pages/viewpage.action?pageId=1118640264
export const USER_RISK = '//i.vip.iq.com/vip-global-coupon/user/risk'
// 优惠券兑换
export const EXCHANGE_COUPON = '//i.vip.iq.com/vip-global-coupon/user/get'

// 优惠券解冻接口 http://wiki.qiyi.domain/pages/viewpage.action?pageId=1118641475
export const FREEZE_COUPON = '//i.vip.iq.com/vip-global-coupon/user/unfreeze'
//  万事达3Ds验证流程初始化验证接口  http://wiki.qiyi.domain/pages/viewpage.action?pageId=1372560242 https://iq.feishu.cn/wiki/W5STwxpDuigjLXkURaYcxPUMnsh?open_in_browser=true
export const INIT_3DS_VERIFY =
  '//pay.iq.com/pay-product-international/mastercard/3ds2/initiate'
// '//pay-test.iqiyi.com/pay-product-international/mastercard/3ds2/initiate'

// export const PCW_COMMON_QAE = '//api-test.intl.online.qiyi.qae/page/pcw_common' // 内网 测试环境：//api-test.intl.online.qiyi.qae/page/pcw_common 线上环境: //api.intl.online.qiyi.qae/page/pcw_common
//  export const PCW_COMMON = '//api-test.iq.com/page/pcw_common' // 外网  测试环境: //api-test.iq.com/page/pcw_common  线上环境: //api.iq.com/page/pcw_common

export const PCW_COMMON_QAE = '//api.intl.online.qiyi.qae/page/pcw_common' // 内网 测试环境：//api-test.intl.online.qiyi.qae/page/pcw_common 线上环境: //api.intl.online.qiyi.qae/page/pcw_common
export const PCW_COMMON = '//api.iq.com/page/pcw_common'

// 获取评论接口
// export const FETCH_COMMENTS_API = // 测试地址
//   '//intl-sns.test.qiyi.qae/intl-be-comment-api/comment/list'

export const FETCH_COMMENTS_API =
  '//intl-sns.iq.com/intl-be-comment-api/comment/list'
export const PUBLISH_COMMENTS_API =
  '//intl-sns.iq.com/intl-be-comment-api/comment/publish'
export const ADD_PRAISE_COMMENTS_API =
  '//intl-sns.iq.com/intl-be-comment-api/comment/praise'
export const CANCEL_PRAISE_COMMENTS_API =
  '//intl-sns.iq.com/intl-be-comment-api/comment/cancelPraise'
export const DELETE_COMMENTS_API =
  '//intl-sns.iq.com/intl-be-comment-api/comment/del'

// 获取专辑单条播放记录
export const GET_ALBUM_PLAY_RECORD = '//pcw-api.iq.com/api/album/recentlyPlay'
// 资料页 预告片视频信息
// export const ALBUM_PREVIEW_VIDEO_INFO =
//   '//pcw.gateway.staging.online.qiyi.qae/api/first-prevue/'
export const ALBUM_PREVIEW_VIDEO_INFO =
  '//pcw.gateway.prod.online.qiyi.qae/api/first-prevue/'

// http://wiki.qiyi.domain/pages/viewpage.action?pageId=1*********
export const COLLECTION_LIST =
  '//pcw.gateway.prod.online.qiyi.qae/api/v2/collection'
export const CLIENT_COLLECTION_LIST = '//pcw-api.iq.com/api/v2/collection'

// 家长控制信息接口 http://wiki.qiyi.domain/pages/viewpage.action?pageId=*********#id-%E5%9B%BD%E9%99%85%E7%AB%99%E7%94%A8%E6%88%B7%E4%BF%A1%E6%81%AF-3.%E5%86%85%E5%A4%96%E7%BD%91%E6%A0%B9%E6%8D%AEauthcookie%E6%9F%A5%E8%AF%A2%E8%84%B1%E6%95%8F%E7%94%A8%E6%88%B7%E4%BF%A1%E6%81%AF%E6%8E%A5%E5%8F%A3
export const PARENTAL_INFO = '//passport.iq.com/intl/user/info.action'

// 家长控制分级列表 http://wiki.qiyi.domain/pages/viewpage.action?pageId=*********#id-/control/rating_list-%E4%BA%8C.%E5%BC%80%E5%85%B3
export const PARENTAL_LIST = '//intl-mixer.online.qiyi.qae/control/rating_list'

// 设置修改pin接口 http://wiki.qiyi.domain/pages/viewpage.action?pageId=1506223911#id-%E5%AE%B6%E9%95%BF%E6%8E%A7%E5%88%B6%E5%8A%9F%E8%83%BD-1.%E8%AE%BE%E7%BD%AE%E6%88%96%E4%BF%AE%E6%94%B9PIN%E7%A0%81
export const SET_PIN_URL = '//passport.iq.com/intl/parental/set_pin.action'
export const FIND_PIN_URL = '//passport.iq.com/intl/parental/find_pin.action'
export const VALIDATE_PIN_URL =
  '//passport.iq.com/intl/parental/validate_pin.action'
// 统一身份验证接口 http://wiki.qiyi.domain/pages/viewpage.action?pageId=47684564
export const VERIFY_URL = '//passport.iq.com/intl/secure/verify.action'

// Mixer PCW\H5\TV营销位接口文档  http://wiki.qiyi.domain/pages/viewpage.action?pageId=1478788658
export const MIXER_COMMON = '//api.iq.com/activity/player_text/'
// 代金券领用接口 http://wiki.qiyi.domain/pages/viewpage.action?pageId=1478790715
export const RESULT_COUPON =
  '//i.vip.iq.com/vip-global-coupon/user/payResultCoupon'

// 激活码风控init接口 http://wiki.qiyi.domain/pages/viewpage.action?pageId=1598820718
export const RISK_INIT =
  '//global.vip.iq.com/vip-global-coupon/external/expcard/initToken'
// '//intl-test.vip.iq.com/vip-global-coupon/expcard/initToken'
// '//i.vip.iq.com/vip-global-coupon/expcard/initToken'

// 激活码兑换服务 http://wiki.qiyi.domain/pages/viewpage.action?pageId=1599704374
export const CODE_EXCHANGE =
  '//global.vip.iq.com/vip-global-coupon/external/expcard/exchange'
// '//i.vip.iq.com/vip-global-coupon/expcard/exchange'
// '//intl-test.vip.iq.com/vip-global-coupon/expcard/exchange'

// http://wiki.qiyi.domain/pages/viewpage.action?pageId=1617134411
export const PREFETCH_AKAMAI_CDN = '//pcw-api.iq.com/api/v2/prefetch'

export const TV_INSTALL_GUIDE =
  '//intl-mixer.online.qiyi.qae/control/operate/tv_install_guide'

export const PARENER_ACTIVATE =
  '//openapi.vip.iq.com/global/partner/preorder/activate'

export const NEW_ONLINE_lIST_QAE =
  '//api.intl.online.qiyi.qae/view/preview_play'

export const NEW_ONLINE_lIST = '//api.iq.com/view/preview_play'

export const CODE_LOGIN = '//passport.iq.com/intl/actcode/code_login.action'
export const TOKEN_LOGIN_CONFIRM =
  '//passport.iq.com/intl/actcode/token_login_confirm.action'

export const GROUP_LIST = '//api.iq.com/playhistory/groupList'

export const PCW_PEOPLE_COMMON =
  '//pcw.gateway.prod.online.qiyi.qae/api/pcw_people_common'

// 收银台UG接口 UTM配置接口
// export const UG_UTM_DATA = '//pcw.gateway.staging.online.qiyi.qae/api/utmData'
// export const UG_UTM_DATA = '//test-pcw-api.iq.com/api/utmData' // pcw-api.iq.com
export const UG_UTM_DATA = '//pcw-api.iq.com/api/utmData'

// mark icon 接口
export const GENERIC_MARK = '//intl-mixer.online.qiyi.qae/control/generic_mark'
// mark icon  测试接口
// export const GENERIC_MARK = '//intl-mbd-views-test.online.qiyi.qae/control/generic_mark'

// TV会员改版跨端信息查询接口 https://iq.feishu.cn/wiki/NshVwino0iCJkOk1RuLcBISDn8e
export const TV_VIP_INFO =
  '//global.vip.iq.com/vip-global-store/external/proxy/otp/get'

// https://iq.feishu.cn/wiki/NQwSwNp4Ri6yZxkzHhycVU3Pnfh
export const CARD_RISK = '//openapi.vip.iq.com/global/partner/card/risk'
// https://iq.feishu.cn/wiki/AdWwwsaWiixrCskXT11cTxNLnug
export const CARD_REDEEM = '//openapi.vip.iq.com/global/partner/card/redeem'
// https://iq.feishu.cn/wiki/WrsDwzUvSiSrOekmqn0cXvD8nHr
export const CARD_QUERY = '//openapi.vip.iq.com/global/partner/card/query'
