import { rebuildPlayUrl, rebuildAlbumUrl, canUseWebp } from '@/kit/common'
import { removeProtocol } from '@/kit/url'
// import { formatPeriod } from '@/kit/number'
import {
  changeAlbumUrl,
  // changePcAlbumMod,
  supportUrlPbMod
} from '@/utils/video'

export const handleSearchIntentEpg = (data = {}, param = {}) => {
  const { mod, ctx } = param
  const webpTag = canUseWebp(ctx)
  const { intentEpg, starsInfo = {} } = data

  return intentEpg.reduce((t, cur, i) => {
    let pbParam = ''
    if (supportUrlPbMod(mod)) {
      pbParam = `frmrp=search&frmb=list`
    }
    // let videoUrl = rebuildPlayUrl(cur['qipuIdStr']) || ''
    let videoUrl =
      rebuildPlayUrl(cur.playLocSuffix) ||
      rebuildPlayUrl(cur.albumLocSuffix) ||
      rebuildPlayUrl(cur.qipuIdStr) ||
      ''

    videoUrl = rebuildAlbumUrl(cur.albumLocSuffix) || changeAlbumUrl(videoUrl)

    if (videoUrl.indexOf('?') === -1 && pbParam) {
      videoUrl += '?' + pbParam
    } else if (pbParam) {
      videoUrl += '&' + pbParam
    }
    let albumPic = webpTag ? cur?.imgDTO?.img : cur?.imgDTO?.imgWebp85

    if (!albumPic) {
      albumPic = webpTag
        ? cur?.albumPic
        : albumPic?.albumWebpPic || cur?.albumPic
      // 基于之前逻辑，改成ssr后，本地webpTag为0，而albumPic?.albumWebpPic可能不存在，所以再取cur?.albumPic兼容下
    }

    // const _episodeList = cur?.episodeList
    // const order =
    //   _episodeList && _episodeList?.length > 0
    //     ? _episodeList[_episodeList.length - 1].order
    //     : 0

    t.push({
      name: cur['name'],
      chnId: cur['chnId'],
      albumPic,
      url: videoUrl,
      id: cur['qipuId'],
      tvCount: cur['tvCount'],
      total: cur['total'],
      year: cur['publishTime'].substring(0, 4),
      vipInfo: cur['vipInfo'],
      videoOrder: cur['videoOrder'],
      updateDesc: {
        chnId: cur['chnId'] || '',
        total: cur['total'] || cur['maxOrder'],
        tvCount: cur['tvCount'] || cur['order'],
        publishTime: cur['publishTime']
      },
      pingback: {
        type: 'search_list',
        index: `play_${i}`,
        rpage: 'search_rst',
        block: 'search_intent_recognize',
        position: starsInfo.qipuId ? 2 : 1
      }
    })
    return t
  }, [])
}

export const handleSearchResutlData = (data, key, lang, mod) => {
  const newData = {
    hasMore: false,
    total: data.total,
    query: key,
    videos: [],
    intentEpg: [],
    pingback: {
      bkt: data.bkt,
      isReplaced: data.isReplaced === true ? 1 : 0,
      realQuery: data.realQuery,
      eventId: data.eventId
    },
    starsInfo: data.starsInfo,
    terms: data.terms,
    pn: data.currentPageNo
  }

  const epg = data.epg
  let pbParam = ''
  if (supportUrlPbMod(mod)) {
    pbParam = `frmrp=search&frmb=list`
  }
  for (let i = 0, len = epg.length; i < len; i++) {
    const cur = epg[i]
    const docId = (cur && cur.docId) || ''
    if (!i && docId === '6af22d1131cacc2e892fa9e0236d0450') {
      newData.videos.push({
        docId
      })
      continue
    }
    // director 可能存在只有id,而没有n的情况下，所以需要过滤下,
    // 出于性能考虑，只判断数组为1的情况，其余情况有问题，周知运营补数据
    // {
    //   "id": 241401205,
    //   "n": ""
    // }
    if (!cur['cast']) {
      cur['cast'] = {
        director: []
      }
    }
    if (
      cur['cast'] &&
      cur['cast']['director'].length === 1 &&
      cur['cast']['director'][0]['n'] === ''
    ) {
      cur['cast']['director'] = []
    }
    let videoUrl = rebuildPlayUrl(cur.playLocSuffix) || ''
    if (!videoUrl) {
      const list = cur['episodeList'] || []
      videoUrl = list.length ? rebuildPlayUrl(list[0].playLocSuffix) : ''
    }
    videoUrl = videoUrl || rebuildPlayUrl(cur.qipuIdStr) || ''
    if (videoUrl.indexOf('?') === -1 && pbParam) {
      videoUrl += '?' + pbParam
    } else if (pbParam) {
      videoUrl += '&' + pbParam
    }
    let albumUrl =
      rebuildAlbumUrl(cur.albumLocSuffix) ||
      rebuildAlbumUrl(cur.playLocSuffix) ||
      rebuildAlbumUrl(cur.qipuIdStr) ||
      ''
    if (albumUrl.indexOf('?') === -1 && pbParam) {
      albumUrl += '?' + pbParam
    } else if (pbParam) {
      albumUrl += '&' + pbParam
    }

    // 标题高亮逻辑
    let name = cur['name']
    const terms = data.terms || []
    for (let j = 0, len = terms.length; j < len; j++) {
      const reg = new RegExp('^(?!(<|<)/).*' + terms[j], 'gi')

      name = name.replace(reg, word => {
        return `<i>${word}</i>`
      })
    }
    // 导演、主演高亮逻辑
    const intlRecallInfo = cur['intlRecallInfo'] || []
    for (let i = 0; i < intlRecallInfo.length; i++) {
      if (intlRecallInfo[i]['fieldId'] === 76) {
        const recallTerm = intlRecallInfo[i]['recallTerm']
        const cast = cur['cast']['mainActor']
        // const director = cur['cast']['director']

        for (let j = 0; j < recallTerm.length; j++) {
          const reg = new RegExp(recallTerm[j], 'gi')

          for (let m = 0; m < cast.length; m++) {
            cast[m]['n'] = cast[m]['n'].replace(reg, word => {
              return `<i>${word}</i>`
            })
          }
        }
      }
    }

    let newEpisodeList = []
    const lockAid = []
    if (cur['episodeList']) {
      newEpisodeList = cur['episodeList'].map(episode => {
        let payUrl =
          rebuildPlayUrl(episode.playLocSuffix) ||
          rebuildPlayUrl(episode.qipuIdStr) ||
          ''
        if (payUrl.indexOf('?') === -1 && pbParam) {
          payUrl += '?' + pbParam
        } else if (pbParam) {
          payUrl += '&' + pbParam
        }

        const vipInfo = episode.vipInfo || {}
        const lockType = vipInfo.advancedUnlockType
        if (lockType === 0 || lockType === 1) {
          lockAid.push(episode.episodeId)
        }

        return {
          ...episode,
          url: payUrl
        }
      })
    }

    newData.videos.push({
      albumId: cur['albumId'],
      url: videoUrl,
      albumUrl,
      name,
      // 专辑封面用cur['imgDTO']['img']
      albumPic: cur['albumPic'] || cur['imgDTO']['img'],
      cast: cur['cast']['mainActor'],
      director: cur['cast']['director'],
      publishYear: cur['publishTime'] && cur['publishTime'].slice(0, 4),
      qipuId: cur['qipuId'],
      vipInfo: cur['vipInfo'],
      chnId: cur['chnId'],
      contentType: cur['contentType'],
      categoryTagMap: cur['categoryTagMap'] || {},
      len: cur['len'] || '',
      lockAid,
      episodeList: newEpisodeList,
      controlStatus:
        cur['controlStatus'] === undefined ? '' : cur['controlStatus']
    })
  }
  return newData
}

export const handleHotSearchData = (data, params) => {
  const { mod } = params
  const newData = {
    rowName: '',
    videos: [],
    pingback: {
      bkt: '',
      e: '',
      r_area: '',
      block: 'nr_hotsearch'
    }
  }

  for (let i = 0, len = data.length; i < len; i++) {
    const cur = data[i]
    let videoUrl =
      rebuildPlayUrl(cur.playLocSuffix) ||
      rebuildPlayUrl(cur.albumLocSuffix) ||
      rebuildPlayUrl(cur.qipuIdStr) ||
      ''
    // if (changePcAlbumMod(mod) || isMobile) {
    videoUrl = rebuildAlbumUrl(cur.albumLocSuffix) || changeAlbumUrl(videoUrl)
    // }
    if (supportUrlPbMod(mod)) {
      if (videoUrl.indexOf('?') === -1) {
        videoUrl += `?frmrp=search_rst&frmb=top_search&frmrs=top_search_${i +
          1}`
      } else {
        videoUrl += `&frmrp=search_rst&frmb=top_search&frmrs=top_search_${i +
          1}`
      }
    }
    newData.videos.push({
      name: cur['name'],
      chnId: cur['chnId'],
      albumPic: removeProtocol(cur['albumPic']),
      url: videoUrl,
      id: cur['qipuId'],
      tvCount: cur['tvCount'],
      total: cur['total'],
      year: cur['publishTime'] && cur['publishTime'].substring(0, 4),
      vipInfo: cur['vipInfo'],
      videoOrder: cur['videoOrder'],
      updateDesc: {
        chnId: cur['chnId'] || '',
        total: cur['total'] || 0,
        tvCount: cur['tvCount'] || 0,
        publishTime: cur['publishTime']
      },
      pingback: {
        r_source: '',
        reasonid: '',
        ht: 0,
        block: 'nr_hotsearch',
        stype: '',
        bkt: '',
        e: '',
        r_area: '',
        index: 'play',
        position: i + 1
      }
    })
  }
  return newData
}

function getSuffix(lang) {
  // 此列表中语言匹配需要空格
  const langList = ['zh_cn', 'zh_tw']

  if (langList.indexOf(lang) > -1) {
    return ''
  }
  return ' '
}
