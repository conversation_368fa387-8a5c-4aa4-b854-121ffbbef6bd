import React from 'react'
import { connect } from 'react-redux'
import Meta from '@/components/common/Meta'
import TongJiPb from '@/components/common/TongJiPb'
import NotShare from '@/components/pages/notshare'
import DeviceCtx from '@/components/context'

class About extends React.Component {
  constructor(props) {
    super(props)
    this.state = {}
  }

  componentDidMount() {}

  static async getInitialProps() {
    return {}
  }

  render() {
    const { modeLangObj, globalLangPkg } = this.props
    const langPkg = globalLangPkg.toJS()
    const trueLang = modeLangObj.toJS()

    const { cookieSetHandler } = this.context
    return (
      <>
        <Meta
          desc={langPkg.do_not_sell_or_share_my_information}
          title={langPkg.do_not_sell_or_share_my_information}
        />
        <NotShare
          langPkg={langPkg}
          mod={trueLang.mod}
          langCode={trueLang.lang}
          cookieSetHandler={cookieSetHandler}
        />
        <TongJiPb
          rpage="do_not_share_my_information"
          blockInfo="header,footer"
        />
      </>
    )
  }
}

About.contextType = DeviceCtx
const mapStateToProps = state => ({
  modeLangObj: state.getIn(['language', 'modeLangObj']),
  globalLangPkg: state.getIn(['language', 'langPkg'])
})

export default connect(mapStateToProps)(About)
