import React from 'react'
import { connect } from 'react-redux'
import Meta from '@/components/common/Meta'
import TongJiPb from '@/components/common/TongJiPb'
import NewOnlineFocus from '@/components/pages/newOnline/focus'
import NewOnlineList from '@/components/pages/newOnline/list'
import { getNewOnlineData } from '@/utils/apis'

class NewOnline extends React.Component {
  constructor(props) {
    super(props)
    this.state = {}
  }

  static async getInitialProps({ ctx }) {
    const { newOblineList: firstLst, nextFlag } = await getNewOnlineData(
      { pg_num: 1 },
      ctx
    )
    return { firstLst, nextFlag }
  }

  render() {
    const { langPkg, firstLst, nextFlag } = this.props
    const pkg = langPkg.toJS()
    const { Upcoming, newAndUpcoming } = pkg
    return (
      <>
        <Meta desc={newAndUpcoming} title={Upcoming} path="newOnline" />
        <NewOnlineFocus title={Upcoming} description={newAndUpcoming} />
        <NewOnlineList pkg={pkg} firstListData={firstLst} nextFlag={nextFlag} />
        <TongJiPb rpage="preview_more" />
      </>
    )
  }
}

const mapStateToProps = state => ({
  langPkg: state.getIn(['language', 'langPkg'])
})

export default connect(mapStateToProps)(NewOnline)
