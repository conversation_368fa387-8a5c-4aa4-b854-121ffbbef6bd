import React, { useCallback, useState, useRef } from 'react'
import dynamic from 'next/dynamic'
import { connect } from 'react-redux'
import { useEffect } from 'react'
import VideoPlayer from '@/components/common/VideoPlayer'
import PlayVideoList from '@/components/pages/play/playVideoList'
import PlayInfo from '@/components/pages/play/playInfo'
import PlayAdInfo from '@/components/pages/play/playVipAd'
import PlayMenue from '@/components/pages/play/playMenue'
// import PlayBanner from '@/components/pages/play/playBanner'
import PlayBannerNew from '@/components/pages/play/playBannerNew'
import MovieRecommendList from '@/components/pages/play/movieRecommendList'
import SimilarPlayList from '@/components/pages/play/similarPlayList'
// import TrailsList from '@/components/pages/play/trailsList'

import HomeSchema from '@/components/pages/home/<USER>'
import OrganizationSchema from '@/components/pages/home/<USER>'
import Schema from '@/components/pages/play/schema'
// import OtherSchema from '@/components/pages/play/otherSchema'
// import ItemListSchema from '@/components/pages/play/itemListSchema'

import PlayMeta from '@/components/pages/play/playMeta'
// import BannerBlowPlayerApp from '@/components/pages/play/bannerBlowPlayerApp'
import TongJiPb from '@/components/common/TongJiPb'
import GPTADRankBanner from '@/components/common/GPTADRankBanner'
import ParentalModal from '@/components/common/ParentalModal'

import Footer from '@/components/common/Footer'
// import FilmmakerList from '@/components/common/FilmmakerList'
import PlayWrapper from '@/style/playStyle'
import shPltfPbParam from '@/utils/shPltf'
import { handleImgSize } from '@/utils/imgSize'
import { cdnFrom } from '@/utils/common'
import { getIp } from '@/utils/commonApi'
import {
  fetchVideoInfoAction,
  getPlayStatusCodeAction
} from '@/store/reducers/play/play'
import { fetchPersonalAction } from '@/store/reducers/personal/personal'
import { fetchConfigAction } from '@/store/reducers/commonConfig/commonConfig'
import $http from '@/kit/fetch'
import { checkQipuIdStr } from '@/kit/url'
import { getDevice, getMobileType } from '@/kit/device'
import { getCtxDevice } from '@/kit/cookie'
import { getCard } from '@/kit/commonConfig'
import Error from './_error'
import {
  CLIENT_INTL_EPG_INFO,
  // CLIENT_DECODE_ID,
  DECODE_ID,
  GET_SUBTITLE_LANG,
  LANG_PKG_WEB,
  FILM_REC,
  PLAY_RANKING_SEVR
} from '@/constants/interfaces'
import { getCookies, setCookies } from '@/kit/cookie'
import { getUid } from '@/utils/userInfo'
import { getLangPkgAction } from '@/store/reducers/language/language'
import { getVipLangPkgAction } from '@/store/reducers/vip/vip'
import {
  commonDeviceIdParams,
  platformId,
  canUseWebp,
  rebuildAlbumUrl,
  forceIntlUrl,
  locUrl
} from '@/kit/common'
import PlayRanking from '@/components/pages/play/playRanking'
import { queryFromUrl } from '@/kit/url'
import { getPrePlayerData } from '@iqiyi-ibd/shared-utils'
import { webpreData } from '@/utils/esrRender'
import axios from 'axios'
import {
  switchNextVideo,
  switchPreVideo
} from '@/utils/player/handlePlayerEvent'

const Comments = dynamic(import('@/components/pages/play/comments'), {
  ssr: false
})

let pbParams = {}
// let blowPlayerChild

const Short = props => {
  // seo 需求 http://pms.qiyi.domain/browse/GLOBALREQ-2505
  // 如果入参携带有?lang=xxx,则在页面url上直接隐去 <EMAIL>
  // 此url会对应sitemap.xml.gz中的url地址
  const [floorShow, setFloorShow] = useState(false)
  useEffect(() => {
    // 模拟componentDidMount
    let url = window.location.href
    // 立荣需求，去掉自动隐藏lang的逻辑
    // let reg = /\?lang=[a-z_]*$/
    // if (reg.test(url)) {
    //   url = url.split('?')[0]
    //   history.pushState(
    //     { url: url, title: document.title },
    //     document.title,
    //     url
    //   )
    // }
    window.addEventListener('scroll', handleScroll)
    const urlFv = queryFromUrl(url, 'fv')
    if (urlFv) {
      setCookies('playFV', urlFv, {
        time: 1000 * 60 * 60 * 24
      })
    }

    getIp()
  }, [])

  // 获取一个英文的剧名，用于追踪来源
  // http://pms.qiyi.domain/browse/GLOBALLINEDEV-2183
  const [videoName, setVideoName] = useState('')
  const [blowPlayerAppSty, setBlowPlayerAppSty] = useState('')
  const [showParentalModal, setShowParentalModal] = useState(false)
  const [modalStep, setModalStep] = useState('startLogin')
  const [highRating, setHighRating] = useState('')
  const [r21set, setR21Set] = useState(false)
  const [verifyToken, setVerifyToken] = useState('')
  const {
    videoInfo,
    playStatusCode,
    albumInfo,
    langPkg,
    globalLangPkg,
    subtitleInfo,
    // shortSimilarData,
    modeLangObj,
    movieRecommendData,
    playScoreInfo,
    playRankingData,
    curVideoPageInfo,
    cachePlayList,
    curUrl,
    ctxIsMobile,
    isMobile,
    cards,
    playerCreateSucess,
    isSeo,
    dispatch,
    userInfo,
    prePlayerData,
    langCode,
    superSeries
  } = props
  let showComponent = !!playerCreateSucess || isSeo

  const playerRef = useRef()
  //竖屏相关
  const playerWrapRef = React.createRef()
  const isPad = getMobileType() === 'ipad'
  const shuScreen =
    isMobile && videoInfo.get('albumId') === 2689361275257701 && !isPad
  useEffect(() => {
    const getLangPkg = async () => {
      const options = {}
      options.params = {
        langCode: modeLangObj.get('lang'),
        platformId: platformId()
      }
      const pkgData = await $http(LANG_PKG_WEB, options)
      dispatch(getLangPkgAction(pkgData.data))
    }
    const getvipLangPkg = async () => {
      const options = {}
      options.params = {
        langCode: modeLangObj.get('lang'),
        businessName: 'PCW_VIP',
        platformId: platformId()
      }
      const pkgData = await $http(LANG_PKG_WEB, options)

      dispatch(getVipLangPkgAction(pkgData.data))
    }
    const getVideoName = async () => {
      try {
        const options = { params: commonDeviceIdParams() }
        // const idMatch = window.location.pathname.match(/play\/(\S*)($|\/|\?)/)
        // const decodeIdData = await $http(
        //   CLIENT_DECODE_ID + (idMatch && idMatch[1]),
        //   options
        // )
        // if (decodeIdData.code === '0') {
        const id = videoInfo.get('tvId')
        if (id) {
          const albumData = await $http(
            CLIENT_INTL_EPG_INFO + videoInfo.get('tvId'),
            {
              params: { ...options.params, langCode: 'en_us' }
            }
          )
          if (albumData.code === '0' && albumData.data) {
            const videoName =
              albumData.data.albumName || albumData.data.name || ''
            setVideoName(videoName.replace(/\s/g, ''))
          }
        }
        // }
      } catch (e) {
        console.error(e) // 增加处理
      }
    }
    getLangPkg()
    getvipLangPkg()
    getVideoName()

    if (shuScreen && document) {
      let header = document.getElementById('block-header')
      if (header) {
        header.style.display = 'none'
      }
    }
  }, [])

  useEffect(() => {
    const { videoInfo } = props
    props.dispatch(
      fetchConfigAction({
        pageSt: 'play_web',
        album_id: videoInfo.get('albumId')
      })
    )
  }, [])
  // const playerCreateSucess = 1
  const transImgSize = data => {
    return data.map(item => {
      // 前端头像取图逻辑：coverPic > headPic @孙立荣提出
      let pic = ''
      if (item.coverPic) {
        pic = handleImgSize(item.coverPic, '_300_300.jpg')
      } else {
        pic = handleImgSize(item.headPic, '_300_300.jpg')
      }
      return {
        ...item,
        headPic: pic
      }
    })
  }

  const closeParentalModal = useCallback(async type => {
    setShowParentalModal(false)
    if (type === 'forget') {
      const { activated, phone } = userInfo.toJS()
      // 未激活状态指的是含有邮箱且activated为0
      const goValidate = phone || (!phone && activated)
      if (!window.sdkPackManager.globalLogin) {
        await window.sdkPackManager.initLogin()
      }
      window.sdkPackManager.globalLogin.openLoginRegWindow({
        type: goValidate ? 110 : 1,
        parentalValidate: goValidate ? 0 : 1
      })
      if (goValidate) {
        window.sdkPackManager.globalLogin.on('parentalValidate', data => {
          setModalStep('forgetToSet')
          setShowParentalModal(true)
          setVerifyToken(data.token)
        })
      } else {
        window.sdkPackManager.globalLogin.on('emailManage', () => {
          setModalStep('forgetToSet')
          setShowParentalModal(true)
        })
      }
    } else if (type === 'setEmail') {
      if (!window.sdkPackManager.globalLogin) {
        await window.sdkPackManager.initLogin()
      }
      window.sdkPackManager.globalLogin.openLoginRegWindow({
        type: 2,
        parentalValidate: 1
      })
      // window.sdkPackManager.globalLogin.setStep(12)
      window.sdkPackManager.globalLogin.on('emailManage', () => {
        if (highRating === 'R21') {
          setR21Set(true)
        } else {
          if (playerRef && playerRef.current) {
            playerRef.current.closeParentalPop()
          }
          if (window.playerObject) {
            window.playerObject.play()
          }
        }
      })
    }
  })

  const closeParentalPop = useCallback(() => {
    if (playerRef && playerRef.current) {
      playerRef.current.closeParentalPop()
    }
  })

  const openParentalModal = useCallback((step, highRating) => {
    setModalStep(step)
    setShowParentalModal(true)
    setHighRating(highRating)
  })

  // 针对最高级别重新验证
  const resetVarify = useCallback(() => {
    setR21Set(true)
  })

  const getStarInfoEnter = () => {
    // 去重
    let starInfoEnterArr = []
    let albumInfoEnter = []
    let videoInfoEnter = []
    const starEnterArrObj = albumInfo.get('starEnterArr')
    const videoStarEnterArrObj = videoInfo.get('starEnterArr')
    if (albumInfo.get('mainActor')) {
      if (starEnterArrObj && typeof starEnterArrObj.toJS === 'function') {
        albumInfoEnter = starEnterArrObj.toJS()
      }
    }
    if (videoInfo.get('mainActor')) {
      if (
        videoStarEnterArrObj &&
        typeof videoStarEnterArrObj.toJS === 'function'
      ) {
        videoInfoEnter = videoStarEnterArrObj.toJS()
      }
    }
    starInfoEnterArr =
      albumInfoEnter.length > 0 ? albumInfoEnter : videoInfoEnter
    // 过滤头像为空的数据
    starInfoEnterArr = starInfoEnterArr.filter(item => {
      if (!item) {
        return false
      }
      if ((!!item.headPic || !!item.coverPic) && !!item.name && !!item.id) {
        return true
      }
    })
    // 过滤可能存在的重复元素
    let repeatResultArr = []
    let obj = {}
    for (let i = 0; i < starInfoEnterArr.length; i++) {
      if (!obj[starInfoEnterArr[i].id]) {
        repeatResultArr.push(starInfoEnterArr[i])
        obj[starInfoEnterArr[i].id] = true
      }
    }
    let transImgResultArr = transImgSize(repeatResultArr)
    return transImgResultArr
  }

  const handleScroll = () => {
    if (shuIsFull) {
      return
    }
    const scrollTop =
      document.documentElement.scrollTop ||
      window.pageYOffset ||
      document.body.scrollTop ||
      -parseInt(document.body.style.top, 10) ||
      0
    if (scrollTop > 400) {
      setFloorShow(true)
    }
  }

  const handleConfig = async () => {
    const config = await $http('//pcw-api.iq.com/api/vote/config', {
      params: {
        platformId: getDevice() === 'mobile' ? 4 : 3
      }
    })
    if (config && config.code === '0') return config.data
  }

  const getUrlLang = () => {
    let url = window.location.href
    let params = url.split('?')[1]
    let paramsResult = new URLSearchParams('?' + params)
    let lang = paramsResult.get('lang')
    let langCode = paramsResult.get('langCode')
    return langCode || lang
  }

  // 青你选手做单独的跳转
  const youthPersonClick = async id => {
    try {
      let data = await handleConfig()
      let { youth_with_you_duxin_voteid } = data
      // 优先用url上的lang @李玉佳 GLOBALREQ-5777
      let curLang = getUrlLang() || getCookies('lang')
      let mod = getCookies('mod')
      window.location.href = `https://www.iq.com/intl-common/YouthPlayerDetail.html?lang=${curLang}&voteId=${youth_with_you_duxin_voteid}&peopleId=${id}&mod=${mod}&fc=bf9bab8cc2f6956b`
    } catch (e) {
      console.log(e)
    }
  }

  const handleH5JumpAppBtn = () => {
    setBlowPlayerAppSty('show')
  }

  let trueLangPkg = {}
  if (langPkg && typeof langPkg.toJS === 'function') {
    trueLangPkg = langPkg.toJS()
  }
  let langPkgData = {}
  if (globalLangPkg && typeof globalLangPkg.toJS === 'function') {
    langPkgData = globalLangPkg.toJS()
  }
  const controlStatus = videoInfo.get('controlStatus')
  let starInfoEnterArr = []
  starInfoEnterArr = getStarInfoEnter()

  let rpage = 'play'
  if (controlStatus === 0) {
    rpage = 'play_restriction'
  }
  const blockInfo =
    'header,album_information,video_information,play_recommend,footer'
  //精彩看点下方广告位 左边长广告
  const recommendBanner = getCard('pcw_play_OA_longAds', cards.toJS())[0]
  //排行榜下方广告位 右边短广告
  const rankBanner = getCard('pcw_play_OA_shortAds', cards.toJS())[0]
  // 评论模块
  const commentsConfig = getCard('pcw_play_comment', cards.toJS())[0]

  let clientWidth = 0
  if (global.window) {
    clientWidth =
      document.documentElement.clientWidth || document.body.clientWidth
  }
  const leftRankBanner = clientWidth > 0 && rankBanner && clientWidth < 1024
  const rightRankBanner = clientWidth > 0 && rankBanner && clientWidth >= 1024
  // const pbShowParams = JSON.stringify({"s2":""}

  //竖屏相关 手势拖动
  let startX, startY
  let shuIsFull = shuScreen
  const GetSlideDirection = (startX, startY, endX, endY) => {
    let dy = startY - endY
    let result = 0
    if (dy > 15) {
      //向上滑动  做个区间 超过15为向上
      result = 1
    } else if (dy < -15) {
      //向下滑动
      result = 2
    } else {
      result = 0
    }
    return result
  }

  const handleTouchStart = useCallback(ev => {
    if (!shuIsFull) return
    startX = ev.touches[0].pageX
    startY = ev.touches[0].pageY
  })
  const handleTouchEnd = useCallback(ev => {
    if (!shuIsFull) return
    let endX, endY
    endX = ev.changedTouches[0].pageX
    endY = ev.changedTouches[0].pageY
    let direction = GetSlideDirection(startX, startY, endX, endY)

    if (direction === 1) {
      // 向上
      switchNextVideo()
    } else if (direction === 2) {
      switchPreVideo()
    }
  })
  //竖屏全屏有修改
  const onShuChanged = full => {
    if (shuScreen) {
      let videodom = document.getElementById('intl-video-wrap')
      let header = document.getElementById('block-header')
      let videolist = document.getElementById('block-album_information')
      shuIsFull = full
      if (full) {
        videodom.style.height = '100%'
        videodom.style.top = '0px'
        header.style.display = 'none'
        videolist.style.display = 'none'
        // playerWrapRef.current.style.height = `100%`
        // playerWrapRef.current.style.top = `0px`
      } else {
        videodom.style.height = '56.2vw'
        videodom.style.top = '88px'
        header.style.display = ''
        videolist.style.display = 'block'
        // playerWrapRef.current.style.height = `56.2vw`
        // playerWrapRef.current.style.top = `88px`
      }
    }
  }
  let shuWrap = shuIsFull ? 'shu-player-wrap' : ''
  let playPageWrapper
  if (playStatusCode === 404) {
    playPageWrapper = <Error statusCode={playStatusCode} rpage="play" />
  } else {
    playPageWrapper = (
      <>
        <PlayMeta mod={modeLangObj.get('mod')} curUrl={curUrl} path="short" />
        <OrganizationSchema langCode={langCode} />
        <HomeSchema langCode={langCode} />
        <Schema
          videoInfo={videoInfo}
          albumInfo={albumInfo}
          subtitleInfo={subtitleInfo}
          langPkg={langPkgData}
          playScoreInfo={playScoreInfo}
          mod={modeLangObj.get('mod')}
        />
        {/*        <ItemListSchema
          videoInfo={videoInfo}
          curVideoPageInfo={curVideoPageInfo}
          cachePlayList={cachePlayList}
        />
*/}
        {/*        <OtherSchema
          videoInfo={videoInfo}
          albumInfo={albumInfo}
          langPkg={langPkgData}
          mod={modeLangObj.get('mod')}
        />*/}
        <PlayWrapper id="setSizeStyle">
          <div className="intl-play-area-wrap">
            <div className="intl-play-area-inner">
              <div className="intl-play-area">
                <div className="intl-play-left">
                  <div
                    className={`intl-video-wrap ${shuWrap}`}
                    id="intl-video-wrap"
                    ref={playerWrapRef}
                    onTouchEnd={handleTouchEnd}
                    onTouchStart={handleTouchStart}
                  >
                    <div className="intl-video-area">
                      <VideoPlayer
                        onRef={ref => {
                          playerRef.current = ref
                        }}
                        shuScreen={shuScreen}
                        onShuChanged={onShuChanged}
                        trueLangPkg={trueLangPkg}
                        videoName={videoName}
                        r21set={r21set}
                        handleH5JumpAppBtn={handleH5JumpAppBtn}
                        openParentalModal={openParentalModal}
                        prePlayerData={prePlayerData}
                      />
                    </div>
                  </div>

                  <div className="intl-play-operation">
                    {showComponent ? <PlayMenue /> : null}
                  </div>
                </div>
                <div
                  className="intl-play-right"
                  id="block-album_information"
                  style={{ display: `${shuIsFull ? 'none' : ''}` }}
                >
                  {showComponent ? (
                    <PlayBannerNew
                      rpage="play"
                      langPkgData={langPkgData}
                      showComponent={showComponent}
                      curVideoInfo={videoInfo}
                      modeLangObj={modeLangObj}
                      videoName={videoName}
                      screenWidth="1023"
                    />
                  ) : null}
                  <PlayVideoList
                    showComponent={showComponent}
                    movieRecommendData={movieRecommendData}
                    playRankingData={playRankingData}
                    starInfoEnterArr={starInfoEnterArr}
                    isMobile={isMobile}
                    youthPersonClick={youthPersonClick}
                    ctxIsMobile={ctxIsMobile}
                    superSeries={superSeries}
                  />
                </div>
              </div>
            </div>
          </div>
          {webpreData}
          <div className="intl-play-con">
            <div className="intl-play-con-inner">
              <div className="intl-play-con-left">
                <div className="pc-info">
                  <PlayInfo
                    videoName={videoName}
                    starInfoEnterArr={starInfoEnterArr}
                    langPkgData={langPkgData}
                    youthPersonClick={youthPersonClick}
                    ctxIsMobile={ctxIsMobile}
                    isH1={!ctxIsMobile}
                  />
                </div>
                {showComponent ? (
                  <>
                    <PlayAdInfo rpage="play" />
                    <MovieRecommendList
                      movieRecommendData={movieRecommendData}
                      videoInfo={videoInfo}
                    />
                    <SimilarPlayList
                      playRankingData={playRankingData}
                      recommendBanner={recommendBanner}
                      rankBanner={leftRankBanner}
                    >
                      {ctxIsMobile && (
                        <Comments
                          tvId={videoInfo.get('tvId')}
                          commentsConfig={commentsConfig}
                          langPkgData={langPkgData}
                          curVideoInfo={videoInfo}
                          modeLangObj={modeLangObj}
                          videoName={videoName}
                        />
                      )}
                    </SimilarPlayList>{' '}
                  </>
                ) : null}
                {!ctxIsMobile && showComponent && (
                  <Comments
                    tvId={videoInfo.get('tvId')}
                    commentsConfig={commentsConfig}
                    langPkgData={langPkgData}
                    curVideoInfo={videoInfo}
                    modeLangObj={modeLangObj}
                    videoName={videoName}
                  />
                )}
              </div>

              {showComponent ? (
                <div className="intl-play-con-right">
                  <PlayBannerNew
                    rpage="play"
                    langPkgData={langPkgData}
                    curVideoInfo={videoInfo}
                    modeLangObj={modeLangObj}
                    videoName={videoName}
                    screenWidth="1024"
                  />
                  {/*<PlayBanner rpage="play" videoInfo={videoInfo} showType="pc" />*/}
                  <PlayRanking
                    playRankingData={playRankingData}
                    videoInfo={videoInfo}
                    showType="pc"
                  />
                  {rightRankBanner ? <GPTADRankBanner showType="pc" /> : ''}
                </div>
              ) : null}
            </div>
          </div>
        </PlayWrapper>

        {showComponent ? (
          <>
            {showParentalModal ? (
              <ParentalModal
                rpage={rpage}
                step={modalStep}
                highRating={highRating}
                closeModal={closeParentalModal}
                verifyToken={verifyToken}
                resetVarify={resetVarify}
                closeParentalPop={closeParentalPop}
              ></ParentalModal>
            ) : (
              ''
            )}
          </>
        ) : null}
        <TongJiPb rpage={rpage} blockInfo={blockInfo} pbShowParams={pbParams} />
      </>
    )
  }
  return (
    <>
      {playPageWrapper} {isSeo || floorShow ? <Footer /> : null}
    </>
  )
}

Short.getInitialProps = async ({ ctx }) => {
  const { query, store, isServer, res } = ctx
  const { id, sh_pltf, from, _from, vip_order } = query
  const curUrl = ctx.req.url.split('?')[0]
  const curUrlParams = ctx.req.url.split('?')[1]
  const isMobile = getCtxDevice(ctx) === 'mobile'
  const curFrom = cdnFrom(_from, from)
  const headers = ctx.req.headers
  let isSeo = false
  if (headers['is-robot-query']) {
    // 增加对seo特殊处理逻辑
    isSeo = true
  }
  let subtitleInfo = ''
  let movieRecommendData, playRankingData, prePlayerData, allhu
  if (sh_pltf) {
    const pbS2 = shPltfPbParam(sh_pltf)
    pbParams = {
      s2: pbS2
    }
  } else {
    pbParams = {}
  }
  const finalId = checkQipuIdStr(id)
  if (!finalId) {
    await store.dispatch(getPlayStatusCodeAction(404))
    res.setHeader('x-page-type', 'not-found')
  } else {
    const videoInfo = await store.dispatch(
      fetchVideoInfoAction({
        id: finalId,
        isSupportSEO: true,
        ctx,
        isMobile,
        _catch: {}
      })
    )
    if (videoInfo && typeof videoInfo === 'number') {
      res.setHeader('x-page-type', 'not-found')
    }
    if (videoInfo && videoInfo.controlStatus === 0) {
      res.setHeader('x-page-type', 'play-control')
    }
    const params = {
      ...commonDeviceIdParams(),
      langCode:
        ctx.req.headers['langCode'] ||
        ctx.req.headers['langcode'] ||
        getCookies('lang', ctx) ||
        'en_us',
      modeCode:
        ctx.req.headers['modeCode'] ||
        ctx.req.headers['modecode'] ||
        getCookies('mod', ctx) ||
        'intl',
      uid: getUid()
    }
    const options = { params }
    options.ctx = ctx
    let qipuId = videoInfo.tvId
    if (!qipuId) {
      const decodeIdData = await $http(DECODE_ID + finalId, options)
      if (decodeIdData.code === '0') {
        qipuId = decodeIdData.data
      }
    }
    const resLangList = await $http(GET_SUBTITLE_LANG + '/' + qipuId, options)
    if (resLangList.code === '0') {
      const list = resLangList.data.subtitleLanguageDTOS.reduce(
        (subtitles, item) => {
          if (item.subtitle) return [...subtitles, item.subtitle]
          return subtitles
        },
        []
      )
      subtitleInfo = list.join(',')
    }
    const mod = store
      .getState()
      .get('language')
      .get('modeLangObj')
      .get('mod')
    const lang = store
      .getState()
      .get('language')
      .get('modeLangObj')
      .get('lang')
    const recommendParams = {
      ...params,
      qipuId,
      pspStatus: 1,
      tab: '',
      size: 30,
      channelId: videoInfo.channelId,
      vip: 0,
      sid: getCookies('QC005', ctx) + Date.now()
    }
    recommendParams.ctx = ctx
    if (videoInfo.channelId === 1 && videoInfo.videoType === 'singleVideo') {
      movieRecommendData = await getMovieRecommend(recommendParams)
      movieRecommendData.mod = params.modeCode
      movieRecommendData.isMobile = isMobile
    }
    const rankDeviceId =
      getCookies('QC005', ctx) || '8fc57f60c3d382cc0b99dc6562873d45'
    const playRankingParams = {
      ...params,
      num: '30',
      channelId: videoInfo.channelId || 2,
      chartType: '1',
      tab: 'play',
      sid: rankDeviceId + Date.now(),
      deviceId: rankDeviceId,
      langCode: lang,
      modeCode: mod
    }
    playRankingData = await getPlayRanking(playRankingParams, ctx)
    let qc005 = getCookies('QC005', ctx)
    // let useSSR = !!(qc005 && parseInt(qc005.slice(-1), 16) < 10)
    const ssr = queryFromUrl(ctx.req.url, 'ssr') //url拼接ssr=0 为不走ssr
    const useSSR = ssr !== '0'
    if (qipuId && global.ptid && useSSR) {
      const i19Val = getCookies('I00019', ctx)
      const islogin = parseInt(i19Val, 10) === 1
      if (islogin) {
        //会员数据
        let vtypeUrl = '//pcw.gateway.prod.online.qiyi.qae/api/vtype'
        let vparams = {
          ...params,
          batch: 1,
          deviceId: qc005 || '',
          modeCode: mod,
          langCode: lang,
          platformId: platformId(),
          vipInfoVersion: 5.0
        }
        let viptypes = await $http(vtypeUrl, { params: vparams, ctx })
        allhu = viptypes.data.all_vip
      }
      const pageUrl = locUrl()
      //UG的页面不走历史记录
      const pageUGW =
        queryFromUrl(pageUrl, 'vip_order') === 'true' && getDevice() === 'pc'
      const fromhistory = !pageUGW
      //ug免广
      const adstrategy = pageUGW ? ['deeplink'] : undefined
      // 挂载全局，提供给 @ibd-player 使用
      global.mmcEncode = str => {
        return str
      }
      global.axios = axios
      // setSwitchHevc(false) //server不开265
      //播放器开播数据
      prePlayerData = await getPrePlayerData({
        ctx: ctx,
        tvid: qipuId,
        vid: videoInfo.vid,
        albumId: videoInfo.albumId,
        lang,
        mod,
        src: global.ptid,
        allhu,
        remoteAddress: global.clientIp,
        fromhistory,
        adstrategy
      })
      prePlayerData.useSSR = useSSR
    }
  }
  // await store.dispatch(fetchPersonalAction({ ctx }))
  // await store.dispatch(fetchConfigAction({ pageSt: 'play_web', ctx: ctx }))

  const playStatusCode = store
    .getState()
    .get('play')
    .get('playStatusCode')
  const albumInfo = store
    .getState()
    .get('play')
    .get('albumInfo')
  const videoInfo = store
    .getState()
    .get('play')
    .get('videoInfo')

  // 超剧集
  let superSeries = {
    seasons: [],
    curSeasonX: '',
    curSeason: ''
  }
  const fatherCollectionIds =
    albumInfo.get('fatherCollectionIds')?.toJS() ||
    videoInfo.get('fatherCollectionIds')?.toJS() ||
    []
  if (fatherCollectionIds.length && videoInfo.get('albumId')) {
    const res = await store.dispatch(
      fetchConfigAction({
        pageSt: 'play_web',
        ctx: ctx,
        album_id: videoInfo.get('albumId'),
        _catch: {}
      })
    )
    const cards = res?.data?.cards || []
    const card = getCard('pcw_season_list', cards)[0]
    const blocks = card?.blocks || []
    blocks.forEach(item => {
      if (item.id) {
        let { kv_pair = {}, statistics = {} } = item
        superSeries.seasons.push({
          id: kv_pair.album_id,
          title: item.title,
          loc_Suffix_play: kv_pair.loc_Suffix_play,
          loc_suffix_album: kv_pair.loc_suffix_album,
          rseat: statistics.rseat
        })
        if (kv_pair.album_id == videoInfo.get('albumId')) {
          superSeries.curSeasonX = item.title
          superSeries.curSeason = kv_pair.album_id
        }
      }
    })
  }

  const defaultTvId = albumInfo.get('defaultTvId')
  const albumLocSuffix = albumInfo.get('albumLocSuffix')
  const tvId = videoInfo.get('tvId')
  if (playStatusCode === 404) {
    // http://pms.qiyi.domain/browse/GLOBALREQ-11146
    if (defaultTvId === 0 && albumLocSuffix) {
      res.writeHead(302, {
        Location: rebuildAlbumUrl(`${albumLocSuffix}`)
      })
    } else {
      res.writeHead(302, {
        Location: `https://www.iq.com?source=play${curFrom}`
      })
    }
  }
  if (vip_order && isMobile) {
    const reUrl = `https://www.iq.com/vip/order?qipuId=${tvId}${
      curUrlParams ? '&' + curUrlParams : ''
    }`
    res.writeHead(302, {
      Location: reUrl
    })
  }
  return {
    isServer,
    subtitleInfo,
    // shortSimilarData,
    movieRecommendData,
    playRankingData,
    curUrl,
    ctxIsMobile: isMobile,
    isSeo,
    prePlayerData,
    superSeries
  }
}

// async function getShortSimilar(params) {
//   try {
//     const ctx = params.ctx
//     delete params.ctx
//     const res = await $http(SHORT_SIMILAR_PLAY, {
//       params,
//       ctx
//     })
//     if (Number(res.code) === 0 && res.data.resys_element.length) {
//       res.data.urlParams = SHORT_SIMILAR_PLAY + '=' + JSON.stringify(params)
//       return res.data
//     }
//   } catch (e) {
//     console.error(e)
//   }
// }

async function getMovieRecommend(params) {
  try {
    const ctx = params.ctx
    delete params.ctx
    const res = await $http(FILM_REC, {
      params: {
        ...params,
        filters: params.qipuId,
        size: 20,
        channelId: 1
      },
      ctx
    })
    if (Number(res.code) === 0 && res.data.resys_element.length) return res.data
    return { resys_element: [] }
  } catch (e) {
    return { resys_element: [] }
    console.error(e)
  }
}

async function getPlayRanking(params, ctx) {
  try {
    const res = (await $http(PLAY_RANKING_SEVR, { params, ctx })) || {}
    if (
      Number(res.code) === 0 &&
      res.data &&
      res.data.epg &&
      res.data.epg.length
    ) {
      let tempData = []
      const webpTag = canUseWebp(ctx)
      res.data.epg.forEach(ele => {
        // albumWebpPic > albumPic > posterWebpPic > posterPic
        let imgUrl = handleImgSize(ele.albumPic, '_260_360.jpg', true) || ''
        if (webpTag) {
          imgUrl =
            handleImgSize(ele.albumWebpPic, '_260_360.webp', true) || imgUrl
        }
        if (!imgUrl) {
          imgUrl = handleImgSize(ele.posterPic, '_260_360.jpg', true)
          if (webpTag) {
            imgUrl =
              handleImgSize(ele.posterWebpPic, '_260_360.webp', true) || imgUrl
          }
        }
        if (ele.name && imgUrl && tempData.length < 10) {
          ele.hoverImg = imgUrl
          tempData.push(ele)
        }
      })
      return tempData
    }
  } catch (e) {
    console.error(e)
  }
}

const mapStateToProps = state => {
  return {
    // dispatch: state.dispatch,
    videoInfo: state.getIn(['play', 'videoInfo']),
    albumInfo: state.getIn(['play', 'albumInfo']),
    playStatusCode: state.getIn(['play', 'playStatusCode']),
    playerCreateSucess: state.getIn(['play', 'playerCreateSucess']),
    modeLangObj: state.getIn(['language', 'modeLangObj']),
    langPkg: state.getIn(['vip', 'vipLangPkg']),
    globalLangPkg: state.getIn(['language', 'langPkg']),
    playScoreInfo: state.getIn(['play', 'playScoreInfo']),
    curVideoPageInfo: state.getIn(['play', 'curVideoPageInfo']),
    cachePlayList: state.getIn(['play', 'cachePlayList']),
    cards: state.getIn(['commonConfig', 'play_web', 'cards']),
    userInfo: state.getIn(['user', 'userInfo'])
  }
}

export default connect(mapStateToProps)(Short)
