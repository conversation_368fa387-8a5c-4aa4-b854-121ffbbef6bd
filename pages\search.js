import React from 'react'
import { connect } from 'react-redux'
import { getDevice, getMobileType } from '@/kit/device'
import Meta from '@/components/common/Meta'
import TongJiPb from '@/components/common/TongJiPb'
import ResultBar from '@/components/pages/search/ResultBar'
import ResultList from '@/components/pages/search/ResultList'
import HotList from '@/components/pages/search/HotList'
import $http from '@/kit/fetch'
import Recommend from '@/components/pages/search/Recommend'
import { LANG_PKG } from '@/constants/interfaces'
import { platformId } from '@/kit/common'
import { queryFromUrl } from '@/kit/url'

import { fetchSearchResultAction } from '@/store/reducers/search/search'
import { fetchConfigAction } from '@/store/reducers/commonConfig/commonConfig'

class Search extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      isMobile: getDevice() === 'mobile',
      deviceType: getMobileType(),
      queryWd: ''
    }
  }

  async componentDidMount() {
    const { dispatch } = this.props
    const isMobile = getDevice() === 'mobile'
    this.setState({
      isMobile
    })

    // let url
    // try {
    //   url = decodeURI(document.location.href)
    // } catch (e) {
    //   url = document.location.href
    // }
    // let queryWd = queryFromUrl(url, 'query')

    window.addEventListener('resize', () => {
      const { isMobile, deviceType } = this.state
      const newIsMobile = getDevice() === 'mobile'
      const newDeviceType = getMobileType()
      if (isMobile !== newIsMobile) {
        this.setState({
          isMobile: newIsMobile
        })
      }
      if (deviceType !== newDeviceType) {
        this.setState({
          deviceType: newDeviceType
        })
      }
    })

    // if (queryWd === '') {
    //   // 可能输入的是query=&& 类似这种 特殊处理下
    //   const reg = /query=[&|=]{1,}$/g
    //   const result = url.match(reg)

    //   console.log('result:' + result)
    //   if (result) {
    //     queryWd = result[0].split('query=')[1]
    //   }
    // }

    // if (queryWd) {
    //   await dispatch(
    //     fetchSearchResultAction({
    //       pn: 1,
    //       key: queryWd
    //     })
    //   )

    //   this.setState({
    //     queryWd
    //   })
    // }
    await dispatch(fetchConfigAction({ pageSt: 'search_web' }))
  }

  static async getInitialProps({ ctx }) {
    const s2 = ctx.query.s2 || ''
    const s3 = ctx.query.s3 || ''
    const s4 = ctx.query.s4 || ''
    const queryWd = ctx.query.query || ''
    const { store } = ctx
    let domainPkg = {}

    try {
      const state = store.getState()
      const langCode = state.getIn(['language', 'modeLangObj', 'lang'])
      const prodLangPkgRes = await $http(LANG_PKG, {
        params: {
          langCode: langCode || 'zh_cn',
          platformId: platformId()
        }
      })
      if (Number(prodLangPkgRes.code) === 0) {
        domainPkg = prodLangPkgRes.data
      }
    } catch (err) {
      console.log(err)
    }

    if (queryWd) {
      await store.dispatch(
        fetchSearchResultAction({
          pn: 1,
          key: queryWd,
          ctx
        })
      )
    }

    return {
      s2s3s4: {
        s2,
        s3,
        s4
      },
      domainPkg,
      queryWd
    }
  }

  render() {
    const { langPkg, hotSearch, searchResult, s2s3s4, domainPkg } = this.props
    const { queryWd } = this.props
    const trueLangPkg = langPkg.toJS()
    const trueHotSearch = hotSearch.toJS()
    const trueSearchResult = searchResult.toJS()
    // const starsInfo =
    //   trueSearchResult.starsInfo.height
    const { total } = trueSearchResult
    const rowContainerStyle = {
      position: 'relative',
      fontSize: '0',
      overflow: 'visible',
      whiteSpace: 'nowrap'
    }
    return (
      <>
        <Meta
          desc={trueLangPkg.home_html_description}
          title={trueLangPkg.home_html_title}
        />
        <div className="search-container" style={{ minHeight: '100vh' }}>
          <ResultBar searchResult={trueSearchResult} domainPkg={domainPkg} />
          <div style={{ minHeight: '80vh' }}>
            {total === -1 ? (
              ''
            ) : total > 0 ? (
              <ResultList
                queryWd={queryWd}
                searchResult={trueSearchResult}
                hotSearch={trueHotSearch}
              />
            ) : (
              <div className="row-container" style={rowContainerStyle}>
                <HotList hotSearch={trueHotSearch} />
                <Recommend />
              </div>
            )}
            <TongJiPb
              rpage="search_rst"
              pbShowParams={{ ...s2s3s4, bstp: 2 }}
            />
          </div>
        </div>
      </>
    )
  }
}

const mapStateToProps = state => ({
  searchResult: state.getIn(['search', 'result']),
  langPkg: state.getIn(['language', 'langPkg']),
  hotSearch: state.getIn(['search', 'hotSearch'])
})

export default connect(mapStateToProps)(Search)
