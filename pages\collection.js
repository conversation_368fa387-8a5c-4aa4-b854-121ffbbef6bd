import React from 'react'
import { connect } from 'react-redux'
import { getCookies, getCtxDevice } from '@/kit/cookie'
import { checkQipuIdStr } from '@/kit/url'
import $http from '@/kit/fetch'
import { getUid } from '@/utils/userInfo'
import TongJiPb from '@/components/common/TongJiPb'
import CollectionMeta from '@/components/pages/collection/collectionMeta'
import { commonDeviceIdParams, getCollectionData } from '@/utils/apis'
import CollectionList from '@/components/pages/collection/CollectionList'
import CollectionFocus from '@/components/pages/collection/CollectionFocus'
import { handleCollectionData } from '@/store/sagas/home/<USER>'
import { DECODE_ID } from '@/constants/interfaces'
import Error from './_error'

// http://wiki.qiyi.domain/pages/viewpage.action?pageId=1385630480
class Collection extends React.Component {
  constructor(props) {
    super(props)
    this.state = {}
  }

  static async getInitialProps({ ctx }) {
    const { query } = ctx
    const { id } = query
    // const idArr = id.split('-')
    // const idArrLen = idArr.length
    // let displayName = ''
    // {name}-{id} 可能只有id;name里也可能有'-'。
    // if (idArrLen > 1) {
    //   displayName = idArr.slice(0, idArrLen - 1).join('-')
    // }
    const encodeId = checkQipuIdStr(id)
    const path = ctx.req.url.split('?')[0]
    let qipuId
    let formatData
    const initParams = {
      ...commonDeviceIdParams(ctx),
      langCode:
        ctx.req.headers['langCode'] ||
        ctx.req.headers['langcode'] ||
        getCookies('lang', ctx) ||
        'en_us',
      modeCode:
        ctx.req.headers['modeCode'] ||
        ctx.req.headers['modecode'] ||
        getCookies('mod', ctx) ||
        'intl'
    }
    const params = {
      ...initParams,
      uid: getUid()
    }
    const options = { params }
    options.ctx = ctx
    if (encodeId) {
      const decodeIdData = await $http(DECODE_ID + encodeId, options)
      if (decodeIdData.code === '0') {
        qipuId = decodeIdData.data
      }
    }

    const immutableParams = {
      ...initParams,
      sid: initParams.deviceId + '_' + new Date().getTime()
    }
    const firstData =
      qipuId &&
      (await getCollectionData({
        ...immutableParams,
        resIds: qipuId + '_1_1',
        // collectionName: displayName,
        startOrder: 1
      }))
    if (firstData) {
      formatData = handleCollectionData(firstData, {
        rpage: 'play_list',
        block: 'video',
        ctx,
        mod: getCookies('mod', ctx) || 'intl',
        isMobile: getCtxDevice(ctx) === 'mobile'
      })
    }
    return { formatData, path, qipuId, immutableParams }
  }

  render() {
    const { formatData, path, qipuId, langPkg, immutableParams } = this.props
    if (!formatData) {
      return <Error statusCode={400} rpage="play_list" />
    }

    const trueLangPkg = langPkg.toJS()
    const newData = { ...formatData }
    newData.focusInfo.displayName =
      newData.focusInfo.displayName ||
      trueLangPkg.collections_default_name ||
      ''
    const {
      collectionType,
      collectionSubType,
      collectionImage,
      collectionLocSuffix,
      collectionHrefLangPile,
      focusInfo,
      nextOrder,
      videos
    } = newData
    const normalStyle = !(
      collectionType === 'PLAYLIST' &&
      collectionSubType === 'COLLECTION_SUB_TYPE_INTL_RANKING'
    )
    const { displayName, description } = focusInfo

    return (
      <>
        <CollectionMeta
          title={displayName}
          desc={description || displayName}
          path={path}
          collectionImage={collectionImage}
          collectionLocSuffix={collectionLocSuffix}
          collectionHrefLangPile={collectionHrefLangPile}
        />
        <CollectionFocus focusInfo={focusInfo} normalStyle={normalStyle} />
        <CollectionList
          qipuId={qipuId}
          nextOrder={nextOrder}
          displayName={displayName}
          firstListData={videos || []}
          normalStyle={normalStyle}
          initParams={immutableParams}
        />
        <TongJiPb rpage="play_list" pbShowParams={{ rId: qipuId }} />
      </>
    )
  }
}

const mapStateToProps = state => ({
  langPkg: state.getIn(['language', 'langPkg'])
})

export default connect(mapStateToProps)(Collection)
