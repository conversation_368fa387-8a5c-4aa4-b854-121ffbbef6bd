import React from 'react'
import { connect } from 'react-redux'
import throttle from 'lodash.throttle'
import { isServer } from '@/kit/device'
import { getQueryParam } from '@/utils/commonUtil'
import { getNavigator, getQueryVariable } from '@/utils/tvUtils/tvResquet'
import { fetchTvLangActions } from '@/store/reducers/tvRegister/tvRegister'

import TvRegisterPage from '@/components/pages/tvRegister'

if (!isServer) {
  // 此处绑定键盘事件
  window.selfHandleKeyDownEvent = null
  window.keydownfun = event => {
    if (window.popwindowShow && window.callbackPopwindowKeyfun) {
      window.callbackPopwindowKeyfun(event)
    } else if (window.isFrameShowing && window.handleFrameKeyevent) {
      window.handleFrameKeyevent(event)
    } else if (window.licenseShow && window.callbacklicenseEventKeyfun) {
      window.callbacklicenseEventKeyfun(event)
    } else if (window.selfHandleKeyDownEvent) {
      // console.log(
      //   'window.selfHandleKeyDownEvent-->',
      //   window.selfHandleKeyDownEvent
      // )
      window.selfHandleKeyDownEvent(event)
    }
  }

  window.mouseDownfun = event => {
    if (window.popwindowShow && window.callbackPopmouseDownfun) {
      window.callbackPopmouseDownfun(event)
    } else if (window.isFrameShowing && window.handleFrameMouseDown) {
      window.handleFrameMouseDown(event)
    } else if (window.selfHandleMouseDownEvent) {
      window.selfHandleMouseDownEvent(event)
    }
  }

  window.addEventListener('keydown', event => {
    if (window.keydownfun) {
      window.keydownfun(event)
    }
  })

  window.addEventListener('message', e => {
    // console.log('message--e-->', e)
    if (window.keydownfun) {
      try {
        if (typeof e.data === 'string' && e.data?.startsWith('{"keyCode":')) {
          const event = JSON.parse(e.data)
          window.keydownfun(event)
        }
      } catch (error) {
        console.log(error)
      }
    }
  })
}

class TvRegister extends React.Component {
  constructor(props) {
    super(props)
    this.state = {}
  }

  static async getInitialProps({ ctx }) {
    const { store, req } = ctx
    const url = req.url || ''
    const lang = getQueryParam('lang', url) || 'en_us'
    const { dispatch } = store
    dispatch(fetchTvLangActions({ lang }))
    return {}
  }

  componentDidMount() {
    // this.setRem()
    if (!isServer) {
      this.addLib()
    }
  }

  addLib() {
    const navigator = getNavigator()
    const agentStr = navigator.userAgent.toLowerCase()
    if (agentStr.indexOf('vizio') !== -1) {
      this.loadscriptUrl(
        'http://localhost:12345/scfs/cl/js/vizio-companion-lib.js'
      )
      document.addEventListener('VIZIO_LIBRARY_DID_LOAD', async function(e) {
        console.log('vizio account support--->', window.VIZIO._deviceInfoStore)
      })
    }
    return 'vizio'
  }

  loadscriptUrl(url) {
    const script = document.createElement('script')
    script.type = 'text/javascript'
    script.src = url
    document.getElementsByTagName('head')[0].appendChild(script)
  }

  render() {
    // const { tvLangPkg } = this.props
    // const trueLangPkg = tvLangPkg.toJS()
    return (
      <>
        <script>this.globalThis || (this.globalThis = this)</script>
        <style jsx global>
          {`
            @media (min-width: 1280px) {
              html {
                font-size: 66.666px !important;
              }
            }

            @media (min-width: 1920px) {
              html {
                font-size: 100px !important;
              }
            }
            @media (min-width: 3840px) {
              html {
                font-size: 200px !important;
              }
            }
          `}
        </style>
        <TvRegisterPage />
      </>
    )
  }
}

const mapStateToProps = state => ({
  tvLangPkg: state.getIn(['tvRegister', 'tvLangPkg']),
  hasMore: state.getIn(['commonConfig', 'anime_web', 'hasMore'])
})
export default connect(mapStateToProps)(TvRegister)
