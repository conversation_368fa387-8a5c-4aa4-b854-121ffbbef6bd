import { getCookies } from '@/kit/cookie'
import createNewDate from '@/utils/newDate'

export const isLogin = ctx => {
  const i19Val = getCookies('I00019', ctx)
  return parseInt(i19Val, 10) === 1
}

export const getUid = ctx => {
  let I00002 = getCookies('I00002', ctx)
  if (I00002 && I00002 !== null && I00002 !== '') {
    I00002 = JSON.parse(I00002)
    const { uid } = I00002.data
    return uid
  }
  return ''
}

// http://wiki.qiyi.domain/pages/viewpage.action?pageId=406751122
export const getCurVipInfo = (allVipList = []) => {
  const desclist = ['vip_basic', 'vip_gold', 'vip_diamond'] // 身份列表 // index越大优先级越高 钻石>黄金>basic
  let highExpire
  let highUnExpire
  for (let i = 0; i < allVipList.length; i++) {
    const info = allVipList[i]
    // info.vipType = parseInt(info.vipType,10)
    if (info.expire === 0) {
      // 没过期
      if (
        highExpire === undefined ||
        desclist.indexOf(info.vipTypeDesc) >
          desclist.indexOf(highExpire.vipTypeDesc)
      ) {
        highExpire = info
      }
    } else if (
      // 过期
      highUnExpire === undefined ||
      desclist.indexOf(info.vipTypeDesc) >
        desclist.indexOf(highUnExpire.vipTypeDesc)
    ) {
      highUnExpire = info
    }
  }
  return highExpire || highUnExpire || {} // 返回最高级别的会员
}

export const getUserLevelDesc = allVipList => {
  // 如果不是数组 代表pvp 接口没回来了
  if (!Array.isArray(allVipList)) {
    return ''
  }
  const levelMap = {
    free_user: 'free_user', // 无会员用户
    premium: 'premium', // 单一未到期钻石会员
    standard: 'standard', // 单一未到期黄金会员
    basic: 'basic', // 单一未到期basic会员
    vip_same_deadline: 'vip_same_deadline', // 多种未到期会员且到期时间相同
    vip_more_than_one: 'vip_more_than_one', // 多种未到期会员且到期时间不同
    vip_expired: 'vip_expired' // 已有会员都过期
  }
  let resultObj = {
    userLevelId: '',
    descBoxArr: [],
    descDeadline: '',
    descDeadlineLong: ''
  }
  const unExpireList = []
  const expireList = []
  let sameDeadlineTag = true
  for (let i = 0; i < allVipList.length; i++) {
    const info = allVipList[i]
    if (info.expire === 0) {
      // http://pms.qiyi.domain/browse/GLOBALLINEDEV-9053
      const newDate = createNewDate()
      info.deadline =
        info.deadlineLong && new newDate(info.deadlineLong).Format('yyyy-MM-dd')
      unExpireList.push(info)
      // 判断多种未到期会员的到期时间是否相同
      if (
        info.deadline.slice(0, 10) === unExpireList[0].deadline.slice(0, 10) &&
        sameDeadlineTag
      ) {
        sameDeadlineTag = true
      } else {
        sameDeadlineTag = false
      }
      // 单一和多种未到期会员展示到期时间
      if (
        !resultObj.descDeadline ||
        info.deadlineLong > resultObj.descDeadlineLong
      ) {
        resultObj.descDeadline = info.deadline
        resultObj.descDeadlineLong = info.deadlineLong
      }
    } else {
      expireList.push(info)
    }
  }

  if (unExpireList.length > 1 && sameDeadlineTag) {
    resultObj = Object.assign(resultObj, {
      userLevelId: levelMap['vip_same_deadline'],
      descBoxArr: [].concat(unExpireList[0])
    })
  } else if (unExpireList.length > 1) {
    resultObj = Object.assign(resultObj, {
      userLevelId: levelMap['vip_more_than_one'],
      descBoxArr: unExpireList
    })
  } else if (unExpireList.length === 1) {
    resultObj = Object.assign(resultObj, {
      descBoxArr: [].concat(unExpireList[0])
    })

    if (unExpireList[0].vipTypeDesc === 'vip_diamond') {
      resultObj.userLevelId = levelMap['premium']
    } else if (unExpireList[0].vipTypeDesc === 'vip_gold') {
      resultObj.userLevelId = levelMap['standard']
    } else if (unExpireList[0].vipTypeDesc === 'vip_basic') {
      resultObj.userLevelId = levelMap['basic']
    }
  } else if (unExpireList.length === 0 && expireList.length > 0) {
    resultObj.userLevelId = levelMap['vip_expired']
  } else if (allVipList.length === 0) {
    resultObj.userLevelId = levelMap['free_user']
  }
  return resultObj
}
