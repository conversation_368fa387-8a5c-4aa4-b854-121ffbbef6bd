import React from 'react'
import { connect } from 'react-redux'
import H5<PERSON>allApp from '@iqiyi-ibd/global-h5-callApp'
import { rebuildCommonUrl, platformId } from '@/kit/common'
import DeviceCtx from '@/components/context'
import { imgUrl } from '@/utils/imgUrl'
import { sendBlockPb } from '@/utils/pingBack'
import { GridCardVipIcon, GridCardTvodIcon } from '@/constants/style'
import sendGtag from '@/utils/gtag'
import { queryFromUrl, removeProtocol } from '@/kit/url'
import { supportUrlPbMod } from '@/utils/video'
import GridCardWrapper from '@/components/common/GridCard/style'
import ReserveButton from '@/components/common/ReserveButton'
import { fromJS } from 'immutable'
import { isLogin } from '@/utils/userInfo'
import { isServer } from '@/kit/device'
import { getCookies } from '@/kit/cookie'
import { BATCH_AUTH } from '@/constants/interfaces'
import $http from '@/kit/fetch'

export class GridCard extends React.Component {
  static contextType = DeviceCtx

  constructor(props) {
    super(props)

    this.state = {
      isSend: false,
      dramaMaxNum: 0,
      dramaHeadNum: 0,
      dramaTailNum: 0,
      varietyShowMaxOrder: 0,
      H5Style: false,
      lockData: {}
    }

    this.castRef = React.createRef()
    this.directorRef = React.createRef()
    this.gridCardRef = React.createRef()
  }

  componentDidMount() {
    const { isMobile } = this.context
    const castNode = this.castRef.current
    const directorNode = this.directorRef.current
    let linHeight = 0
    let dramaMaxNum
    let dramaHeadNum
    let dramaTailNum
    let varietyShowMaxOrder
    let H5Style = false

    const clientWidth = document.body.clientWidth
    if (clientWidth >= 1680) {
      linHeight = 19
      dramaMaxNum = 28
      dramaHeadNum = 12
      dramaTailNum = 14
      varietyShowMaxOrder = 4
    } else if (clientWidth >= 1024) {
      linHeight = 19
      dramaMaxNum = 18
      dramaHeadNum = 8
      dramaTailNum = 9
      varietyShowMaxOrder = 4
    } else if (clientWidth <= 767) {
      linHeight = 14
      dramaMaxNum = 6
      dramaHeadNum = 1
      dramaTailNum = 4
      varietyShowMaxOrder = 2
      H5Style = true
    } else {
      linHeight = 16
      dramaMaxNum = 22
      dramaHeadNum = 10
      dramaTailNum = 11
      varietyShowMaxOrder = 4
    }

    this.setState({
      dramaMaxNum,
      dramaHeadNum,
      dramaTailNum,
      varietyShowMaxOrder,
      H5Style
    })
    this.callSurplus(castNode, isMobile ? linHeight : linHeight * 3)
    this.callSurplus(directorNode, linHeight)

    if (this.gridCardRef.current) {
      this.detectBlockShow(this.gridCardRef.current)
    }

    window.addEventListener('scroll', () => {
      this.detectBlockShow(this.gridCardRef.current)
    })

    window.addEventListener('resize', () => {
      const clientWidth = document.body.clientWidth
      let H5Style = false
      if (clientWidth >= 1680) {
        linHeight = 19
        dramaMaxNum = 28
        dramaHeadNum = 12
        dramaTailNum = 14
        varietyShowMaxOrder = 4
      } else if (clientWidth >= 1024) {
        linHeight = 19
        dramaMaxNum = 18
        dramaHeadNum = 8
        dramaTailNum = 9
        varietyShowMaxOrder = 4
      } else if (clientWidth <= 767) {
        linHeight = 14
        dramaMaxNum = 6
        dramaHeadNum = 1
        dramaTailNum = 4
        varietyShowMaxOrder = 2
        H5Style = true
      } else {
        linHeight = 16
        dramaMaxNum = 22
        dramaHeadNum = 10
        dramaTailNum = 11
        varietyShowMaxOrder = 4
      }

      this.setState({
        dramaMaxNum,
        dramaHeadNum,
        dramaTailNum,
        varietyShowMaxOrder,
        H5Style
      })
      this.callSurplus(castNode, isMobile ? linHeight : linHeight * 3)
      this.callSurplus(directorNode, linHeight)
    })

    this.getLockData(this.props.item.lockAid)
  }

  getChnType(chnId, trueLangPkg) {
    let chnType

    switch (chnId) {
      case 1:
        chnType = trueLangPkg['navigation_movie']
        break
      case 2:
        chnType = trueLangPkg['navigation_drama']
        break
      case 4:
        chnType = trueLangPkg['navigation_anime']
        break
      case 6:
        chnType = trueLangPkg['navigation_variety_show']
        break
      case 15:
        chnType = trueLangPkg['childtab']
        break
      case 7:
        chnType = trueLangPkg['yuletab']
        break
      case 3:
        chnType = trueLangPkg['jilupiantab']
        break
      default:
        chnType = undefined
        break
    }
    return chnType
  }

  getBlock(chnId, contentType, qipuId) {
    qipuId = '' + qipuId
    if (qipuId && qipuId.slice(-2) === '02') {
      // 播单
      return 'playlist'
    }

    if (contentType === 3) {
      // 预告片
      return 'preview'
    }

    if (chnId === 1) {
      // 电影 是长视频
      return 'video_L'
    } else if (chnId === 2 || chnId === 4) {
      // 电视剧 动漫
      return 'album_J'
    } else if (chnId === 6) {
      return 'album_Q'
    } else {
      return 'video_S'
    }
  }

  detectBlockShow(current) {
    const { index, result } = this.props

    const trueResult = result.toJS()
    const { pingback, pn } = trueResult

    if (current) {
      const rect = current.getBoundingClientRect()
      const { chnId, contentType, qipuId } = this.props.item
      if (window.innerHeight - rect.top > 50 && rect.bottom > 50) {
        const { isSend } = this.state
        const href = window.location.href
        if (!isSend) {
          sendBlockPb('list', {
            rpage: 'search_rst',
            tjPb: {
              block: this.getBlock(chnId, contentType, qipuId),
              bstp: 2,
              bkt: pingback.bkt,
              e: pingback.eventId,
              c1: chnId,
              position: index + 1,
              s_docids: `${qipuId};${chnId}`,
              s_il: `${qipuId};iqiyi;${chnId};${index + 1}`,
              s_r: decodeURIComponent(queryFromUrl(href, 'query')),
              s_page: pn,
              s_mode: 1,
              s_source: queryFromUrl(href, 's4') || '',
              s_qr: pingback.isReplaced,
              s_rq: pingback.realQuery,
              s_token:
                queryFromUrl(href, 's4') === 'suggest'
                  ? decodeURIComponent(queryFromUrl(href, 'originInput'))
                  : '',
              s2: queryFromUrl(href, 's2'),
              s3: queryFromUrl(href, 's3'),
              s4: queryFromUrl(href, 's4')
            }
          })
          this.setState({
            isSend: true
          })
        }
      } else {
        this.setState({
          isSend: false
        })
      }
    }
  }

  removeSurplusChild(node, maxHeight) {
    const childs = node.childNodes
    const curHeight = node.offsetHeight

    if (curHeight < maxHeight) {
      return
    }

    const removeChildsArr = []
    for (let i = 0; i < childs.length; i++) {
      if (childs[i].offsetTop >= maxHeight) {
        removeChildsArr.push(childs[i])
      }
    }
    removeChildsArr.forEach(item => {
      node.removeChild(item)
    })
  }

  callSurplus(node, maxHeight) {
    if (!node) {
      return
    }
    this.removeSurplusChild(node, maxHeight)
  }

  handleRowImgRender = (item, { pb, pbTag }) => {
    const { url, name, cast, categoryTagMap, contentType, len } = item
    const { langPkg, result, currentLang } = this.props
    const { Place = [] } = categoryTagMap
    const trueLangPkg = langPkg.toJS()
    const chnType = this.getChnType(item.chnId, trueLangPkg)
    const { isMobile } = this.context
    const typeArr = [...Place, { name: chnType }]
    const pbPreview = this.handlePb(contentType === 3 ? 'preview' : 'video_S')
    // const pbVideo = this.handlePb('video_S')
    const pendZero = (count, num) =>
      num >= 10 ? num : Array(count).join(0) + num
    let hour
    let minute
    let lenHour
    let lenMinute
    if (len) {
      hour = parseInt(len / 60, 10)
      minute = len % 60
      lenHour = hour > 0 ? pendZero(2, hour) : '00'
      lenMinute = minute > 0 ? pendZero(2, minute) : '00'
    }

    let pingback
    if (result) {
      pingback = result.toJS()['pingback']
    }

    let commaStr = ', '
    let colonStr = ': '
    if (currentLang === 'zh_cn' || currentLang === 'zh_tw') {
      commaStr = '，'
      colonStr = '：'
    }

    return (
      <>
        {/* <div className="row-title">
          {chnType ? <span className="type">{chnType}</span> : ''}
          {contentType === 3 ? (
            <span className={`title-preview ${isMobile ? 'isMobile' : ''}`}>
              {trueLangPkg.search_rst_preview}
            </span>
          ) : null}
        </div> */}
        <a
          rseat={contentType === 3 ? 'preview' : 'play'}
          data-pb={pbPreview + '&s_ptype=1-1-1'}
          href={`${pbTag === 1 ? url + '&frmrs=title' : url}`}
          className="row-img"
          style={{
            backgroundImage: `url(${removeProtocol(
              imgUrl(item.albumPic, '_480_270')
            )})`,
            backgroundSize: '100%'
          }}
          target="_blank"
          rel="noreferrer"
        >
          {len ? (
            <i className="duration">
              {lenHour}:{lenMinute}
            </i>
          ) : null}
        </a>
        <div className="detail">
          <a
            href={`${pbTag === 1 ? url + '&frmrs=title' : url}`}
            rseat={contentType === 3 ? 'preview' : 'play'}
            data-pb={
              pbPreview + '&s_rq=' + pingback.realQuery + '&s_ptype=1-1-2'
            }
            dangerouslySetInnerHTML={{ __html: name }}
            target="_blank"
            className="name"
            rel="noreferrer"
          />
          {cast.length ? (
            <div className="group cast noHover" ref={this.castRef}>
              <span className="label">
                {trueLangPkg.play_main_character}
                {colonStr}
              </span>
              <div className="row-img-castWrap">
                {cast.map((ct, index) => {
                  const html =
                    ct.n && ct.n + (index === cast.length - 1 ? '' : commaStr)
                  return (
                    <a
                      href={rebuildCommonUrl(
                        `actor-info/${ct.titleSEO || ct.id}`
                      )}
                      target="_blank"
                      className="val"
                      key={ct.id}
                      role="button"
                      data-pb={pb + '&s_rq=' + ct.n + '&s_ptype=1-1-4'}
                      rseat={'character_' + ct.n}
                      dangerouslySetInnerHTML={{ __html: html }}
                      rel="noreferrer"
                    />
                  )
                })}
              </div>
            </div>
          ) : null}
          {typeArr.length && !isMobile ? (
            <div className="group resultType">
              <span className="label">
                {trueLangPkg.play_type}
                {colonStr}
              </span>
              {typeArr.map((place, index) => {
                return (
                  <React.Fragment key={place.name}>
                    <a href="#!" className="val" key={place.name} role="button">
                      {place.name}
                    </a>
                    {index === typeArr.length - 1 ? null : commaStr}
                  </React.Fragment>
                )
              })}
            </div>
          ) : null}
        </div>
      </>
    )
  }

  handlePb = block => {
    if (!global.window) return
    // t=20
    const { item, index, result } = this.props
    let pingback = {}
    let pn
    if (result) {
      pingback = result.toJS()['pingback']
      pn = result.toJS()['pn']
    }
    const { qipuId, chnId } = item
    const href = window.location.href

    return `
    block=${block}&
    rpage=search_rst&
    bkt=${pingback.bkt}&
    bstp=2&
    e=${pingback.eventId}&
    c1=${chnId}&
    qpid=${qipuId}&
    position=${index + 1}&
    s_r=${decodeURIComponent(queryFromUrl(href, 'query'))}&
    s_page=${pn}&
    s_mode=1&
    s_source=${queryFromUrl(href, 's4')}&
    s_qr=${pingback.isReplaced}&
    s_rq=${pingback.realQuery}&
    s_token=${
      queryFromUrl(href, 's4') === 'suggest'
        ? decodeURIComponent(queryFromUrl(href, 'originInput'))
        : ''
    }&
    s_ptype=&
    s_target=${qipuId}&
    s2=${queryFromUrl(href, 's2')}&
    s3=${queryFromUrl(href, 's3')}&
    s4=${queryFromUrl(href, 's4')}&
    `
  }

  toApp(url, item, block, rseat) {
    const { isMobile } = this.context
    const { currentMod } = this.props
    if (!isMobile) {
      window.open(url)
      // window.location.href = url
      return
    }
    const { qipuId, albumId } = item
    const videoData = {
      albumId: +albumId,
      tvid: +albumId === +qipuId ? 0 : +qipuId,
      rpage: 'search_rst',
      block,
      rseat,
      mod: currentMod
    }

    const h5CallApp = new H5CallApp(videoData)
    h5CallApp.callApp('play')
  }

  async getLockData(lockAid) {
    if (!isServer && isLogin() && lockAid.length) {
      let newLockAid = lockAid
      let otherLockAid = []

      if (lockAid.length > 20) {
        newLockAid = lockAid.slice(0, 20)
        otherLockAid = lockAid.slice(20)
      }

      if (otherLockAid.length) {
        this.getLockData(otherLockAid)
      }

      const data = await this.fetchLockList(newLockAid)

      this.setState(prevState => {
        Object.assign(prevState.lockData, data)
      })
    }
  }

  async fetchLockList(lockAid) {
    const options = {}

    options.params = {
      modeCode: getCookies('mod'),
      langCode: getCookies('lang'),
      platformId: platformId(),
      deviceId: getCookies('QC005'),
      aids: lockAid.join(',')
    }

    const data = await $http(BATCH_AUTH, options)
    if (+data.code === 0) {
      const obj = {}
      data.data.map(item => {
        obj[item.aid] = item.subscribe
      })
      return obj
    }
    return {}
  }

  render() {
    const { lockData } = this.state

    const {
      item,
      langPkg,
      result,
      currentLang,
      currentMod,
      langs,
      userInfo,
      curVipList
    } = this.props
    const {
      name,
      publishYear,
      director = [],
      cast,
      url,
      contentType,
      episodeList,
      qipuId,
      chnId,
      albumUrl,
      controlStatus
    } = item
    const curVip = curVipList.toJS()
    const userinfo = userInfo.toJS()
    const trueLangPkg = langPkg.toJS()
    const chnType = this.getChnType(item.chnId, trueLangPkg)
    const showVipIcon = item.vipInfo.payMark === 'VIP_MARK'
    const showTvodIcon =
      item.vipInfo.payMark === 'PAY_ON_DEMAND_MARK' &&
      item.vipInfo?.isTvod === 1
    const tvodRatio =
      currentLang === 'zh_cn' ? 1 : currentLang === 'zh_tw' ? 2 : 3
    const showPreviecIcon = item.contentType === 3
    const block = this.getBlock(chnId, contentType, qipuId)
    const pb = this.handlePb(block)
    let pbTag = 0
    if (url.indexOf('frmrp') !== -1 && url.indexOf('frmrs') === -1) {
      // url已被添加播放器pingback所需参数，frmrs为各自的rseat值，所以渲染时添加
      pbTag = 1
    }
    const {
      dramaMaxNum,
      dramaHeadNum,
      dramaTailNum,
      varietyShowMaxOrder,
      H5Style
    } = this.state
    let EpisodeBlock = null
    const len = episodeList ? episodeList.length : 0

    let pingback
    if (result) {
      pingback = result.toJS()['pingback']
    }

    const moreBlock = (
      <div className={`${item.chnId === 6 ? 'variety-show-more' : ''} more`}>
        <a
          href={`${pbTag === 1 ? albumUrl + '&frmrs=more' : albumUrl}`}
          rseat="more"
          data-pb={pb}
        >
          {trueLangPkg.shareProgramInfoShowMore}
          <img
            rseat="more"
            data-pb={pb}
            alt="more"
            src="//www.iqiyipic.com/common/fix/global/global_search_more.png"
          />
        </a>
      </div>
    )
    const itemBlock = episode => {
      const order = episode.order
      let frmrsMsg = ''
      if (supportUrlPbMod(currentMod)) {
        frmrsMsg = '&frmrs=fenji_button'
      }
      let iconSpan = ''
      const lockType = episode?.vipInfo?.advancedUnlockType

      if (episode?.vipInfo?.payMark === 'VIP_MARK') {
        iconSpan = <span className="vip-icon" />
      } else if (episode?.vipInfo?.payMark === 'PAY_MARK_ONLY_DIAMOND') {
        // 仅砖石可看
        iconSpan = <span className="vip-icon lock" />
        if (userinfo?.isVip && curVip?.vipTypeDesc === 'vip_diamond') {
          // 砖石权益可看
          iconSpan = <span className="vip-icon unlock" />
        }
      } else if (lockType === 0 || lockType === 1) {
        // 超前解锁
        iconSpan = <span className="vip-icon lock" />
        if (this?.state?.lockData[episode.episodeId] === '1') {
          // 有超前解锁权益可看
          iconSpan = <span className="vip-icon unlock" />
        }
      }

      return (
        <li key={episode['qipuStr']} className="drama-item">
          <a
            data-pb={
              pb +
              `&rseat=${episode.episodeType ? 'trailer' : 'play'}&s_ptype=1-1-5`
            }
            href={`${episode.url}${frmrsMsg}`}
            target="_blank"
            rel="noreferrer"
          >
            <span>{order}</span>
          </a>
          {iconSpan}
          {episode.episodeType ? <span className="preview" /> : <></>}
        </li>
      )
    }
    if (episodeList && len > 0) {
      const lastEpisodeOrder = episodeList[len - 1].order // 最大order
      if (item.chnId === 2 || item.chnId === 4) {
        // 分集
        let currIndex = 1 // 当前索引， 从1开始因为之前数量也是从1开始
        const lsIndex = len - dramaTailNum + 1 // 第二行开始位置的索引 第二行是dramaTailNum个
        if (lastEpisodeOrder > dramaMaxNum || len > dramaMaxNum) {
          EpisodeBlock = (
            <React.Fragment key={item['qipuId']}>
              <ul className="series-block">
                {episodeList.map(episode => {
                  let tempNode = null
                  const order = episode.order
                  if (currIndex < dramaHeadNum) {
                    tempNode = itemBlock(episode)
                  } else if (currIndex === dramaHeadNum) {
                    tempNode = (
                      <React.Fragment key={episode['qipuStr']}>
                        {itemBlock(episode)}
                        <li key="ellipsis" className="drama-item">
                          <a
                            href={`${pbTag === 1 ? url + '&frmrs=more' : url}`}
                            rseat="more"
                            data-pb={pb}
                            target="_blank"
                            rel="noreferrer"
                          >
                            <span>...</span>
                          </a>
                        </li>
                      </React.Fragment>
                    )
                  } else if (currIndex >= lsIndex) {
                    tempNode = itemBlock(episode)
                  }
                  currIndex++
                  return tempNode
                })}
              </ul>
              {dramaMaxNum > 0 ? moreBlock : ''}
            </React.Fragment>
          )
        } else {
          EpisodeBlock = (
            <React.Fragment key={item['qipuId']}>
              <ul className="series-block">
                {episodeList.map(episode => {
                  return itemBlock(episode)
                })}
              </ul>
            </React.Fragment>
          )
        }
      } else if (item.chnId === 6) {
        // 分期
        EpisodeBlock = (
          <React.Fragment key={item['qipuId']}>
            <ul className="series-block variety-wrapper">
              {episodeList.map((episode, index) => {
                if (index < varietyShowMaxOrder) {
                  let frmrsMsg = ''
                  if (supportUrlPbMod(currentMod)) {
                    frmrsMsg = '&frmrs=fenji_button'
                  }
                  return (
                    <li key={episode.name} className="variety-show-item">
                      <a
                        rseat="play"
                        data-pb={pb}
                        href={`${episode.url}${frmrsMsg}`}
                        target="_blank"
                        rel="noreferrer"
                      >
                        {episode.name}
                      </a>
                    </li>
                  )
                }
                return null
              })}
            </ul>
            {/* http://pms.qiyi.domain/browse/GLOBALLINEDEV-4490 */}
            {!(len < varietyShowMaxOrder) && moreBlock}
          </React.Fragment>
        )
      }
    }
    const { isMobile } = this.context
    /**
     * 在儿童、娱乐、纪录片频道下，视频时长（字段：len）小于3分钟，也判断为短视频
     * 如果不是上述三个频道则通过contentType判断
     */
    const isGivenChnid = [15, 7, 2].find(v => v === item.chnId)
    const isSortVideo =
      isGivenChnid && item.len ? item.len <= 180 : contentType !== 1

    // http://wiki.qiyi.domain/pages/viewpage.action?pageId=1702691596
    let isPlayControl = false

    if ((chnId === 2 || chnId === 4 || chnId === 6) && len === 0) {
      isPlayControl = true
    }

    if (chnId === 1 && (controlStatus === null || controlStatus === 0)) {
      // 条件由后端提供 未上线
      isPlayControl = true
    }
    let commaStr = ', '
    let colonStr = ': '
    if (currentLang === 'zh_cn' || currentLang === 'zh_tw') {
      commaStr = '，'
      colonStr = '：'
    }

    return (
      <GridCardWrapper
        ref={this.gridCardRef}
        data-id={`${qipuId}`}
        data-chnid={chnId}
        className={`${isSortVideo ? 'sort-video-container' : ''} ${
          isMobile ? 'mobile' : ''
        } ${EpisodeBlock ? '' : 'bottomLine'}`}
      >
        {/* 分类标题展示移动到外面 */}
        {!isSortVideo ? (
          chnType ? (
            <span className="type">{chnType}</span>
          ) : (
            ''
          )
        ) : (
          <div className="row-title">
            {chnType ? <span className="type">{chnType}</span> : ''}
            {contentType === 3 ? (
              <span className={`title-preview ${isMobile ? 'isMobile' : ''}`}>
                {trueLangPkg.search_rst_preview}
              </span>
            ) : null}
          </div>
        )}
        <div className="grid-card-container">
          {!isSortVideo ? ( // TODO 等后端补全contentType
            <>
              {/* {chnType ? <span className="type">{chnType}</span> : ''} */}
              <a
                href={`${pbTag === 1 ? albumUrl + '&frmrs=image' : albumUrl}`}
                className="img"
                rseat={contentType === 3 ? 'preview' : 'play'}
                data-pb={pb + '&s_ptype=1-1-1'}
                style={{
                  backgroundImage: `url(${removeProtocol(
                    imgUrl(item.albumPic, '_260_360')
                  )})`
                }}
                gtag-category="search"
                onClick={e => {
                  sendGtag(e.currentTarget)
                }}
              >
                {showVipIcon ? <GridCardVipIcon /> : null}
                {showTvodIcon ? <GridCardTvodIcon ratio={tvodRatio} /> : null}
              </a>
              <div className="detail">
                <a
                  href={`${pbTag === 1 ? albumUrl + '&frmrs=title' : albumUrl}`}
                  className="name"
                  rseat={contentType === 3 ? 'preview' : 'play'}
                  data-pb={
                    pb + '&s_rq=' + pingback.realQuery + '&s_ptype=1-1-2'
                  }
                  dangerouslySetInnerHTML={{ __html: name }}
                  target="_blank"
                  rel="noreferrer"
                />
                {contentType === 3 ? null : ( // 预告片
                  <div className="group year">
                    <span className="label">
                      {trueLangPkg.year}
                      {colonStr}
                    </span>
                    <span className="val">{publishYear}</span>
                  </div>
                )}
                {director.length ? (
                  <div className="group ln1" ref={this.directorRef}>
                    <span className="label">
                      {trueLangPkg.play_director}
                      {colonStr}
                    </span>
                    {director.map((dtor, index) => {
                      return (
                        <a
                          href={rebuildCommonUrl(
                            `actor-info/${dtor.titleSEO || dtor.id}`
                          )}
                          target="_blank"
                          className="val"
                          key={dtor.id}
                          role="button"
                          data-pb={pb + '&s_rq=' + dtor.n}
                          rseat={'character_' + dtor.n}
                          rel="noreferrer"
                        >
                          {dtor.n}
                          {index === director.length - 1 ? '' : commaStr}
                        </a>
                      )
                    })}
                  </div>
                ) : (
                  <></>
                )}
                {cast.length ? (
                  <div className="group cast" ref={this.castRef}>
                    <span className="label">
                      {trueLangPkg.play_main_character}
                      {colonStr}
                    </span>
                    {cast.map((ct, index) => {
                      const html =
                        ct.n &&
                        ct.n + (index === cast.length - 1 ? '' : commaStr)
                      return (
                        <a
                          href={rebuildCommonUrl(
                            `actor-info/${ct.titleSEO || ct.id}`
                          )}
                          target="_blank"
                          className="val"
                          key={ct.id}
                          role="button"
                          data-pb={pb + '&s_rq=' + ct.n + '&s_ptype=1-1-4'}
                          rseat={'character_' + ct.n}
                          dangerouslySetInnerHTML={{ __html: html }}
                          rel="noreferrer"
                        />
                      )
                    })}
                  </div>
                ) : (
                  <></>
                )}
                {EpisodeBlock && (
                  <div className="width-more-768">{EpisodeBlock}</div>
                )}
                {isPlayControl && (
                  <a href="#!" className="play-btn control">
                    <ReserveButton
                      site="search_rst"
                      videoInfo={fromJS(item)}
                      langs={langs}
                      isMobile={isMobile}
                    />
                  </a>
                )}
                {(!EpisodeBlock || H5Style) && !isPlayControl && (
                  <a
                    href="#!"
                    className="play-btn"
                    rseat={contentType === 3 ? 'preview' : 'play'}
                    data-pb={pb + '&s_ptype=1-1-4'}
                    onClick={() => {
                      const newUrl = `${
                        pbTag === 1
                          ? url +
                            `&frmrs=${
                              showPreviecIcon ? 'preview_button' : 'button'
                            }`
                          : url
                      }`
                      const rseat = contentType === 3 ? 'preview' : 'play'
                      this.toApp(newUrl, item, block, rseat)
                    }}
                    rel="noreferrer"
                  >
                    {showPreviecIcon
                      ? trueLangPkg['search_rst_preview'] || 'preview'
                      : trueLangPkg['home_focusbanner_button']}
                  </a>
                )}
              </div>
            </>
          ) : (
            this.handleRowImgRender(item, { pb, pbTag })
          ) // 除正片之外的
          }
        </div>
        {EpisodeBlock && (
          <div className="width-less-767">
            {EpisodeBlock}
            <div className="line" />
          </div>
        )}
      </GridCardWrapper>
    )
  }
}

const mapStateToProps = state => {
  return {
    langs: state.getIn(['language', 'modeLangObj']),
    langPkg: state.getIn(['language', 'langPkg']),
    result: state.getIn(['search', 'result']),
    currentLang: state.getIn(['language', 'modeLangObj', 'lang']),
    currentMod: state.getIn(['language', 'modeLangObj', 'mod']),
    userInfo: state.getIn(['user', 'userInfo']),
    curVipList: state.getIn(['user', 'curVipList'])
  }
}

export default connect(mapStateToProps)(GridCard)
