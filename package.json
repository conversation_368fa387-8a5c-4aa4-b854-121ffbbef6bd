{"name": "global-web", "version": "0.0.1", "scripts": {"dev": "node ./server.js", "dev:inspect": "node --inspect ./server.js", "build": "next build", "test": "jest --collectCoverage", "ci:dev": "node ./scripts/dockerFile.js --dev && sh scripts/ci.sh dev", "ci:prod": "node ./scripts/dockerFile.js --prod && sh scripts/ci.sh prod", "ci:staging": "node ./scripts/dockerFile.js --prod && sh scripts/ci.sh staging", "ci:prodmaster": "node ./scripts/dockerFile.js --master && sh scripts/ci.sh master", "start": "cross-env NODE_ENV=production node server.js", "storybook": "start-storybook -p 7007", "build-storybook": "build-storybook", "analyze": "cross-env ANALYZE=true next build", "postinstall": "npx cimsg", "uploadImg": "sh scripts/uploadImg.sh", "testWeb": "testWeb"}, "lint-staged": {"*.{js,jsx}": ["pretty-quick --staged", "eslint --fix", "git add"]}, "dependencies": {"@fullpage/react-fullpage": "0.1.19", "@iqiyi-ibd/global-h5-callApp": "1.0.58-1", "@iqiyi-ibd/global-login-sdk": "0.3.62", "@iqiyi-ibd/global-upgrade-sdk": "^1.0.6", "@iqiyi-ibd/global-vip-sdk": "^1.2.3", "@iqiyi-ibd/google-sitemap": "^1.0.17", "@iqiyi-ibd/ibd-live-player": "1.0.24-1", "@iqiyi-ibd/ibd-player": "1.12.341-2", "@iqiyi-ibd/shared-utils": "1.0.29-w", "@iqiyi-uniqy/vip-components": "^1.1.4", "@iqiyi/qdsf": "^1.1.1", "@koa/router": "^11.0.1", "@next/bundle-analyzer": "^12.3.1", "@redux-saga/testing-utils": "^1.1.3", "@storybook/addon-actions": "^5.0.11", "@storybook/addon-links": "^5.0.11", "@storybook/addons": "^5.0.11", "@storybook/react": "^5.0.11", "@zeit/next-less": "^1.0.1", "babel-plugin-module-resolver": "^3.2.0", "body-parser": "^1.20.0", "cheerio": "^1.1.2", "classnames": "^2.2.6", "color-convert": "^2.0.1", "core-js": "^3.19.0", "cross-env": "^5.2.0", "enquire.js": "^2.1.6", "eslint-plugin-react-hooks": "^4.2.0", "fetch-jsonp": "^1.2.1", "helmet": "^3.23.3", "html5-history-api": "^4.2.10", "http-proxy-middleware": "^2.0.6", "immutable": "^3.8.1", "intersection-observer": "^0.12.0", "ip": "^1.1.9", "isomorphic-fetch": "^2.2.1", "jest-canvas-mock": "^2.2.0", "js-md5": "^0.7.3", "json2mq": "^0.2.0", "koa": "^2.13.4", "koa-bodyparser": "^4.3.0", "koa-favicon": "^2.0.1", "koa-router": "^7.4.0", "koa-server-http-proxy": "^0.1.0", "koa-static": "^5.0.0", "koa2-connect": "^1.0.2", "lodash.debounce": "^4.0.8", "lodash.throttle": "^4.1.1", "lru-cache": "^5.1.1", "next": "12.2.2", "next-compose-plugins": "^2.2.1", "next-redux-saga": "^4.0.2", "next-redux-wrapper": "^2.0.0", "next-transpile-modules": "^10.0.1", "nookies": "^2.0.6", "prop-types": "^15.7.2", "qrcode": "^1.5.0", "react": "^17.0.2", "react-animated-css": "^1.2.1", "react-dom": "^17.0.2", "react-load-script": "0.0.6", "react-redux": "^7.2.6", "redux": "^4.1.2", "redux-devtools-extension": "^2.13.8", "redux-immutable": "^4.0.0", "redux-logger": "^3.0.6", "redux-saga": "^1.0.5", "redux-thunk": "^2.4.0", "request": "^2.88.0", "resize-observer-polyfill": "^1.5.1", "s": "^1.0.0", "scriptjs": "^2.5.9", "styled-components": "^5.3.3", "swiper": "^6.8.4", "web-vitals": "^3.4.0", "workbox-cacheable-response": "^6.3.0", "workbox-core": "^6.3.0", "workbox-expiration": "^6.3.0", "workbox-routing": "^6.3.0", "workbox-strategies": "^6.3.0"}, "resolutions": {"styled-components": "5.3.1"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/helper-validator-option": "^7.12.17", "@babel/plugin-proposal-decorators": "^7.16.0", "@iqiyi-ibd/prepare-commit-msg": "^2.0.1", "@iqiyi-ibd/stress-testing-global-web": "1.0.8", "@typescript-eslint/eslint-plugin": "^2.1.0", "@typescript-eslint/parser": "^2.1.0", "@wojtekmaj/enzyme-adapter-react-17": "^0.6.7", "babel-eslint": "^10.0.2", "babel-loader": "^8.2.3", "babel-plugin-styled-components": "^1.13.3", "enzyme": "^3.10.0", "enzyme-to-json": "^3.4.3", "eslint": "^6.8.0", "eslint-config-airbnb": "^18.0.1", "eslint-config-prettier": "^6.0.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.25.2", "eslint-plugin-jsx-a11y": "^6.2.2", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-react": "^7.26.1", "eslint-plugin-react-hooks": "^4.2.0", "first-input-delay": "^0.1.3", "husky": "^3.0.0", "jest": "^24.9.0", "lint-staged": "^8.2.1", "nyc": "^15.1.0", "prettier": "^1.18.2", "pretty-quick": "^1.11.1", "react-test-renderer": "^16.11.0", "redux-mock-store": "^1.5.3", "workbox-webpack-plugin": "^6.3.0"}, "husky": {"hooks": {"pre-commit": "cross-env NODE_ENV=production lint-staged"}}}