import React, { useEffect } from 'react'
import { connect } from 'react-redux'
import LivePlayer from '@/components/common/LivePlayer'
import { getCookies } from '@/kit/cookie'
import { queryFromUrl, checkQipuIdStr } from '@/kit/url'

import {
  getLiveStatusCodeAction,
  fetchLiveInfoAction
} from '@/store/reducers/live/live'
import { fetchConfigAction } from '@/store/reducers/commonConfig/commonConfig'
import LiveWrapper from '@/style/liveStyle'
import Info from '@/components/pages/live/info'
import LiveMeta from '@/components/pages/live/meta'
import Schema from '@/components/pages/live/schema'
import TongJiPb from '@/components/common/TongJiPb'
import AdBanner from '@/components/common/ADBanner'
import { getCard } from '@/kit/commonConfig'
import Error from './_error'

const Live = props => {
  useEffect(() => {
    // 华梅姐需求，针对特定用户，可以访问某个特定直播页，999直播需求，直播结束后下掉
    const url = window.location.href
    const isIqiyi = queryFromUrl(url, 'iqiyi')
    const uids = [
      '34000485022',
      '2720539194',
      '2358066780269423',
      '30000000080',
      '30103935276',
      '34013985701',
      '30103885205',
      '30103885141',
      '30103885641',
      '30100696210',
      '30103921830',
      '34001477667',
      '34013995677',
      '34002522620',
      '30103935180',
      '30102135932',
      '30102114091',
      '30103849841',
      '30100439752',
      '30100760878',
      '30103651481',
      '30103651423',
      '30103602076',
      '30100087619',
      '34000000100',
      '227834309',
      '5433880360',
      '256849752363832',
      '30104010140',
      '1727908083',
      '1343131076',
      '30104011740',
      '30104010864',
      '30104012796',
      '564096311243545',
      '1304962445',
      '1239018280',
      '1825633767',
      '30103984785',
      '894334031479881',
      '2152122962',
      '198487412672996',
      '30100098989',
      '1227834309',
      '30103977707'
    ]
    if (url.match('mepgsp5t6r')) {
      if (isIqiyi !== 'true') {
        let userInfo = getCookies('I00002')
        if (userInfo) {
          userInfo = JSON.parse(userInfo)
          if (userInfo.data && userInfo.data.uid) {
            const uid = String(userInfo.data.uid)
            if (uids.indexOf(uid) === -1) {
              window.location.href = 'https://www.iq.com'
            }
          }
        } else {
          window.location.href = 'https://www.iq.com'
        }
      }
    }
  }, [])
  const { liveStatusCode, liveInfo, curUrl, loginTag, cards } = props
  const youthAd = getCard('pcw_tab_banner', cards.toJS())[0]
  let livePageWrapper
  if (liveStatusCode === 404) {
    livePageWrapper = <Error statusCode={liveStatusCode} />
  } else {
    livePageWrapper = (
      <>
        <LiveMeta liveInfo={liveInfo} curUrl={curUrl} />
        <LiveWrapper>
          <div className="intl-live-area-wrap">
            {/* <div className="intl-live-area"> */}
            <div className="intl-live-wrap">
              <div className="intl-video-area">
                <LivePlayer liveInfo={liveInfo} loginTag={loginTag} />
              </div>
            </div>
            {youthAd?.card_type === 'pcw_tab_banner' &&
              youthAd?.show_control?.promotion_type === 'customize' && (
                <div className="intl-live-youth-ad">
                  <AdBanner data={youthAd} rpage="live_play" />
                </div>
              )}
            {/* </div> */}
            <Info liveInfo={liveInfo} />
          </div>
        </LiveWrapper>
        <TongJiPb rpage="live_play" />
        <Schema liveInfo={liveInfo} />
      </>
    )
  }
  return <>{livePageWrapper}</>
}

Live.getInitialProps = async ({ ctx }) => {
  const { query, store, res, isServer } = ctx
  const { id } = query
  const curUrl = ctx.req.url.split('?')[0]
  const finalId = checkQipuIdStr(id)
  if (!finalId) {
    await store.dispatch(getLiveStatusCodeAction(404))
    res.setHeader('x-page-type', 'not-found')
  } else {
    const liveIdInfo = await store.dispatch(
      fetchLiveInfoAction({
        id: finalId,
        ctx,
        _catch: {}
      })
    )
    await store.dispatch(
      fetchConfigAction({
        pageSt: 'live_web',
        ctx,
        tv_id: liveIdInfo?.qipuId,
        _catch: {}
      })
    )
  }
  return {
    isServer,
    curUrl
  }
}

const mapStateToProps = state => {
  return {
    liveInfo: state.getIn(['live', 'liveInfo']),
    liveStatusCode: state.getIn(['live', 'liveStatusCode']),
    loginTag: state.getIn(['user', 'userInfo', 'loginTag']),
    cards: state.getIn(['commonConfig', 'live_web', 'cards'])
  }
}

export default connect(mapStateToProps)(Live)
