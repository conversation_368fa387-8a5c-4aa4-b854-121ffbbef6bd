import React, { useState, useEffect, useContext } from 'react'
import { connect } from 'react-redux'
import { createVipOrder } from '@iqiyi-ibd/global-vip-sdk'
import { createUpgrade } from '@iqiyi-ibd/global-upgrade-sdk'
import { PopUpWindowCloseIcon } from '@/components/svgs'
import { appendQuery } from '@/kit/url'
import { forceIntlUrl } from '@/kit/common'
import { getCard } from '@/kit/commonConfig'
import { isLogin } from '@/utils/userInfo'
import { rpageObj } from '@/utils/common'
import { sendBlockPb } from '@/utils/pingBack'
import context from '@/components/context'
import { getDevice, getOs } from '@/kit/device'
import PopUpWindowWrapper from './style'

const PopUpWindow = props => {
  const { commonConfigData } = props
  const trueCommonConfigData = commonConfigData.toJS()

  const { pathname } = useContext(context)
  const rpage = rpageObj[pathname] ? rpageObj[pathname] : '404'

  const [isModalVisible, setIsModalVisible] = useState(false)
  const [popCheckDone, setPopCheckDone] = useState(false)

  const trueCurWebData =
    trueCommonConfigData[trueCommonConfigData['curPageKey']] || {}
  const { cards = [] } = trueCurWebData
  const curCard = getCard('pcw_open_popup', cards)[0]

  // getCard('pcw_open_popup', cards)[0]
  const blockKvpair = curCard?.blocks[0]?.kv_pair || {}
  const contentType = curCard?.blocks[0]?.content_type || ''
  const cardsStatistics = curCard?.statistics || {}
  const customizeDisplayFrequency = blockKvpair.customize_display_frequency
  const isCustomize =
    contentType === 'customize' ||
    contentType === 'auto_single_image' ||
    contentType === 'auto_multiple_image'

  let imageList = blockKvpair.image_list
  if (
    contentType === 'auto_single_image' ||
    contentType === 'auto_multiple_image'
  ) {
    if (typeof imageList === 'string') {
      imageList = JSON.parse(imageList)
    }
  }

  const actions = curCard?.blocks[0]?.actions || {}
  const clickEvent = actions?.click_event || {}
  const data = clickEvent?.data || {}
  const statistics = clickEvent?.statistics || {}
  let addEvt = false
  const isGptShow = () => {
    // if (!isCustomize) {
    //   return true
    // }
    let closetm = window.sessionStorage.getItem('openscreen-close')

    if (!closetm) {
      // 不存在说明一次也没出现过广告
      return true
    }

    closetm = closetm ? parseInt(closetm, 10) : 0
    const current = Math.round(+new Date() / 1000)
    const isGptShowed =
      closetm > 0 && current - closetm > 60 * 60 * customizeDisplayFrequency // 一小时内不再展示 单位秒
    return isGptShowed
  }
  useEffect(() => {
    if (!addEvt) {
      addEvt = true
      document.addEventListener('toShowPopUp', () => {
        if (!popCheckDone) {
          setPopCheckDone(true)
        }
      })
    }
    if (curCard && popCheckDone) {
      if (!isCustomize) {
        sendBlockPb(data.block, {
          rpage,
          tjPb: {
            fc: statistics.fc
          }
        })
      } else if (isCustomize && isGptShow()) {
        sendBlockPb(cardsStatistics.block, {
          rpage
        })
      }
    }
  }, [contentType, popCheckDone])
  // 如果curCard 不存在 则没必要继续渲染
  if (!curCard || !popCheckDone || !isGptShow()) {
    return ''
  }

  const isButtonText =
    !!blockKvpair.customize_button_text || !!blockKvpair.vip_button

  const handleCancel = () => {
    // if (isCustomize) {
    // 手动关闭 存储消失时间
    const tm = Math.round(+new Date() / 1000)
    window.sessionStorage.setItem('openscreen-close', tm)
    // }

    document.body.style.overflow = 'auto'
    document.body.style.width = '100%'
    document.querySelector('.header-container').style.width = '100%'
    setIsModalVisible(false)
  }

  const handleShow = () => {
    const isPc = getDevice() === 'pc'
    if (isPc && window && window.innerWidth >= 1024) {
      document.body.style.overflow = 'hidden'
      if (getOs() !== 'Mac') {
        document.body.style.width = 'calc(100% - 17px)'
        document.querySelector('.header-container').style.width =
          'calc(100% - 17px)'
      }
    }
    setIsModalVisible(true)
  }

  const handleSureBtn = loc => {
    // 如果有button 那么点击图片无效
    if (isButtonText && loc === 'pic') {
      return
    }

    setIsModalVisible(false)
    if (isCustomize) {
      window.open(blockKvpair.customize_landing_url)
      return
    }

    let url = data.url
      ? appendQuery({ fc: statistics.fc, fv: statistics.fv }, data.url)
      : ''
    url = forceIntlUrl(url)
    if (data.type === '4' && url) {
      window.open(url)
    } else if (Number(data.type) === 17 && isLogin()) {
      createUpgrade({
        vipTags: data.vipTag,
        fc: statistics.fc || ''
      })
    } else {
      createVipOrder({
        cashierType: data.cashierType,
        vipType: data.vipType,
        payAutoRenew: data.autoRenew,
        amount: data.vipProduct,
        fc: statistics.fc,
        fv: statistics.fv
      })
    }
  }
  return (
    <PopUpWindowWrapper
      style={{
        display: `${isModalVisible ? 'block' : 'none'}`,
        borderRadius: 'border-radius: 8px 8px 0px 0px;'
      }}
    >
      {contentType === 'auto_single_image' ||
      contentType === 'auto_multiple_image' ? (
        <div
          className="pop-up-container1"
          style={{
            background: '#2D2F34',
            borderRadius: '8px 8px 8px 8px'
          }}
        >
          {// 有按钮 点击图片不应该发pingback
          isButtonText && imageList.length > 0 ? (
            <div
              style={{
                width: '400px',
                borderRadius: '8px 8px 0px 0px'
              }}
              onClick={() => {
                handleSureBtn('pic')
              }}
              tabIndex="0"
              role="button"
              className="pop-imglist"
            >
              {imageList.map((item, index) => {
                const keyval = index + 'imglist'
                let classIndex = index
                if (index === 0 && imageList.length === 1) {
                  classIndex = 'all'
                }
                if (index === 1 && imageList.length === 2) {
                  classIndex = 'end'
                }
                if (index === 2 && imageList.length === 3) {
                  classIndex = 'end'
                }
                return (
                  <img
                    key={keyval}
                    src={`${item}`}
                    className={'pop-img' + classIndex}
                    alt="pic"
                    onLoad={() => {
                      if (index === 0) {
                        handleShow()
                      }
                    }}
                  />
                )
              })}
            </div>
          ) : null}
          <div className="pop-title">
            {blockKvpair.title ? (
              <p className="titlemain">{blockKvpair.title}</p>
            ) : null}
            {blockKvpair.subtitle ? (
              <p className="titlesub">{blockKvpair.subtitle}</p>
            ) : null}
          </div>
          <div
            style={{
              width: '100%',
              bottom: '-2px',
              position: 'sticky',
              paddingBottom: '40px',
              background: ' rgb(45, 47, 52)',
              borderRadius: ' 8px'
            }}
          >
            {isButtonText && (
              <div
                className="sur-btn"
                style={{
                  background: blockKvpair.customize_button_color,
                  color: blockKvpair.customize_button_text_color,
                  width: '320px',
                  marginLeft: 'auto',
                  marginRight: 'auto',
                  whiteSpace: 'nowrap',
                  paddingLeft: '10px',
                  paddingRight: '10px',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis'
                }}
                onClick={() => {
                  handleSureBtn('btn')
                }}
                tabIndex="0"
                role="button"
                rseat={`${isCustomize ? data.pb_button_rseat : data.rseat}`}
                data-pb={`block=${
                  isCustomize ? cardsStatistics.block : data.block
                }&rpage=${rpage}&fc=${statistics.fc || ''}`}
              >
                {isCustomize
                  ? blockKvpair.customize_button_text
                  : blockKvpair.vip_button}
              </div>
            )}
            <div
              className="sur-btn"
              style={{
                border: '1px solid ' + blockKvpair.customize_button_color,
                color: blockKvpair.customize_button_color,
                width: '320px',
                marginLeft: 'auto',
                marginRight: 'auto',
                paddingLeft: '10px',
                paddingRight: '10px',
                background: 'none',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis'
              }}
              onClick={() => {
                handleCancel()
              }}
              role="button"
              tabIndex="0"
              rseat="close"
              data-pb={`block=${
                cardsStatistics.block
              }&rpage=${rpage}&fc=${statistics.fc || ''}`}
            >
              {blockKvpair.close_button_text}
            </div>
          </div>
        </div>
      ) : (
        <div className="pop-up-container">
          <div className="pic-box">
            <div
              onClick={() => {
                handleSureBtn('pic')
              }}
              tabIndex="0"
              role="button"
            >
              {// 有按钮 点击图片不应该发pingback
              isButtonText ? (
                <img
                  src={`${
                    isCustomize
                      ? blockKvpair.customize_background_pic
                      : blockKvpair.vip_image
                  }`}
                  alt="pic"
                  onLoad={() => {
                    handleShow()
                  }}
                />
              ) : (
                <img
                  src={`${
                    isCustomize
                      ? blockKvpair.customize_background_pic || ''
                      : blockKvpair.vip_image || ''
                  }`}
                  rseat={`${
                    isCustomize ? data.pb_backgroundPic_rseat : data.rseat
                  }`}
                  data-pb={`block=${
                    isCustomize ? cardsStatistics.block : data.block
                  }&rpage=${rpage}&fc=${statistics.fc || ''}`}
                  alt="pic"
                  onLoad={() => {
                    handleShow()
                  }}
                />
              )}
            </div>
          </div>

          {isButtonText && (
            <div
              className="sur-btn"
              style={{
                background: isCustomize
                  ? blockKvpair.customize_button_color
                  : blockKvpair.vip_button_background_color,
                color: isCustomize
                  ? blockKvpair.customize_button_text_color
                  : blockKvpair.vip_button_color
              }}
              onClick={() => {
                handleSureBtn('btn')
              }}
              tabIndex="0"
              role="button"
              rseat={`${isCustomize ? data.pb_button_rseat : data.rseat}`}
              data-pb={`block=${
                isCustomize ? cardsStatistics.block : data.block
              }&rpage=${rpage}&fc=${statistics.fc || ''}`}
            >
              {isCustomize
                ? blockKvpair.customize_button_text
                : blockKvpair.vip_button}
            </div>
          )}
          <div
            className="close-btn"
            onClick={() => {
              handleCancel()
            }}
            role="button"
            tabIndex="0"
            rseat="close"
            data-pb={`block=${
              isCustomize ? cardsStatistics.block : data.block
            }&rpage=${rpage}&fc=${statistics.fc || ''}`}
          >
            <PopUpWindowCloseIcon />
          </div>
        </div>
      )}
    </PopUpWindowWrapper>
  )
}

const mapStateToProps = state => ({
  commonConfigData: state.getIn(['commonConfig'])
})
export default connect(mapStateToProps)(PopUpWindow)
