import React from 'react'
import { connect } from 'react-redux'
import Meta from '@/components/common/Meta'
import TongJiPb from '@/components/common/TongJiPb'
import TvTutorialDetailsWrapper from '@/style/tvInstallDetailsStyle'
import $http from '@/kit/fetch'
import { iqSwitchPlatformId } from '@/kit/common'
import { TV_INSTALL_GUIDE } from '@/constants/interfaces'

class TvTutorialDetails extends React.Component {
  constructor(props) {
    super(props)
    this.state = {}
  }

  componentDidMount() {}

  static async getInitialProps({ ctx }) {
    const { query, store } = ctx
    const tvId = query.tvId || ''

    let tvList = []
    let curTvDdata = {}
    const state = store.getState()
    const langCode = state.getIn(['language', 'modeLangObj', 'lang'])
    const modCode = state.getIn(['language', 'modeLangObj', 'mod'])
    try {
      const res = await $http(TV_INSTALL_GUIDE, {
        params: {
          app_k: 'appk_pcw',
          app_v: '3.5.1',
          platform_id: iqSwitchPlatformId(),
          lang: langCode || global.langCode,
          app_lm: modCode || global.modeCode
        }
      })
      if (res.code === 0) {
        tvList = res.data
        // console.log(tvList)
        curTvDdata = tvList.filter(item => item.id === tvId)[0]
      }
    } catch (err) {
      console.log(err)
    }

    return { curTvDdata }
  }

  render() {
    const { langPkg, curTvDdata } = this.props
    const trueLangPkg = langPkg.toJS()
    const textAndPictureList = JSON.parse(
      curTvDdata.oppkit_data.textAndPictureList || ''
    )

    return (
      <>
        <Meta
          desc={trueLangPkg.download_page_html_description}
          title={trueLangPkg.direct_headers}
          isShowAnimateCSS
        />
        <TvTutorialDetailsWrapper>
          <div className="tv-icon">
            <img src={curTvDdata?.oppkit_data?.legend} alt="legend" />
          </div>
          <div className="title">{curTvDdata?.oppkit_data?.title}</div>
          <div className="detail">
            {textAndPictureList.map((item, index) => {
              const keyVal = index + 'abc'
              return (
                <div key={keyVal} className="box">
                  <div
                    className="text"
                    style={{ display: `${item.text ? 'block' : 'none'}` }}
                  >
                    {item.text}
                  </div>
                  <div
                    className="pic"
                    style={{ display: `${item.picture ? 'block' : 'none'}` }}
                  >
                    <img src={item.picture} alt="legend" />
                  </div>
                </div>
              )
            })}
          </div>
        </TvTutorialDetailsWrapper>
        <TongJiPb
          rpage={`TV_tutorial_${curTvDdata?.oppkit_data?.name || ''}`}
          blockInfo="header,footer"
        />
      </>
    )
  }
}

const mapStateToProps = state => ({
  langPkg: state.getIn(['language', 'langPkg'])
})

export default connect(mapStateToProps)(TvTutorialDetails)
