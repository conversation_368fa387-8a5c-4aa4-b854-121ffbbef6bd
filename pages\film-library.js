import React from 'react'
// import dynamic from 'next/dynamic'
import { connect } from 'react-redux'
import FileLibraryWrapper from '@/style/filmLibraryStyle'
import FilmLibrary from '@/components/pages/filmLibrary'
import { getMobileType } from '@/kit/device'
import TongJiPb from '@/components/common/TongJiPb'
import Meta from '@/components/common/Meta'
import { queryFromUrl } from '@/kit/url'
// import { rebuildCommonUrl } from '@/kit/common'

import Error from './_error'

// const BackTop = dynamic(import('@/components/common/BackTop'), {
//   ssr: false
// })
class FilmLibraryPage extends React.PureComponent {
  state = {
    isError: false
    // chnid: 'pending'
  }

  static isBeforeFetchData = true

  static getInitialProps({ ctx, data }) {
    const { mod = 'intl', chnid, value } = ctx.query

    return { defaultChnid: data[0] && data[0].id, mod, chnid, value }
  }

  static getDerivedStateFromError() {
    // 子组件报错
    return { isError: true }
  }

  componentDidMount() {
    // const { mod } = this.props
    // if (mod === 'ntw') {
    //   window.location.href = rebuildCommonUrl('')
    // }
    // const { defaultChnid } = this.props
    // const chnid = queryFromUrl(window.location.href, 'chnid') || defaultChnid
    // this.setState({ chnid: chnid || 1 })
  }

  render() {
    const { langPkg, defaultChnid, chnid, value } = this.props
    const { isError } = this.state
    const trueLangPkg = langPkg.toJS()
    const deType = getMobileType()
    const deviceClassName =
      deType === 'pc' ? '' : deType === 'ipad' ? 'ipad' : 'mobile'
    const pbChnid = chnid || defaultChnid

    return (
      <>
        <Meta
          desc={trueLangPkg.home_html_description}
          title={trueLangPkg.home_html_title}
          path="film-library"
        />
        {isError ? (
          <Error statusCode={404} />
        ) : (
          <FileLibraryWrapper className={deviceClassName}>
            {/*            {chnid !== 'pending' ? (
             */}{' '}
            <FilmLibrary
              chnid={chnid}
              deviceClassName={deviceClassName}
              value={value}
            />
            {/* ) : null} */}
            {/* <BackTop rpage={`explore_library_${pbChnid}`} /> */}
            <TongJiPb
              rpage={`explore_library_${pbChnid}`}
              pbShowParams={{ bstp: 18 }}
            />
          </FileLibraryWrapper>
        )}
      </>
    )
  }
}
const mapStateToProps = state => ({
  langPkg: state.getIn(['language', 'langPkg'])
})
export default connect(mapStateToProps)(FilmLibraryPage)
