import React from 'react'
import { connect } from 'react-redux'
import Meta from '@/components/common/Meta'
import TongJiPb from '@/components/common/TongJiPb'
import PrivilegeCom from '@/components/pages/vip/Privilege'
import { gtagReportConversion } from '@/utils/gtag'

class Privilege extends React.Component {
  constructor(props) {
    super(props)
    this.state = {}
  }

  componentDidMount() {
    window.addEventListener('resize', () => {})
    gtagReportConversion('AW-339868652/CEgQCKChnvECEOz3h6IB')
  }

  static async getInitialProps({ ctx }) {
    const { query } = ctx
    const mod = query.mod || ''
    return { mod }
  }

  render() {
    const { langPkg, langs } = this.props
    const trueLang = langs.toJS()
    const trueLangPkg = langPkg.toJS()
    return (
      <>
        <Meta
          desc={trueLangPkg.privilege_html_description}
          title={trueLangPkg.privilege_html_title}
        />
        <PrivilegeCom mod={trueLang.mod} />
        <TongJiPb rpage="privilege" />
      </>
    )
  }
}

const mapStateToProps = state => ({
  langs: state.getIn(['language', 'modeLangObj']),
  langPkg: state.getIn(['language', 'langPkg']),
  userInfo: state.getIn(['user', 'userInfo'])
})

export default connect(mapStateToProps)(Privilege)
