const glob = require('glob')
const path = require('path')
const SiteMap = require('@iqiyi-ibd/google-sitemap')

const siteMap = new SiteMap({})
let controllers = Object.create({})

glob
  .sync(path.resolve(__dirname, '**/*.js'))
  .filter(value => value.indexOf('index.js') === -1)
  .forEach(controller => {
    const basename = path.basename(controller, '.js')
    controllers[basename] = require(controller)
  })

controllers = Object.assign(controllers, {
  siteMap
})

module.exports = {
  controllers
}
