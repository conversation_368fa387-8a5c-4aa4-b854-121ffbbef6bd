import React from 'react'
import { connect } from 'react-redux'
import { locUrl, rebuildCommonUrl } from '@/kit/common'
import Meta from '@/components/common/Meta'
import UserWrap from '@/components/common/Header/userWrap'
import { isLogin } from '@/utils/userInfo'
import { getCookies } from '@/kit/cookie'
import { queryFromUrl } from '@/kit/url'
import {
  getVipData,
  handleVipInfo,
  getOrderQuery,
  getAbtest
} from '@/components/pages/vip/api'
import { utmPbParams } from '@/utils/pingBack'
import VipPageContainer from '@/components/pages/vip/VipPageContainer'
import SelectPaytype from '@/components/pages/vip/SelectPaytype'
import TemplateErrorPop from '@/components/pages/vip/Error'
import DeviceCtx from '@/components/context'

class Paytype extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      selectedPkg: null,
      vipTextNodes: {},
      isSelectVipServiceAgreement: false,
      error: '',
      _abtest: '',
      initParams: null
    }
  }

  async componentDidMount() {
    const _url = locUrl()
    const initParams = this.getInitParams()
    this.setState({
      initParams
    })
    this.sendPb(initParams)

    const selectedId = queryFromUrl(_url, 'selectedId')

    const vipLangPkg = this.props.vipLangPkg.toJS()

    try {
      const vipInfoRes = await getVipData(
        initParams,
        this.props.modeLangObj.toJS()
      )
      const vipInfo = handleVipInfo(vipInfoRes, vipLangPkg)
      const selectedPkg = vipInfo.vipPkgList.find(
        item => item.id === Number(selectedId)
      )
      if (selectedPkg) {
        this.setState({
          selectedPkg,
          vipTextNodes: vipInfo.vipTextNodes,
          isSelectVipServiceAgreement: vipInfo.isSelectVipServiceAgreement,
          _abtest: vipInfo.abtest
        })
      } else {
        this.setState({
          error: 'error'
        })
      }
    } catch (err) {
      // console.log(err)
      // this.setState({
      //   error: err ? err.msg || err.message : 'error'
      // })
    }
  }

  getInitParams() {
    const _url = locUrl()
    const productName = queryFromUrl(_url, 'productName')
    const typeParam = queryFromUrl(_url, 'vipType')
    const { vipPid, bossCode, ptid } = this.context
    const vipType =
      typeParam || (productName ? vipPid[productName].viptype : '')
    const initParams = {
      vipType,
      aid: queryFromUrl(_url, 'aid') || '',
      fc: queryFromUrl(_url, 'fc') || '',
      fv: getCookies('playFV') || queryFromUrl(_url, 'fv') || '',
      amount: queryFromUrl(_url, 'amount') || '',
      payAutoRenew: queryFromUrl(_url, 'payAutoRenew') || '',
      albumId: queryFromUrl(_url, 'albumId') || '',
      fr_version: queryFromUrl(_url, 'fr') || '',
      abtest: queryFromUrl(_url, 'abtest') || '',
      pid: queryFromUrl(_url, 'pid') || '',
      v_prod: queryFromUrl(_url, 'v_prod') || '',
      vipPid,
      bossCode,
      ptid
    }

    return initParams
  }

  sendPb(initParams) {
    const varScript = document.createElement('script')
    const rpage = 'pay_type'
    const { modeLangObj } = this.props
    const lang = modeLangObj.get('lang')
    const mod = modeLangObj.get('mod')
    const testRes = getAbtest()
    const abtest = `page_cashier${testRes ? `,${testRes}` : ''}${
      initParams.abtest ? ',' + initParams.abtest : ''
    }`
    const { vfm, encodeVfm } = utmPbParams()

    varScript.innerHTML = `
      window.intlPageInfo = window.intlPageInfo || {};
      intlPageInfo.i18n = 'global';
      intlPageInfo.pbInfos = {
        "rpage": "${rpage}",
        "lang": "${lang}",
        "mod": "${mod}"
      }
      intlPageInfo.pbInfos.pbShowParams = intlPageInfo.pbInfos.pbShowParams || {}
      intlPageInfo.pbInfos.pbClickParams = intlPageInfo.pbInfos.pbClickParams || {}
      intlPageInfo.pbInfos.pbShowParams.bstp = 56
      intlPageInfo.pbInfos.pbShowParams.abtest = '${abtest}'
      intlPageInfo.pbInfos.pbShowParams.fc = '${initParams.fc || ''}'
      intlPageInfo.pbInfos.pbShowParams.fv = '${initParams.fv || ''}'
      intlPageInfo.pbInfos.pbShowParams.v_pid = '${initParams.pid || ''}'
      intlPageInfo.pbInfos.pbShowParams.v_prod = '${initParams.v_prod || ''}'
      intlPageInfo.pbInfos.pbShowParams.cashier_type = 'norm'
      if("${vfm}") {
        intlPageInfo.pbInfos.pbShowParams.vfm = "${encodeVfm}"
        intlPageInfo.pbInfos.pbClickParams.vfm = "${vfm}"
      }
    `
    document.body.append(varScript)
    // document.body.append(script)
  }

  handleLogin = () => {}

  bindLoginRef = ref => {
    this.loginRef = ref
  }

  checkLogin = () => {
    if (!isLogin()) {
      this.loginRef.handleUserLogin()
      return false
    }
    return true
  }

  render() {
    const { cashierLangPkg } = this.props
    const vipLangPkg = this.props.vipLangPkg.toJS()
    const {
      selectedPkg,
      vipTextNodes,
      isSelectVipServiceAgreement,
      error,
      _abtest,
      initParams
    } = this.state
    const timeoutErr = error && error.match('timeout')

    const abtestQuery = initParams ? initParams.abtest : ''

    const testRes = getAbtest()
    const abtest = `page_cashier${testRes ? `,${testRes}` : ''}${
      abtestQuery ? ',' + abtestQuery : ''
    }`

    return (
      <>
        <Meta
          desc={cashierLangPkg.getIn(['cashier_html_description'])}
          title={cashierLangPkg.getIn(['upgrade_VIP'])}
        />
        {error && (
          <VipPageContainer autoWidth>
            <TemplateErrorPop
              image={
                timeoutErr
                  ? '//www.iqiyipic.com/common/fix/global/api_network_error.png'
                  : '//www.iqiyipic.com/common/fix/global/api_oops.png'
              }
              title={
                timeoutErr
                  ? vipLangPkg.pcashier_error_network
                  : vipLangPkg.pcashier_error_errorOccur
              }
              btnText={
                timeoutErr
                  ? vipLangPkg.pcashier_error_retry
                  : vipLangPkg.pcashier_result_backPlan
              }
              btnClick={() => {
                if (timeoutErr) {
                  window.location.reload()
                } else {
                  const url = locUrl()
                  window.location.href = rebuildCommonUrl(
                    'vip/order' + getOrderQuery(url)
                  )
                }
              }}
            />
          </VipPageContainer>
        )}
        {!error && !selectedPkg && <VipPageContainer />}
        {!error && selectedPkg && (
          <VipPageContainer>
            <SelectPaytype
              vipPkg={selectedPkg}
              vipTextNodes={vipTextNodes}
              cashierLangPkg={cashierLangPkg}
              params={{ ...initParams, _abtest }}
              isSelectVipServiceAgreement={isSelectVipServiceAgreement}
              checkLogin={this.checkLogin}
              setErrorPop={err => {
                this.setState({
                  error: err
                })
              }}
              abtest={abtest}
            />
          </VipPageContainer>
        )}
        <div className="user-wrapper">
          <UserWrap
            isVipButton="true"
            onRef={this.bindLoginRef}
            onLogin={this.handleLogin}
          />
        </div>
      </>
    )
  }
}

Paytype.contextType = DeviceCtx

const mapStateToProps = state => ({
  vipLangPkg: state.getIn(['vip', 'vipLangPkg']),
  cashierLangPkg: state.getIn(['vip', 'cashierLangPkg']),
  modeLangObj: state.getIn(['language', 'modeLangObj']),
  userInfo: state.getIn(['user', 'userInfo'])
})

export default connect(mapStateToProps)(Paytype)
