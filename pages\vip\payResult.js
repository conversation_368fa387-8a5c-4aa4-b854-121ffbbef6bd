import React from 'react'
import { connect } from 'react-redux'
import { locUrl } from '@/kit/common'
import Meta from '@/components/common/Meta'

import VipPageContainer from '@/components/pages/vip/VipPageContainer'
import PayResult from '@/components/pages/vip/PayResult'
import TongJiPb from '@/components/common/TongJiPb'
import { queryFromUrl } from '@/kit/url'
import { getCookies } from '@/kit/cookie'
import { utmPbParams } from '@/utils/pingBack'
import { getAbtest } from '@/components/pages/vip/api'

class Page extends React.Component {
  constructor(props) {
    super(props)
    this.state = {}
  }

  componentDidMount() {
    // const abParams = this.getAbParams()
    // this.sendPb(abParams)
  }

  // static async getInitialProps({ ctx }) {
  //   // const { res } = ctx
  //   // res.writeHead(301, {
  //   //   Location: `https://www.iq.com`
  //   // })
  //   // res.end()
  // }

  getAbParams() {
    const _url = locUrl()
    return {
      fc: queryFromUrl(_url, 'fc') || '',
      fv: getCookies('playFV') || queryFromUrl(_url, 'fv') || '',
      abtest: queryFromUrl(_url, 'abtest') || '',
      pid: queryFromUrl(_url, 'pid') || '',
      v_prod: queryFromUrl(_url, 'v_prod') || '',
      pay_type: queryFromUrl(_url, 'pay_type') || '',
      code: queryFromUrl(_url, 'code') || '',
      msg: queryFromUrl(_url, 'msg') || ''
    }
  }

  sendPb(initParams) {
    const varScript = document.createElement('script')
    const rpage = 'success_pay'
    const { modeLangObj } = this.props
    const lang = modeLangObj.get('lang')
    const mod = modeLangObj.get('mod')
    const testRes = getAbtest()
    const abtest = `page_cashier${testRes ? `,${testRes}` : ''}${
      initParams.abtest ? ',' + initParams.abtest : ''
    }`
    const { vfm, encodeVfm } = utmPbParams()

    varScript.innerHTML = `
      window.intlPageInfo = window.intlPageInfo || {};
      intlPageInfo.i18n = 'global';
      intlPageInfo.pbInfos = {
        "rpage": "${rpage}",
        "lang": "${lang}",
        "mod": "${mod}"
      }
      intlPageInfo.pbInfos.pbShowParams = intlPageInfo.pbInfos.pbShowParams || {}
      intlPageInfo.pbInfos.pbClickParams = intlPageInfo.pbInfos.pbClickParams || {}
      intlPageInfo.pbInfos.pbShowParams.bstp = 56
      intlPageInfo.pbInfos.pbShowParams.abtest = '${abtest}'
      intlPageInfo.pbInfos.pbShowParams.fc = '${initParams.fc || ''}'
      intlPageInfo.pbInfos.pbShowParams.fv = '${initParams.fv || ''}'
      intlPageInfo.pbInfos.pbShowParams.v_pid = '${initParams.pid || ''}'
      intlPageInfo.pbInfos.pbShowParams.v_prod = '${initParams.v_prod || ''}'
      intlPageInfo.pbInfos.pbShowParams.pay_type = '${initParams.pay_type ||
        ''}'
      intlPageInfo.pbInfos.pbShowParams.cashier_type = 'norm'
      if("${vfm}") {
        intlPageInfo.pbInfos.pbShowParams.vfm = "${encodeVfm}"
        intlPageInfo.pbInfos.pbClickParams.vfm = "${vfm}"
      }
    `
    document.body.append(varScript)
  }

  onRef = ref => {
    this.userRef = ref
  }

  showLogin() {
    // eslint-disable-next-line no-unused-expressions
    this.userRef && this.userRef.handleUserLogin()
  }

  render() {
    const { bossCode } = this.props
    const _url = locUrl()
    const vipOrder = queryFromUrl(_url, 'vipOrder') || ''
    const orderCode = queryFromUrl(_url, 'orderCode') || ''
    const cashierType = queryFromUrl(_url, 'cashierType') || ''
    const code = queryFromUrl(_url, 'code') || ''
    const msg = decodeURIComponent(queryFromUrl(_url, 'msg')) || ''
    const blockInfo = 'header,footer'

    const cashierLangPkg = this.props.cashierLangPkg.toJS()

    return (
      <>
        <Meta
          desc={cashierLangPkg.cashier_html_description}
          title={cashierLangPkg.tvod_paymentProcess}
        />
        <VipPageContainer autoWidth>
          <PayResult
            vipOrder={vipOrder}
            orderCode={orderCode}
            bossCode={bossCode}
            cashierType={cashierType}
            code={code}
            msg={msg}
          />
        </VipPageContainer>
        <TongJiPb rpage="pay_result" blockInfo={blockInfo} />
      </>
    )
  }
}

const mapStateToProps = state => ({
  modeLangObj: state.getIn(['language', 'modeLangObj']),
  cashierLangPkg: state.getIn(['vip', 'cashierLangPkg']),
  userInfo: state.getIn(['user', 'userInfo']),
  vipList: state.getIn(['user', 'vipList'])
})

export default connect(mapStateToProps)(Page)
