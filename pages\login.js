import React from 'react'
import { connect } from 'react-redux'
import { isApp } from '@/utils/appDevice'
import jsBridge from '@/utils/jsBridge'
import DeviceCtx from '@/components/context'
import { getDevice, getMobileType } from '@/kit/device'
import { queryFromUrl } from '@/kit/url'
import Meta from '@/components/common/Meta'
import TongJiPb from '@/components/common/TongJiPb'
import { hasAgentType, hostName, rebuildCommonUrl } from '@/kit/common'
import otpCodeLogin, { getOtpVipInfo } from '@/utils/otpCodeLogin'
import Toast from '@/components/common/Toast'
import { serialize } from '@/kit/fetch'
// import { activatePartnerCode } from '@/utils/apis'
import { sendBlockPb } from '@/utils/pingBack'

class Login extends React.Component {
  static async getInitialProps({ ctx }) {
    const { isServer, query } = ctx
    const isOtpLogin = !!query.otp
    const mytv = !!query.mytv

    return { isServer, isOtpLogin, mytv }
  }

  static contextType = DeviceCtx

  constructor(props, context) {
    super(props)
    const { ptid } = context
    this.state = {
      isMobile: getDevice() === 'mobile',
      deviceType: getMobileType()
    }
    this.loginSdkParam = {
      oprUserInfo: true,
      // lang: 'en_us',
      agenttype: hasAgentType(),
      ptid,
      pingbackParams: '',
      mode: 'landing'
    }
  }

  async componentDidMount() {
    const isMobile = getDevice() === 'mobile'
    const { langs } = this.props
    const trueLang = langs.toJS()
    const curHref = window.location.href

    this.setState({
      isMobile
    })
    this.loginSdkParam.lang = trueLang.lang

    // 如果url有传语言参数或ptid，覆盖原有页面设置
    const applang = queryFromUrl(window.location.href, 'lang')
    if (applang) {
      this.loginSdkParam.lang = applang
    }
    const urlPtid = queryFromUrl(window.location.href, 'ptid')
    if (urlPtid) {
      this.loginSdkParam.ptid = urlPtid
    }
    // document.domain = 'iq.com'
    const globalLoginSdk = require('@iqiyi-ibd/global-login-sdk')
    const sdk = new globalLoginSdk.LoginSdk(this.loginSdkParam)
    this.sdk = sdk

    sdk.init()
    sdk.openLoginRegWindow({
      pingback: {}
    })
    // this.$set(this, 'sdk', sdk)
    sdk.on('emailManage', data => {
      this.emailType = data.data.type
      this.email = data.data.email
    })
    sdk.on('passwordManage', data => {
      this.pwdType = data.data.type
    })

    const sessionKey = queryFromUrl(curHref, 'sessionKey')
    const partnerId = queryFromUrl(curHref, 'partnerId')
    sessionKey &&
      partnerId &&
      window.sessionStorage.setItem(
        'CMHK-activate',
        sessionKey + '|' + partnerId
      )

    // 注册jsBridge
    if (isApp) {
      jsBridge(window)
    }

    window.addEventListener('resize', () => {
      const { isMobile, deviceType } = this.state
      const newIsMobile = getDevice() === 'mobile'
      const newDeviceType = getMobileType()
      if (isMobile !== newIsMobile) {
        this.setState({
          isMobile: newIsMobile
        })
      }
      if (deviceType !== newDeviceType) {
        this.setState({
          deviceType: newDeviceType
        })
      }
    })
    // if (queryFromUrl(curHref, 'otp') && queryFromUrl(curHref, 'tv-vip')) {
    //   if (sdk.isLogin()) {
    //     await sdk.logout()
    //   }
    // }

    // otp登录 是从login-tv页面调过来的
    const code = queryFromUrl(curHref, 'code')
    const isVizio = queryFromUrl(curHref, 'isVizio')
    if (queryFromUrl(curHref, 'otp') && code) {
      if (sdk.isLogin()) {
        this.notifyOtpCodeTvLoginFb(code, isVizio)
      } else {
        sdk.on('login', () => {
          // 登录成功了...
          this.notifyOtpCodeTvLoginFb(code, isVizio)
        })
      }
      return
    }

    // otp登录 扫码直接登录进来的
    if (queryFromUrl(curHref, 'otp') && !code) {
      if (sdk.isLogin()) {
        this.notifyOtpScanTvLoginFb()
      } else {
        sdk.on('login', async () => {
          // 登录成功了...
          this.notifyOtpScanTvLoginFb()
        })
      }
      return
    } else {
      sdk.on('login', async () => {
        window.location.href = decodeURIComponent(
          queryFromUrl(curHref, 'from_url') ||
            'https://www.iq.com?_FORCE_INTL_SITE=INTL'
        )
      })
    }
    if (sdk.isLogin()) {
      if (
        queryFromUrl(curHref, 'agentType') === '446' &&
        !queryFromUrl(curHref, 'tvLogin')
      ) {
        const result = await sdk.notifyLoginConfirm()
        if (!result) {
          // 同步失败
          sdk.vueInstance.webTvNotifyStatus = 2
          return
        }
      }
      if (!queryFromUrl(curHref, 'thirdLoginAuth')) {
        await sdk.activatePartnerCode()
        window.location.href = decodeURIComponent(
          queryFromUrl(curHref, 'from_url') ||
            'https://www.iq.com?_FORCE_INTL_SITE=INTL'
        )
      }
    }
  }

  async notifyOtpCodeTvLoginFb(code, isVizio) {
    const { langPkg, mytv } = this.props
    const trueLang = langPkg.toJS()
    try {
      const res = await otpCodeLogin({
        code,
        ptid: this.loginSdkParam.ptid
      })
      // mytv 错误打点
      if (mytv && res.code !== 'A00000') {
        res.code &&
          sendBlockPb(res.code, {
            rpage: 'tv_login_no_H5_otp'
          })
      }
      if (res.code === 'A00000') {
        if (mytv) {
          const vipInfo = await getOtpVipInfo({ extraKey: code })
          vipInfo &&
            (window.location.href = rebuildCommonUrl(
              `vip/order?mytv=true&key=${vipInfo?.key}&${
                vipInfo?.extraInfo
                  ? serialize(JSON.parse(vipInfo.extraInfo))
                  : ''
              }&H5=true`
            ))
          !vipInfo && Toast.info(trueLang.webtv_cashier_otp_keyNotValid)
          !vipInfo &&
            sendBlockPb('A00000', {
              rpage: 'tv_login_no_H5_otp'
            })
        } else {
          window.location.href = isVizio
            ? `${hostName()}tv?success=1&isVizio=${isVizio}`
            : `${hostName()}tv?success=1`
        }
      } else if (res.code === 'P01010') {
        // 激活码已过期
        Toast.info(trueLang?.PCW_FRONTEND_1692784866490_756)
        setTimeout(() => {
          window.location.href = `${hostName}tv`
        }, 2000)
      } else {
        Toast.info(trueLang?.PCW_FRONTEND_1693201262677_849)
      }
    } catch (err) {
      Toast.info(trueLang?.PCW_FRONTEND_1693201262677_849)
    }
  }

  async notifyOtpScanTvLoginFb() {
    const { langPkg, mytv } = this.props
    const trueLang = langPkg.toJS()

    try {
      const result = await this.sdk.notifyLoginConfirm()
      this.sdk.vueInstance.webTvNotifyStatus = 0

      if (result) {
        if (mytv) {
          const vipInfo = await getOtpVipInfo({
            extraKey: queryFromUrl(window.location.href, 'token')
          })
          vipInfo &&
            (window.location.href = rebuildCommonUrl(
              `vip/order?mytv=true&key=${vipInfo?.key}&${
                vipInfo?.extraInfo
                  ? serialize(JSON.parse(vipInfo.extraInfo))
                  : ''
              }&H5=true`
            ))
          !vipInfo && Toast.info(trueLang.webtv_cashier_otp_keyNotValid)
          !vipInfo &&
            sendBlockPb('A00000', {
              rpage: 'tv_login_no_H5_otp'
            })
        } else {
          window.location.href = `${hostName()}tv?is_login_page=1&success=1`
        }
      } else {
        Toast.info(trueLang?.PCW_FRONTEND_1693215136736_641)
      }
    } catch (err) {
      Toast.info(trueLang?.PCW_FRONTEND_1693215136736_641)
    }
  }

  render() {
    const { langPkg, isOtpLogin, mytv } = this.props
    const trueLangPkg = langPkg.toJS()
    // const href = locUrl()

    return (
      <>
        <Meta
          desc={trueLangPkg.home_html_description}
          title={trueLangPkg.home_html_title}
        />
        <div
          id="login-sdk-container"
          style={{
            position: 'relative',
            background: '#fff',
            minHeight: '600px'
          }}
        />
        <TongJiPb
          rpage={
            isOtpLogin
              ? mytv
                ? 'tv_H5_login'
                : 'global-pssdk-login-tv'
              : 'login'
          }
        />
      </>
    )
  }
}

const mapStateToProps = state => ({
  // langPkg: state.getIn(['vip', 'vipLangPkg']),
  langs: state.getIn(['language', 'modeLangObj']),
  langPkg: state.getIn(['language', 'langPkg'])
})

export default connect(mapStateToProps)(Login)
