// import { batch } from 'react-redux'
import {
  formatPeriod,
  formatYear,
  sliceArray,
  isValidDate,
  formatSeconds,
  formatFullSecond
} from '@/kit/number'
import { handleImgSize, removeProtocol, appendQuery } from '@/kit/url'
import {
  isAlbumId,
  handleExtraTitle,
  // changeAlbumUrl,
  // changePcAlbumMod,
  supportUrlPbMod
} from '@/utils/video'
import {
  rebuildPlayUrl,
  // rebuildAlbumUrl,
  canUseWebp,
  forceIntlUrl,
  rebuildCommonUrl,
  handleChartInfoDTOS
} from '@/kit/common'
import secondsToDuration from '@/kit/isoDuration'
import { isServer } from '@/kit/device'
import { asyncAlls } from '@/kit/reqTools'
import { BATCH_AUTH } from '@/constants/interfaces'
import { isLogin } from '@/utils/userInfo'
// const { isAlbumId } = require('@/utils/video');
let size = 50

/**
 * 这几个专辑中的前两集在h5中可观看全集(越南:vn)
 * 漂亮書生 (253451201) 19rrhl9ewd
 * 天舞紀 (253376801) 19rrhlb03x
 * 惡之花 (9000000001097201) 2ffkws2qwt9
 * 重啟之極海聽雷 第壹季 (9000000001199501) 2ffkws2vkdd
 * 夏日衝浪店 (253163901) 19rrhl5qfl
 */
// const diversionAlbums = [
//   '253451201',
//   '253376801',
//   '9000000001097201',
//   '9000000001199501',
//   '253163901'
// ]

// const handleSearchUrl = name => {
//   const url = rebuildCommonUrl(`search?query=${encodeURIComponent(name)}`)
//   return url
// }

const handleActorInfoUrl = path => {
  const url = rebuildCommonUrl(`actor-info/${path}`)
  return url
}

const concatStarEnterArr = (cast, chnId) => {
  const {
    director = [],
    mainActor = [],
    guest = [],
    host = [],
    actor = [],
    star = [],
    dubber = []
  } = cast
  if (chnId === 1 || chnId === 2) {
    return [].concat(director, mainActor, actor, guest, host, star, dubber)
  } else if (chnId === 4) {
    return [].concat(director, dubber, mainActor, actor, host, guest, star)
  } else if (chnId === 6) {
    return [].concat(host, guest, star, mainActor, actor, director, dubber)
  } else {
    return [].concat(director, mainActor, host, actor, guest, star, dubber)
  }
}

const actorAndDirector = (cast = {}, chnId) => {
  const {
    director = [],
    mainActor = [],
    guest = [],
    host = [],
    actor = []
  } = cast
  let dirArr = (host.length && host) || director || []
  let actArr = (guest.length && guest) || mainActor || []
  dirArr = dirArr.filter(item => {
    return item && item.n
  })
  actArr = actArr.filter(item => {
    return item && item.n
  })
  host.forEach(item => {
    item.type = 'dir'
  })
  director.forEach(item => {
    item.type = 'dir'
  })
  // @孙立荣提出GLOBALLINEBUG-21895 特殊拼接频道页入口
  const starEnterArr = concatStarEnterArr(cast, chnId)
  // 特殊拼接频道页入口：
  //   let dirStr = ''
  //   dirArr.forEach(item => {
  //     if (item.n) {
  //       dirStr += item.n + ', '
  //     }
  //   })
  //   if (dirStr) {
  //     dirStr = dirStr.slice(0, dirStr.length - 2)
  //   }
  return {
    dirArr: dirArr.map(item => {
      return {
        name: item.n,
        url: handleActorInfoUrl(item.titleSEO)
        // actorInfoUrl: handleActorInfoUrl(item.titleSEO)
      }
    }),
    actorArr: actArr.map(item => {
      return {
        name: item.n,
        url: handleActorInfoUrl(item.titleSEO)
        // actorInfoUrl: handleActorInfoUrl(item.titleSEO)
      }
    }),
    actor: actor.map(item => {
      return {
        name: item.n,
        url: handleActorInfoUrl(item.titleSEO)
        // actorInfoUrl: handleActorInfoUrl(item.titleSEO)
      }
    }),
    mainActor: mainActor.map(item => {
      return {
        headPic: item.headPic,
        id: item.id,
        name: item.n,
        coverPic: item.coverPic
      }
    }),
    starEnterArr: starEnterArr.map(item => {
      return {
        headPic: item.headPic,
        id: item.titleSEO || item.id,
        name: item.n,
        type: item.type || 'act',
        coverPic: item.coverPic
      }
    })
  }
}

const pageTag = params => {
  const { total, firstOrder, pageType } = params
  let from
  let to
  let rangeMsg
  let totalPage
  const pageRange = []
  const totalNum = total || 1
  if (pageType === 'album') {
    size = 24
  } else {
    size = 50
  }
  if (totalNum % size === 0) {
    totalPage = totalNum / size
  } else {
    totalPage = totalNum / size + 1
  }
  totalPage = parseInt(totalPage, 10)
  for (let i = 0; i < totalPage; i++) {
    from = i * size + 1
    to = (i + 1) * size
    if (i === totalPage - 1) {
      to = totalNum
    }
    // from--> to之间有内容才会生成标签,tab签数才会++
    if (to >= firstOrder) {
      if (from < firstOrder) {
        from = firstOrder
      }
      rangeMsg = from + '-' + to
      pageRange.push({
        pageNo: i + 1,
        from,
        to,
        msg: rangeMsg
      })
    }
  }
  return pageRange
}

export const handleVideoInfo = (data, urlParam, pkg) => {
  const vipInfo = data.vipInfo || {}
  const type = data.type || ''
  const {
    vid,
    qipuId,
    qipuIdStr,
    albumId,
    chnId,
    playUrl,
    albumName,
    sourceCode,
    order,
    defaultTvId,
    name,
    desc,
    descSEO,
    albumDesc,
    publishTime,
    subTitle,
    shortName,
    cast,
    categoryTags,
    categoryTagMap,
    categories,
    pic,
    posterPic,
    posterWebpPic,
    initIssueTime,
    len,
    controlStatus,
    isQiyiProduced,
    isExclusive,
    contentType, // 视频内容类型
    allowRegion,
    shareAllowed, // 是否允许分享
    alternativeTitles,
    fatherEpisodeId,
    fatherEpisodeIdOrder,
    fatherCollectionIds,
    playLocSuffix,
    playHrefLangPile,
    contentChartInfoDTOS,
    firstPlayTimeLine,
    firstPlayTimeOnlyDate,
    contentRating,
    isShort
  } = data

  const rating = contentRating ? contentRating.rating : data.rating // 优先拿contentRating
  const { encodeId } = urlParam
  let isoTime = new Date(initIssueTime)
  if (isValidDate(isoTime)) {
    isoTime = isoTime.toISOString()
  } else {
    isoTime = initIssueTime
  }

  const loaderPic = posterPic || pic
  const period = formatPeriod(publishTime)
  const { h, m } = formatSeconds(len)
  let subTitleMsg = subTitle || shortName || name
  if (chnId === 4) {
    subTitleMsg = subTitle
  }
  // const episode = pkg.episode || ''
  // let subInfo
  // if (order && mod === 'ntw' && chnId === 4) {
  //   // 动漫
  //   subInfo = episode.replace('{}', order) + ' ' + subTitle
  // }
  const regionsAllowed =
    allowRegion && allowRegion.length ? allowRegion.join(',') : ''
  const alterTitle = alternativeTitles ? alternativeTitles.join(', ') : ''

  const albumPic284 =
    handleImgSize(posterPic, '_284_160.jpg', true) ||
    handleImgSize(pic, '_284_160.jpg', true)

  let mergeDesc = albumDesc ? `${albumDesc} ${desc}` : desc
  // albumDesc存在，desc不存在，去掉默认空格
  mergeDesc = mergeDesc.replace(/[\s]+$/g, '')

  const info = {
    // locUrl: `//www.iqiyi.com/${mod}/play?id=${encodeId}`,
    locUrl: `//www.iq.com/play/${encodeId}`,
    vid: vid || 'abe2c4788688b54418ebe6a4119bf1a5',
    tvId: qipuId,
    albumId,
    qipuIdStr,
    channelId: chnId,
    defaultTvId,
    playUrl,
    name,
    shortName,
    albumName: albumName || name,
    rating,
    order,
    publishTime: period,
    subTitle: subTitleMsg,
    year: formatYear(publishTime),
    isoUploadDate: isoTime,
    description: desc,
    descSEO,
    albumDesc,
    mergeDesc,
    categoryTags,
    categoryTagMap,
    categories,
    imgUrl: handleImgSize(pic, '_220_124.jpg'),
    albumFocus125: handleImgSize(pic, '_480_270.jpg'),
    thumbnailUrl1: handleImgSize(pic, '_480_270.jpg'),
    thumbnailUrl2: handleImgSize(pic, '_260_360.jpg'),
    thumbnailUrl3: handleImgSize(pic, '_240_240.jpg'),
    schemaVideoImage: handleImgSize(posterWebpPic, '_480_270.webp'),
    facebookShareUrl: 'https:' + handleImgSize(pic, '_1080_608.jpg'),
    imageLoader: loaderPic,
    isoDuration: secondsToDuration(len),
    vipInfo,
    controlStatus,
    isQiyiProduced,
    isExclusive,
    hour: h,
    min: m,
    shareAllowed,
    contentType,
    regionsAllowed,
    sourceCode,
    // subInfo,
    alterTitle,
    urlParams: '',
    albumPic284,
    len: formatFullSecond(len),
    fatherEpisodeId,
    fatherEpisodeIdOrder,
    fatherCollectionIds,
    playLocSuffix,
    playHrefLangPile: playHrefLangPile
      ? Object.keys(playHrefLangPile).map(key => ({
          hreflang: key,
          href: playHrefLangPile[key]
        }))
      : [],
    firstPlayTimeLine,
    firstPlayTimeOnlyDate,
    contentRating, // 越南|韩国分级信息项目的数据
    isShort
  }

  const { dirArr, actorArr, actor, mainActor, starEnterArr } = actorAndDirector(
    cast,
    chnId
  )
  info.dirArr = dirArr
  info.actorArr = actorArr
  info.actor = actor
  info.mainActor = mainActor
  info.starEnterArr = starEnterArr
  if (type === 'bodan') {
    info.videoType = 'bodan'
    if (isAlbumId(info.tvId)) {
      info.tvId = info.defaultTvId || info.tvId
    }
  } else if (sourceCode) {
    info.videoType = 'laiyuan'
  } else if (isAlbumId(data.albumId)) {
    info.videoType = 'juji'
    let pageNo
    if (order % size === 0) {
      pageNo = order / size
    } else {
      pageNo = order / size + 1
    }
    pageNo = pageNo || 1
    info.pageNo = parseInt(pageNo, 10)
    info.from = parseInt(pageNo - 1, 10) * size + 1
    info.to = info.from + size - 1
  } else if (contentType !== 1 && contentType !== 3) {
    // 1-正片 3-预告片，其他类型统一归到短视频类型
    // http://pms.qiyi.domain/browse/GLOBALREQ-5221
    info.videoType = 'shortVideo'
  } else {
    // 单视频-电影
    info.videoType = 'singleVideo'
  }
  const { extraOrderMsg } = handleExtraTitle({
    item: info,
    pkg,
    type: info.videoType
  })
  info.extraOrderMsg = extraOrderMsg
  info.order = fatherEpisodeIdOrder || order

  // 处理排行
  info.rankInfo = contentChartInfoDTOS
    ? handleChartInfoDTOS(contentChartInfoDTOS)
    : null
  return info
}

export const handleCurVideoInfo = (data, pkg) => {
  const {
    vid,
    qipuId,
    qipuIdStr,
    albumId,
    chnId,
    vu,
    name,
    albumName,
    order,
    cid,
    tvid,
    pd,
    an,
    shareAllowed,
    publishTime,
    contentType,
    subTitle,
    shortName,
    sourceCode,
    fatherEpisodeIdOrder,
    playLocSuffix,
    albumLocSuffix
  } = data
  let subTitleMsg = subTitle || shortName || name
  if (chnId === 4) {
    subTitleMsg = subTitle
  }
  let type = ''
  if (!sourceCode && isAlbumId(albumId)) {
    type = 'juji'
  }
  const { extraOrderMsg } = handleExtraTitle({ item: data, pkg, type })
  const info = {
    vid: vid || 'abe2c4788688b54418ebe6a4119bf1a5',
    tvId: qipuId || tvid,
    albumId,
    qipuIdStr,
    channelId: chnId || cid,
    playUrl: vu,
    albumName: albumName || an,
    order: fatherEpisodeIdOrder || order || pd,
    shareAllowed,
    publishTime: formatPeriod(publishTime),
    contentType,
    name,
    subTitle: subTitleMsg,
    extraOrderMsg,
    playLocSuffix,
    albumLocSuffix,
    shortName
  }
  return info
}

export const handlePlayAlbumInfo = data => {
  const vipInfo = data.vipInfo || {}
  const {
    desc,
    maxOrder,
    strategy,
    total,
    firstOrder,
    qipuId,
    defaultTvId,
    publishTime,
    cast,
    categoryTags,
    categoryTagMap,
    categories,
    chnId,
    prevueNum,
    albumPic,
    albumWebpPic,
    isQiyiProduced,
    isExclusive,
    sourceCode,
    qipuIdStr,
    allowRegion,
    alternativeTitles,
    name,
    fatherCollectionIds,
    playLocSuffix,
    albumLocSuffix,
    albumHrefLangPile,
    playHrefLangPile,
    contentChartInfoDTOS,
    isShort
  } = data

  let totalNum = total || maxOrder
  // 海贼王更新集数已超过1100 暂时先注释掉该逻辑，，不清楚这个限制的具体原因
  // totalNum = totalNum > 1100 ? 1100 : totalNum
  if (maxOrder && maxOrder < totalNum) {
    totalNum = maxOrder
  }
  if (prevueNum) {
    totalNum += prevueNum
  }
  const regionsAllowed =
    allowRegion && allowRegion.length ? allowRegion.join(',') : ''
  const alterTitle = alternativeTitles ? alternativeTitles.join(', ') : ''
  const info = {
    desc,
    maxOrder,
    strategy,
    categoryTags,
    categoryTagMap,
    categories,
    total: totalNum,
    defaultTvId,
    albumId: qipuId,
    year: formatYear(publishTime),
    totalPageRange: [],
    subPageRange: [],
    lastPageRange: [],
    chnId,
    publishTime,
    originalTotal: total,
    thumbnailUrl1: handleImgSize(albumPic, '_480_270.jpg'),
    thumbnailUrl2: handleImgSize(albumPic, '_260_360.jpg'),
    thumbnailUrl3: handleImgSize(albumPic, '_240_240.jpg'),
    schemaAlbumImage: handleImgSize(albumWebpPic, '_284_160.webp'),
    // facebookShareUrl: 'https:' + handleImgSize(albumWebpPic, '_260_360.jpg'),
    facebookShareUrl: 'https:' + handleImgSize(albumWebpPic, '_1080_608.jpg'),
    isQiyiProduced,
    isExclusive,
    vipInfo,
    qipuIdStr,
    regionsAllowed,
    alterTitle,
    name,
    fatherCollectionIds,
    playLocSuffix,
    albumLocSuffix,
    albumHrefLangPile: albumHrefLangPile
      ? Object.keys(albumHrefLangPile).map(key => ({
          hreflang: key,
          href: albumHrefLangPile[key]
        }))
      : [],
    playHrefLangPile: playHrefLangPile
      ? Object.keys(playHrefLangPile).map(key => ({
          hreflang: key,
          href: playHrefLangPile[key]
        }))
      : [],
    isShort
  }
  const { dirArr, actorArr, actor, mainActor, starEnterArr } = actorAndDirector(
    cast,
    chnId
  )
  info.dirArr = dirArr
  info.actorArr = actorArr
  info.actor = actor
  info.mainActor = mainActor
  info.starEnterArr = starEnterArr
  if (!sourceCode) {
    const pageRange = pageTag({ total: totalNum, firstOrder })
    info.totalPageRange = pageRange

    if (pageRange.length > 3) {
      info.subPageRange = pageRange.slice(0, 3)
      info.lastPageRange = pageRange.slice(3)
    } else {
      info.subPageRange = pageRange
    }
  }

  // 处理排行
  info.rankInfo = contentChartInfoDTOS
    ? handleChartInfoDTOS(contentChartInfoDTOS)
    : null
  return info
}

export const handleJujiList = async (data, params, pkg, ctx) => {
  const { epg, albumName, chnId, urlParams } = data
  const list = []
  const lockAid = []
  // const aid = '' + params.aid
  // let diversionTag = false
  // if (params.modeCode === 'vn') {
  //   diversionTag = diversionAlbums.includes(aid)
  // }
  const epgList = epg || []
  const webpTag = canUseWebp(ctx)
  const episode = pkg.episode || ''
  const isPbMsg = supportUrlPbMod(params.modeCode)
  epgList.forEach(item => {
    const vipInfo = item.vipInfo || {}
    let payMark = vipInfo.payMark || ''
    let payMarkSty = ''
    let payMarkFont = ''
    if (payMark === 'VIP_MARK') {
      payMarkSty = 'card_vip_icon'
      payMarkFont = 'VIP'
    }
    const lockType = vipInfo.advancedUnlockType
    // 超前解锁 + 钻石可看的逻辑
    if (
      lockType === 0 ||
      lockType === 1 ||
      payMark === 'PAY_MARK_ONLY_DIAMOND'
    ) {
      payMark = 'lock'
      payMarkSty = 'card_vip_icon lock'
      lockAid.push(item.qipuId)
    }
    let orderRseat = item.order
    if (item.contentType === 3) {
      payMark = 'preview'
      payMarkSty = 'card_vip_icon preview'
      payMarkFont = 'Preview'
      orderRseat = item.order + '_preview'
    }
    const publishTime = formatPeriod(item.publishTime)
    let albumPic =
      handleImgSize(item.albumPic, '_220_124.jpg') ||
      handleImgSize(item.pic, '_220_124.jpg')
    if (webpTag) {
      albumPic = handleImgSize(item.albumWebpPic, '_220_124.webp') || albumPic
    }
    let albumPic284 =
      handleImgSize(item.albumPic, '_284_160.jpg') ||
      handleImgSize(item.pic, '_284_160.jpg')
    if (webpTag) {
      albumPic284 =
        handleImgSize(item.albumWebpPic, '_284_160.webp') || albumPic284
    }
    // 儿童、纪录片、电视剧展示一句话推荐
    const focusTag = item.chnId === 15 || item.chnId === 3 || item.chnId === 2

    const albumPlayUrl =
      rebuildPlayUrl(item.playLocSuffix) || rebuildPlayUrl(item.qipuIdStr) || ''
    let albumPlayUrlPb = ''
    if (isPbMsg) {
      if (albumPlayUrl.indexOf('?') === -1) {
        albumPlayUrlPb += `?frmrp=album&frmb=album_information&frmrs=${item.order}`
      } else {
        albumPlayUrlPb += `&frmrp=album&frmb=album_information&frmrs=${item.order}`
      }
    }
    // let subInfo
    // if (params.modeCode === 'ntw' && chnId === 4) {
    //   // 动漫
    //   subInfo = episode.replace('{}', item.order) + ' ' + item.subTitle
    // }
    const alterTitle = item.alternativeTitles
      ? item.alternativeTitles.join(', ')
      : ''
    const { extraOrderMsg, extraOrderRseat } = handleExtraTitle({
      item,
      pkg,
      type: 'juji'
    })
    const subOrder =
      extraOrderMsg ||
      (item.order ? episode.replace('{}', item.order) : '') +
        `${item.contentType === 3 ? ' ' + pkg.search_rst_preview : ''}`

    const info = {
      vipInfo,
      payMark,
      payMarkSty,
      payMarkFont,
      qipuId: item.qipuId,
      tvId: item.qipuId,
      albumId: item.albumId,
      qipuIdStr: item.qipuIdStr,
      name: item.name,
      order: item.fatherEpisodeIdOrder || item.order,
      channelId: item.chnId,
      subTitle:
        subOrder === 'undefined' ? albumName : albumName + ' ' + subOrder, // 用于图片+标题渲染
      // subInfo, // 用于动漫播放页：播放器下方的title：专辑名称加副标题
      imgUrl: albumPic,
      imageLoader: item.pic,
      vid: item.vid || 'abe2c4788688b54418ebe6a4119bf1a5',
      episodeType: item.episodeType || 0, // 1:预告片
      orderRseat,
      seoTitle: extraOrderMsg || item.shortName || item.name,
      contentType: item.contentType,
      publishTime,
      chnId,
      urlParams: item.qipuIdStr ? '' : '&' + urlParams,
      albumName,
      albumPic284,
      focus: item.focus,
      focusTag,
      albumPlayUrl,
      albumPlayUrlPb,
      alterTitle,
      extraOrderMsg,
      extraOrderRseat,
      playLocSuffix: item.playLocSuffix
    }
    // 剧集类第1，2集为全集观看
    // if (diversionTag && info.order < 3) {
    //   info.todiversion = 0
    // }
    list.push(info)
  })
  const lockLen = lockAid.length
  // const lockTag = lockLen ? 1 : 0
  let jujiList = list
  if (lockLen && !isServer && isLogin()) {
    jujiList = await handleLockList({
      list,
      params,
      lockLen,
      lockAid
    })
  }
  return {
    list: jujiList,
    lockAid
  }
}

export const handleLockList = async data => {
  const { params, list, lockAid, lockLen } = data
  const { modeCode, langCode, platformId, deviceId } = params
  const httpArr = []
  const url = BATCH_AUTH
  if (lockLen > 20) {
    const splitLockAids = sliceArray(lockAid, 20)
    splitLockAids.forEach(item => {
      const options = {}
      options.params = {
        modeCode,
        langCode,
        platformId,
        deviceId,
        aids: item.join(',')
      }
      httpArr.push({ url, options })
    })
  } else {
    const options = {}
    options.params = {
      modeCode,
      langCode,
      platformId,
      deviceId,
      aids: lockAid.join(',')
    }
    httpArr.push({ url, options })
  }
  const batchData = await asyncAlls(httpArr)
  if (batchData.length) {
    const authObj = {}
    batchData.forEach(item => {
      authObj[item.aid] = item.subscribe
    })
    list.forEach(item => {
      if (authObj[item.tvId] === '1') {
        item.payMark = 'unlock'
        item.payMarkSty = 'card_vip_icon unlock'
      }
    })
  }
  return list
}

export const handleLaiyuanList = (data, paramObj) => {
  const {
    epg = [],
    chnId,
    total,
    pageIndex,
    albumId,
    albumName,
    urlParams,
    pageType,
    params
  } = data
  const { ctx, pkg } = paramObj
  const list = []
  // const aid = '' + params.aid
  // let diversionTag = false
  // if (params.modeCode === 'vn') {
  //   diversionTag = diversionAlbums.includes(aid)
  // }
  // const diversionIdx = data.length - 3
  const webpTag = canUseWebp(ctx)
  const pageNo = pageIndex || 1
  const isPbMsg = supportUrlPbMod(params.modeCode)
  epg.forEach((item, index) => {
    const vipInfo = item.vipInfo || {}
    const publishTime = formatPeriod(item.publishTime)
    const coverImg = item.coverImg || {}
    let albumPic =
      handleImgSize(item.albumPic, '_220_124.jpg') ||
      handleImgSize(item.pic, '_220_124.jpg') ||
      handleImgSize(coverImg.img, '_220_124.jpg')
    if (webpTag) {
      albumPic =
        handleImgSize(item.albumWebpPic, '_220_124.webp') ||
        handleImgSize(coverImg.imgWebp85, '_220_124.webp') ||
        albumPic
    }
    let albumPic284 =
      handleImgSize(item.albumPic, '_284_160.jpg') ||
      handleImgSize(item.pic, '_284_160.jpg') ||
      handleImgSize(coverImg.img, '_284_160.jpg')
    if (webpTag) {
      albumPic284 =
        handleImgSize(item.albumWebpPic, '_284_160.webp') ||
        handleImgSize(coverImg.imgWebp85, '_284_160.webp') ||
        albumPic
    }
    const albumPlayUrl =
      rebuildPlayUrl(item.playLocSuffix) || rebuildPlayUrl(item.qipuIdStr) || ''
    let albumPlayUrlPb = ''
    if (isPbMsg) {
      if (albumPlayUrl.indexOf('?') === -1) {
        albumPlayUrlPb = `?frmrp=album&frmb=album_information&frmrs=${publishTime}`
      } else {
        albumPlayUrlPb = `&frmrp=album&frmb=album_information&frmrs=${publishTime}`
      }
    }
    const idx = index + 1
    // let subInfo = publishTime
    // if (
    //   pageType === 'bodan' ||
    //   pageType === 'shortVideo' ||
    //   (params.modeCode === 'ntw' && chnId === 6)
    // ) {
    //   // 综艺
    //   subInfo = item.subTitle || item.shortName || item.name || publishTime
    // }
    /**
     * ntw台湾：subtitle > shortName > name > albumName+publishTime
     * 其他地区：shortName > name > albumName+publishTime
     */
    let subInfo = item.shortName || item.name || albumName + ' ' + publishTime
    if (params.modeCode === 'ntw') {
      subInfo = item.subTitle || subInfo
    }
    const contentType = item.contentType
    // const itemOrder = item.order
    const { extraOrderMsg, extraOrderRseat } = handleExtraTitle({
      item,
      pkg,
      subInfo
    })
    subInfo = extraOrderMsg || subInfo

    const alterTitle = item.alternativeTitles
      ? item.alternativeTitles.join(', ')
      : ''
    const info = {
      vipInfo,
      defaultTvId: item.defaultTvId,
      tvId: item.qipuId || item.episodeId,
      albumId: item.albumId || albumId,
      qipuIdStr: item.qipuIdStr,
      qipuId: item.qipuId || item.episodeId,
      name: item.name,
      albumName: albumName || '',
      channelId: item.chnId || chnId,
      publishTime,
      subTitle: subInfo,
      seoTitle:
        extraOrderMsg ||
        item.subTitle ||
        item.shortName ||
        item.name ||
        publishTime,
      imgUrl: albumPic,
      imageLoader: item.pic,
      vid: item.vid || 'abe2c4788688b54418ebe6a4119bf1a5',
      contentType,
      order: pageNo === 1 ? idx : idx + (pageNo - 1) * size,
      videoType: 'laiyuan',
      urlParams: item.qipuIdStr ? '' : '&' + urlParams,
      albumPic284,
      focus: item.focus,
      albumPlayUrl,
      albumPlayUrlPb,
      alterTitle,
      extraOrderMsg,
      extraOrderRseat,
      playLocSuffix: item.playLocSuffix
    }
    if (isAlbumId(info.tvId)) {
      info.tvId = item.defaultTvId || info.tvId
    }
    // 来源类列表最后两期为全集观看
    // if (diversionTag && idx > diversionIdx) {
    //   info.todiversion = 0
    // }
    list.push(info)
  })
  const pageRange = pageTag({ total, firstOrder: 1, pageType })
  return {
    list,
    pageRange
  }
}

export const handleSimiplayPlayList = (params = {}, ctx) => {
  const { data, pageType, mod } = params
  // const lang = data.lang
  const resysElement = data.resys_element || []
  if (resysElement.length === 0) {
    return { list: [], pbParam: {} }
  }
  const videoObj = resysElement[0] || {}
  const videos = videoObj.videos || []
  const list = []
  let trailers = []
  let clips = []
  let tidbit = []
  const webpTag = canUseWebp(ctx)
  let frmrp = 'play'
  let frmb = 'play_recommend'
  let videoTab = ''
  if (pageType === 'albumPage') {
    frmrp = 'album'
    frmb = 'recommend'
  } else if (pageType === 'albumTrailer') {
    frmrp = 'album'
    frmb = 'hightlight'
  } else if (pageType === 'playHighLight') {
    videoTab = 'highLight'
  }
  const isPbMsg = supportUrlPbMod(mod)
  videos.forEach((item, idx) => {
    const epg = item.epg || {}
    const pingback = item.pingback || {}
    const vipInfo = epg.vipInfo || {}
    let albumPic = removeProtocol(epg.albumPic)
    let albumPic260
    let albumPic284
    let albumPic220
    let albumPic480
    if (webpTag && epg.albumWebpPic) {
      albumPic = removeProtocol(epg.albumWebpPic)
      albumPic260 = handleImgSize(albumPic, '_260_360.webp')
      albumPic284 = handleImgSize(albumPic, '_284_160.webp')
      albumPic220 = handleImgSize(albumPic, '_220_124.webp')
      albumPic480 = handleImgSize(albumPic, '_480_270.webp')
    } else {
      albumPic260 = handleImgSize(albumPic, '_260_360.jpg')
      albumPic284 = handleImgSize(albumPic, '_284_160.jpg')
      albumPic220 = handleImgSize(albumPic, '_220_124.jpg')
      albumPic480 = handleImgSize(albumPic, '_480_270.jpg')
    }

    let videoUrl =
      rebuildPlayUrl(epg.playLocSuffix) ||
      rebuildPlayUrl(epg.albumLocSuffix) ||
      rebuildPlayUrl(epg.qipuIdStr) ||
      ''
    // if (changePcAlbumMod(mod) || isMobile) {
    // videoUrl = rebuildAlbumUrl(epg.albumLocSuffix) || changeAlbumUrl(videoUrl)
    // }
    if (isPbMsg) {
      if (videoUrl.indexOf('?') === -1) {
        videoUrl += `?frmrp=${frmrp}&frmb=${frmb}&frmrs=${idx}`
      } else {
        videoUrl += `&frmrp=play&frmb=play_recommend&frmrs=${idx}`
      }
    }

    // 兼容播放页精彩看点角标
    let payMark = vipInfo.payMark || ''
    let payMarkSty = ''
    if (payMark === 'VIP_MARK') {
      payMarkSty = 'card_vip_icon'
    }
    if (item.contenttype === 3) {
      // 预告片
      payMark = 'preview'
      payMarkSty = 'card_vip_icon preview'
      // orderRseat = item.order + '_preview'
    }

    const info = {
      albumName: epg.albumName,
      shortName: epg.shortName,
      vid: item.vid || 'abe2c4788688b54418ebe6a4119bf1a5',
      tvId: epg.defaultTvId || epg.qipuId, // 感觉没用，不敢删
      albumId: epg.albumId || epg.qipuId, // 同上
      qipuId: epg.qipuId,
      name: epg.name,
      focus: epg.focus,
      chnId: epg.chnId,
      seoTitle: epg.name,
      qipuIdStr: epg.qipuIdStr,
      url: videoUrl, // playUrl => url
      publishTime: formatPeriod(epg.publishTime),
      albumPic, //
      albumPic260,
      albumPic284,
      albumPic220,
      albumPic480,
      vipInfo,
      payMark,
      payMarkSty,
      desc: epg.desc, // 应用首页通栏组件RowBlock样式新增
      sourceCode: epg.sourceCode, // 同上
      tvCount: epg.tvCount, // 同上
      total: epg.total, // 同上
      year: epg.publishTime?.substring(0, 4), // 同上
      id: epg.albumId || epg.qipuId, // 同上
      region: epg.categoryTags ? epg.categoryTags.Place : '', // 同上
      pingback: {
        r_source: pingback.r_bkt,
        reasonid: pingback.r_ext,
        ht: 0,
        index: idx,
        stype: 2,
        bkt: data.bucket,
        e: data.eventId,
        r_area: data.r_area,
        position: idx + 1, // 同上
        block: 'play_recommend' // 同上
      },
      updateDesc: {
        chnId: epg.chnId || '',
        total: epg.total || 0,
        tvCount: epg.tvCount,
        publishTime: epg.publishTime
      },
      urlParams: epg.qipuIdStr ? '' : '&' + data.urlParams,
      len: formatFullSecond(epg.len),
      videoTab,
      playLocSuffix: epg.playLocSuffix,
      contentType: epg.contentType
    }
    list.push(info)
  })
  const pbParam = videos.length ? handleTjPbParam(data, videos) : {}
  if (pageType === 'albumTrailer') {
    // 资料页精彩看点需要根据contentType进行内容分类
    list.forEach(item => {
      if (item.contentType === 3) {
        trailers.push(item)
      } else if (item.contentType === 4) {
        clips.push(item)
      } else if (item.contentType === 5) {
        tidbit.push(item)
      }
    })
    if (
      list.length === trailers.length ||
      list.length === clips.length ||
      list.length === tidbit.length
    ) {
      trailers = []
      clips = []
      tidbit = []
    }
  }
  return {
    list,
    pbParam,
    trailers,
    clips,
    tidbit
  }
}
const handleTjPbParam = data => {
  const pbObj = {
    pbType: 'tj',
    bkt: data.bucket,
    e: data.eventId,
    r_area: data.r_area,
    stype: 2,
    block: 'play_recommend', // Slider组件，sendShowPB函数使用
    position: 2 // similarPlayList 移植过来
  }
  return pbObj
}

export const handlePlayAdInfo = (code, data) => {
  const coverInfo = data.covers[0] || {}
  const linkType = coverInfo.detail.linkType
  const { fc, fv } = coverInfo
  const type = linkType.type
  let url = linkType.url ? appendQuery({ fc, fv }, linkType.url) : ''
  url = forceIntlUrl(url)
  const block = `${code}_${data.strategyCode}_${coverInfo.code}_block`
  const rseat = `${code}_${data.strategyCode}_${coverInfo.code}_rseat`
  const detail = coverInfo.detail || {}
  const _linkType = { ...linkType, url }
  const adInfo = {
    webPic: removeProtocol(detail.pcImg),
    h5Pic: removeProtocol(detail.h5Img),
    button: detail.button,
    buttonColor: detail.buttonColor,
    type,
    block,
    rseat,
    linkType: _linkType,
    fc,
    fv
  }
  return adInfo
}

export const handleBodanList = (data, urlParam, params, langPkg, ctx) => {
  const epgList = data.epg || []
  if (epgList.length === 0) {
    return {}
  }
  const urlParams = data.urlParams || ''
  const videoObj = epgList[0]
  // 播单播放页展示播单名称和描述，故将第一个视频的名称和描述替换成播单的
  if (data.name || data.shortName) {
    videoObj.albumName = data.name || data.shortName
  }
  if (data.desc) {
    videoObj.desc = data.desc
  }

  videoObj.type = 'bodan'
  const firstVideo = handleVideoInfo(videoObj, urlParam, langPkg)
  // const aid = firstVideo.albumId || firstVideo.tvId
  const { list } = handleLaiyuanList(
    {
      epg: epgList,
      total: epgList.length,
      urlParams,
      params,
      pageType: 'bodan'
    },
    {
      ctx,
      pkg: langPkg
    }
  )

  return {
    firstVideo,
    bodanList: list
  }
}

export const handlePlayBannerInfo = (code, data) => {
  const coverInfo = data.covers[0] || {}
  const { fc, fv } = coverInfo
  const linkType = coverInfo.detail.linkType
  const type = linkType.type
  let url = linkType.url ? appendQuery({ fc, fv }, linkType.url) : ''
  url = forceIntlUrl(url)
  const block = `${code}_${data.strategyCode}_${coverInfo.code}_block`
  const rseat = `${code}_${data.strategyCode}_${coverInfo.code}_rseat`
  const detail = coverInfo.detail || {}
  const _linkType = { ...linkType, url }
  const bannerInfo = {
    webPic: removeProtocol(detail.pcImg),
    h5Pic: removeProtocol(detail.h5Img),
    button: detail.button,
    buttonColor: detail.buttonColor,
    type,
    block,
    rseat,
    linkType: _linkType,
    fc,
    fv
  }
  return bannerInfo
}

// export const handleSuperSeries = (data, pkg) => {
//   const aid = data.aid
//   const seriesData = data.data || {}
//   const seasons = seriesData.seasons || []
//   let curSeason = ''
//   let curSeasonX = ''
//   seasons.forEach(item => {
//     item.seasonX = (pkg.seasonX || '').replace('%s', item.season)
//     if (item.qipuId === aid) {
//       // 因黑帮少爷特火，需做特殊需求 孙立荣
//       // 因恋恋北极星特火，需做特殊需求 孙立荣
//       if (item.qipuId === 6794043917937901) {
//         curSeasonX = 'La Forte'
//       } else if (item.qipuId === 6759550786486501) {
//         curSeasonX = 'TV-Ver'
//       } else if (item.qipuId === 3903038202827801) {
//         curSeasonX = 'Uncut Ver'
//       } else if (item.qipuId === 8215481800062601) {
//         curSeasonX = 'TV Ver'
//       } else {
//         curSeasonX = item.seasonX
//       }
//       curSeason = item.season
//     }
//   })
//   return {
//     seasons,
//     curSeason,
//     curSeasonX
//   }
// }
