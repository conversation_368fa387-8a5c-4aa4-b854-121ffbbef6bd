window.lib = window.lib || {}
lib.kit = lib.kit || {}
lib = lib || {}
lib.kit = lib.kit || {}
lib.kit.util = lib.kit.util || {}
lib.kit.util.jsLoad = {}
var callbackNameValue = ''
window.Q = window.Q || {}
window.huQueue = []
window.vtypeSending = false
Q.__callbacks__ = Q.__callbacks__ || {}
;(function() {
  var v = function(t) {
    var e = []
    if (typeof t === 'object') {
      for (var i in t) {
        e[e.length] = encodeURIComponent(i) + '=' + encodeURIComponent(t[i])
      }
    }
    return e.join('&').replace(/%20/g, '+')
  }
  lib.kit.util.jsLoad.jsonp = function(t, e) {
    var i
    var r
    var a
    var n
    var o = 'window.Q.__callbacks__.'
    callbackNameValue =
      'cb' + Math.floor(Math.random() * 2147483648).toString(36)
    var s = function() {}
    e.timeout = e.timeout || 1e4
    e.params = e.params || {}
    e.params['callback'] = o + callbackNameValue
    var l = function(t) {
      if (t.clearAttributes) {
        t.clearAttributes()
      }
      if (t && t.parentNode) {
        t.parentNode.removeChild(t)
      }
      t = null
    }
    var c = Q.__callbacks__
    var f = e.oncomplete || s
    var u = e.onsuccess || s
    var d = e.onfailure || s
    var h = e.onTimeout || d || s
    c[callbackNameValue] = function(t) {
      if (i) {
        clearTimeout(i)
      }
      f(t)
      if (t && (t.code == 'A00000' || t.code == '0')) {
        u(t)
      } else {
        d(t)
      }
      l(r)
      setTimeout(function() {
        delete c[callbackNameValue]
      }, 6e3)
    }
    a = t + (/\?/.test(t) ? '&' : '?') + v(e.params)
    r = document.createElement('script')
    r.type = 'text/javascript'
    if (e.charset) {
      r.charset = e.charset
    }
    r.src = a
    r.onerror = function() {
      d({ code: 'E00000' })
      l(r)
    }
    document.getElementsByTagName('head')[0].appendChild(r)
    i = setTimeout(function() {
      h({ code: 'E00000' })
      n = true
      l(r)
    }, e.timeout)
  }
})()
var __getLocalDfp = function() {
  var t = window.localStorage && window.localStorage instanceof Storage
  var e = ''
  if (t) {
    e = localStorage.getItem('__dfp')
  }
  if (e && qa_isDpfUseful(e)) {
    return e.split('@')[0]
  }
  return ''
}
var qa_isDpfUseful = function(t) {
  if (t && t.length > 0) {
    var e = t.split('@')
    var i = e[2]
    var r = e[1]
    i = isNaN(i) ? 0 : Number(i)
    r = isNaN(r) ? 0 : Number(r)
    var a = new Date().getTime()
    if (a < r && a > i) {
      return true
    }
  }
  return false
}
var isPWA = function() {
  return window.matchMedia('(display-mode: standalone )').matches
}
var Url = function(t) {
  t = t || ''
  this.url = t
  this.query = {}
  this.hrParam = {}
  this.info = {}
  this.parse()
}
;(function(t) {
  t.prototype = {
    parse: function(t) {
      if (t) {
        this.url = t
      }
      this.parseAnchor()
      this.parseParam()
      this.parseInfo()
    },
    parseAnchor: function() {
      var t = this.url.match(/\#(.*)/)
      t = t ? t[1] : null
      this._anchor = t
      if (t != null) {
        this.anchor = this.getNameValuePair(t)
        this.url = this.url.replace(/\#.*/, '')
      }
    },
    parseParam: function() {
      var t = this.url.match(/\?([^\?]*)/)
      t = t ? t[1] : null
      if (t != null) {
        this.query = this.getNameValuePair(t)
        this.hrParam = this.getNameValuePair(t, '-')
        this.url = this.url.replace(/\?([^\?]*)/, '')
      }
    },
    parseInfo: function() {
      var t = /(\w+):\/\/([^\/:]+):?(\d*)((?:\/|$).*)/
      var e = this.url.match(t)
      if (e) {
        var i = e[1]
        var r = e[2]
        var a = e[3]
        var n = e[4]
        this.info = { protocol: i, host: r, port: a, path: n }
      }
    },
    getNameValuePair: function(t, e) {
      var r = {}
      var i = e || '&'
      var a = new RegExp('([^' + i + '=]*)(?:=([^' + i + ']*))?', 'gim')
      t.replace(a, function(t, e, i) {
        if (e == '') {
          return r
        }
        r[e] = i || ''
      })
      return r
    },
    getParam: function(t) {
      return this.query[t] || ''
    },
    serialize: function(t) {
      var e = []
      for (var i in t) {
        if (t[i] == null || t[i] == '') {
          e.push(i + '=')
        } else {
          e.push(i + '=' + t[i])
        }
      }
      return e.join('&')
    }
  }
})(Url)
window.lib = window.lib || {}
lib.kit = lib.kit || {}
lib.kit.Url = Url
if (window.$reg) {
  $reg('lib.kit.Url', function() {
    lib.kit.Url = Url
  })
}
var md5V2 = (function() {
  var s = function(t, e) {
    return (t << e) | (t >>> (32 - e))
  }
  var E = function(t, e) {
    var i
    var r
    var a
    var n
    var o
    a = t & 2147483648
    n = e & 2147483648
    i = t & 1073741824
    r = e & 1073741824
    o = (t & 1073741823) + (e & 1073741823)
    if (i & r) {
      return o ^ 2147483648 ^ a ^ n
    }
    if (i | r) {
      if (o & 1073741824) {
        return o ^ 3221225472 ^ a ^ n
      } else {
        return o ^ 1073741824 ^ a ^ n
      }
    } else {
      return o ^ a ^ n
    }
  }
  var l = function(t, e, i) {
    return (t & e) | (~t & i)
  }
  var c = function(t, e, i) {
    return (t & i) | (e & ~i)
  }
  var f = function(t, e, i) {
    return t ^ e ^ i
  }
  var u = function(t, e, i) {
    return e ^ (t | ~i)
  }
  var A = function(t, e, i, r, a, n, o) {
    t = E(t, E(E(l(e, i, r), a), o))
    return E(s(t, n), e)
  }
  var M = function(t, e, i, r, a, n, o) {
    t = E(t, E(E(c(e, i, r), a), o))
    return E(s(t, n), e)
  }
  var T = function(t, e, i, r, a, n, o) {
    t = E(t, E(E(f(e, i, r), a), o))
    return E(s(t, n), e)
  }
  var U = function(t, e, i, r, a, n, o) {
    t = E(t, E(E(u(e, i, r), a), o))
    return E(s(t, n), e)
  }
  var j = function(t) {
    var e
    var i = t.length
    var r = i + 8
    var a = (r - (r % 64)) / 64
    var n = (a + 1) * 16
    var o = Array(n - 1)
    var s = 0
    var l = 0
    while (l < i) {
      e = (l - (l % 4)) / 4
      s = (l % 4) * 8
      o[e] = o[e] | (t.charCodeAt(l) << s)
      l++
    }
    e = (l - (l % 4)) / 4
    s = (l % 4) * 8
    o[e] = o[e] | (128 << s)
    o[n - 2] = i << 3
    o[n - 1] = i >>> 29
    return o
  }
  var N = function(t) {
    var e = ''
    var i = ''
    var r
    var a
    for (a = 0; a <= 3; a++) {
      r = (t >>> (a * 8)) & 255
      i = '0' + r.toString(16)
      e += i.substr(i.length - 2, 2)
    }
    return e
  }
  var O = function(t) {
    t = t.replace(/\x0d\x0a/g, '\n')
    var e = ''
    for (var i = 0; i < t.length; i++) {
      var r = t.charCodeAt(i)
      if (r < 128) {
        e += String.fromCharCode(r)
      } else if (r > 127 && r < 2048) {
        e += String.fromCharCode((r >> 6) | 192)
        e += String.fromCharCode((r & 63) | 128)
      } else {
        e += String.fromCharCode((r >> 12) | 224)
        e += String.fromCharCode(((r >> 6) & 63) | 128)
        e += String.fromCharCode((r & 63) | 128)
      }
    }
    return e
  }
  return function(t) {
    t += ''
    var e = Array()
    var i
    var r
    var a
    var n
    var o
    var s
    var l
    var c
    var f
    var u = 7
    var d = 12
    var h = 17
    var v = 22
    var p = 5
    var m = 9
    var g = 14
    var w = 20
    var b = 4
    var _ = 11
    var I = 16
    var k = 23
    var y = 6
    var C = 10
    var P = 15
    var Q = 21
    t = O(t)
    e = j(t)
    s = 1732584193
    l = 4023233417
    c = 2562383102
    f = 271733878
    for (i = 0; i < e.length; i += 16) {
      r = s
      a = l
      n = c
      o = f
      s = A(s, l, c, f, e[i + 0], u, 3614090360)
      f = A(f, s, l, c, e[i + 1], d, 3905402710)
      c = A(c, f, s, l, e[i + 2], h, 606105819)
      l = A(l, c, f, s, e[i + 3], v, 3250441966)
      s = A(s, l, c, f, e[i + 4], u, 4118548399)
      f = A(f, s, l, c, e[i + 5], d, 1200080426)
      c = A(c, f, s, l, e[i + 6], h, 2821735955)
      l = A(l, c, f, s, e[i + 7], v, 4249261313)
      s = A(s, l, c, f, e[i + 8], u, 1770035416)
      f = A(f, s, l, c, e[i + 9], d, 2336552879)
      c = A(c, f, s, l, e[i + 10], h, 4294925233)
      l = A(l, c, f, s, e[i + 11], v, 2304563134)
      s = A(s, l, c, f, e[i + 12], u, 1804603682)
      f = A(f, s, l, c, e[i + 13], d, 4254626195)
      c = A(c, f, s, l, e[i + 14], h, 2792965006)
      l = A(l, c, f, s, e[i + 15], v, 1236535329)
      s = M(s, l, c, f, e[i + 1], p, 4129170786)
      f = M(f, s, l, c, e[i + 6], m, 3225465664)
      c = M(c, f, s, l, e[i + 11], g, 643717713)
      l = M(l, c, f, s, e[i + 0], w, 3921069994)
      s = M(s, l, c, f, e[i + 5], p, 3593408605)
      f = M(f, s, l, c, e[i + 10], m, 38016083)
      c = M(c, f, s, l, e[i + 15], g, 3634488961)
      l = M(l, c, f, s, e[i + 4], w, 3889429448)
      s = M(s, l, c, f, e[i + 9], p, 568446438)
      f = M(f, s, l, c, e[i + 14], m, 3275163606)
      c = M(c, f, s, l, e[i + 3], g, 4107603335)
      l = M(l, c, f, s, e[i + 8], w, 1163531501)
      s = M(s, l, c, f, e[i + 13], p, 2850285829)
      f = M(f, s, l, c, e[i + 2], m, 4243563512)
      c = M(c, f, s, l, e[i + 7], g, 1735328473)
      l = M(l, c, f, s, e[i + 12], w, 2368359562)
      s = T(s, l, c, f, e[i + 5], b, 4294588738)
      f = T(f, s, l, c, e[i + 8], _, 2272392833)
      c = T(c, f, s, l, e[i + 11], I, 1839030562)
      l = T(l, c, f, s, e[i + 14], k, 4259657740)
      s = T(s, l, c, f, e[i + 1], b, 2763975236)
      f = T(f, s, l, c, e[i + 4], _, 1272893353)
      c = T(c, f, s, l, e[i + 7], I, 4139469664)
      l = T(l, c, f, s, e[i + 10], k, 3200236656)
      s = T(s, l, c, f, e[i + 13], b, 681279174)
      f = T(f, s, l, c, e[i + 0], _, 3936430074)
      c = T(c, f, s, l, e[i + 3], I, 3572445317)
      l = T(l, c, f, s, e[i + 6], k, 76029189)
      s = T(s, l, c, f, e[i + 9], b, 3654602809)
      f = T(f, s, l, c, e[i + 12], _, 3873151461)
      c = T(c, f, s, l, e[i + 15], I, 530742520)
      l = T(l, c, f, s, e[i + 2], k, 3299628645)
      s = U(s, l, c, f, e[i + 0], y, 4096336452)
      f = U(f, s, l, c, e[i + 7], C, 1126891415)
      c = U(c, f, s, l, e[i + 14], P, 2878612391)
      l = U(l, c, f, s, e[i + 5], Q, 4237533241)
      s = U(s, l, c, f, e[i + 12], y, 1700485571)
      f = U(f, s, l, c, e[i + 3], C, 2399980690)
      c = U(c, f, s, l, e[i + 10], P, 4293915773)
      l = U(l, c, f, s, e[i + 1], Q, 2240044497)
      s = U(s, l, c, f, e[i + 8], y, 1873313359)
      f = U(f, s, l, c, e[i + 15], C, 4264355552)
      c = U(c, f, s, l, e[i + 6], P, 2734768916)
      l = U(l, c, f, s, e[i + 13], Q, 1309151649)
      s = U(s, l, c, f, e[i + 4], y, 4149444226)
      f = U(f, s, l, c, e[i + 11], C, 3174756917)
      c = U(c, f, s, l, e[i + 2], P, 718787259)
      l = U(l, c, f, s, e[i + 9], Q, 3951481745)
      s = E(s, r)
      l = E(l, a)
      c = E(c, n)
      f = E(f, o)
    }
    var S = N(s) + N(l) + N(c) + N(f)
    return S.toLowerCase()
  }
})()
window.lib = window.lib || {}
lib.md5V2 = md5V2
Object.extend = function(t, e) {
  for (var i in e) {
    t[i] = e[i]
  }
  return t
}
window.lib = window.lib || {}
if (!lib.SITE_DOMAIN) {
  var getDomain = function() {
    var t = 2
    var e = window.location.hostname.split('.')
    e = e.slice(e.length - t)
    return e.join('.')
  }
  lib.SITE_DOMAIN = getDomain()
}
lib.PROJECT_VERSION = '201104221818300317'
lib.action = lib.action || {}
lib.action.Qa = function() {
  this.init = function(t) {
    var e = this
    var i = lib.SITE_DOMAIN.match(/pps/)
    try {
      var r = navigator.userAgent.toLowerCase()
      this.par = {}
      this.pars = []
      this.custom = {}
      this.filter = []
      this.time = 0
      this.w = window
      this.l = window.location
      this.d = window.document
      this.searchJson = this.queryToJson(this.l.href)
      this.urlMap = {
        rdm: 'rdm',
        qtcurl: 'qtcurl',
        rfr: 'rfr',
        lrfr: 'lrfr',
        jsuid: 'jsuid',
        qtsid: 'qtsid',
        ppuid: 'ppuid',
        platform: 'platform',
        weid: 'weid',
        pru: 'pru',
        flshuid: 'flshuid',
        fcode: 'fcode',
        ffcode: 'ffcode',
        coop: 'coop',
        odfrm: 'odfrm',
        fvcode: 'fvcode',
        nu: 'nu',
        mod: 'mod',
        pcau: 'pcau'
      }
      this.cookieMap = {
        flshuid: 'QC005',
        jsuid: 'QC006',
        pru: 'P00PRU',
        lrfr: 'QC007',
        qtsid: 'QC008',
        QY_FC: 'QC009',
        QY_FFC: 'QC014',
        gaflag: 'QC011',
        odfrm: 'QC132',
        QY_FV: 'QC142',
        EDM_FC: 'QCedm001',
        EDM_FV: 'QCedm002',
        pcau: 'PCAU'
      }
      t = t || {}
      this.times = t.times || 5
      this.timeouts = t.timeouts || 1e3
      this.url = t.url || '//msg-intl.qy.net/jspb.gif'
      this.newUrl = t.newUrl || '//msg-intl.qy.net/act'
      var a = window.intlPageInfo || {}
      if (a.i18n === 'global') {
        this.url = t.url || '//msg-intl.qy.net/jspb.gif'
        this.newUrl = t.newUrl || '//msg-intl.qy.net/act'
      }
      if (this.url.indexOf('?') == -1) {
        this.url += '?'
      } else if (this.url.slice(-1) != '&') {
        this.url += '&'
      }
      if (this.newUrl.indexOf('?') == -1) {
        this.newUrl += '?'
      } else if (this.newUrl.slice(-1) != '&') {
        this.newUrl += '&'
      }
      this.flag = t.flag || 'QC010'
      this.callback = t.callback || function() {}
      if (typeof t.urlMap === 'object') {
        Object.extend(this.urlMap, t.urlMap)
      }
      if (typeof t.cookieMap === 'object') {
        Object.extend(this.cookieMap, t.cookieMap)
      }
      if (typeof t.custom === 'object') {
        Object.extend(this.custom, t.custom)
      }
      if (t.filter instanceof Array) {
        this.filter = t.filter
      }
      var n = this.urlMap
      this.par[n.rdm] = this.rand()
      this.par[n.qtcurl] = this.u(this.l.href)
      this.par[n.rfr] = this.u(this.d.referrer)
      this.par[n.lrfr] = this.getLrfr()
      this.par[n.jsuid] = this.getJsuid()
      this.par[n.qtsid] = this.getQtsid()
      this.par[n.ppuid] = this.getUserInfoUid()
      this.par[n.nu] = this.getNu()
      this.par[n.platform] = /ipad/i.test(r)
        ? '21'
        : /(iphone os)|(android)/i.test(r)
        ? '31'
        : '11'
      if (i) {
        this.par[n.platform] = '20' + this.par[n.platform]
      }
      if (this.w.pingbackParams) {
        if (Object.assign) {
          this.par = Object.assign({}, this.w.pingbackParams, this.par)
        } else {
          var o = Object.extend({}, this.w.pingbackParams)
          this.par = Object.extend(o, this.par)
        }
      }
      if (
        this.w.Q &&
        Q.PageInfo &&
        Q.PageInfo.playPageInfo &&
        Q.PageInfo.playPageInfo.videoTemplate
      ) {
        this.par['tmplt'] = Q.PageInfo.playPageInfo.videoTemplate || ''
      }
      this.par[n.fcode] = this.getFc()
      this.par[n.ffcode] = this.getFfc()
      this.par[n.coop] = this.getCoop()
      this.par[n.weid] = this.getWeid()
      this.par[n.pru] = this.getPRU()
      this.par[n.fvcode] = this.getFv()
      this.par[n.mod] = this.getMod()
      Object.extend(this.par, this.custom)
      var s = this.searchJson
      var l = this.cookieMap[this.urlMap.odfrm]
      var c = s[this.urlMap.odfrm] || this.cookieGet(l) || ''
      if (c) {
        c = c
        this.par[n.odfrm] = c
        this.cookieSet(l, c, 0, '/', lib.SITE_DOMAIN)
        var f = this.d.getElementsByTagName('body')[0]
        var u = this.queryToJson(f.getAttribute('data-pb') || '') || {}
        u[n.odfrm] = c
        var d = this.jsonToQuery(u)
        f.setAttribute('data-pb', d)
      }
      var h = document.getElementById('block-B')
      if (h && h.getAttribute('data-pb')) {
        var v = h.getAttribute('data-pb')
        var p = v.match(/(^|&)?tmplt=([^&]+)/i)
        if (p && p[2]) {
          e.par['tmplt'] = p[2]
        }
      }
      var m = /ipad/i.test(r) || /iphone os/i.test(r) || /lepad_hls/i.test(r)
      if (m) {
        e.par[n.flshuid] = e.getJsuid()
      } else {
        e.par[n.flshuid] = e.getFlashId()
      }
      e.par[n.pcau] = e.getPcau()
      var g = 'ChEnYH0415dadrrEDFf2016'
      var w = m ? e.par[n.flshuid] : e.getJsuid()
      this.getFingerPrint(function(t) {
        if (t) {
          e.par['dfp'] = t
        }
        e.setQC005()
        e.post()
      })
    } catch (t) {}
    this.sendNewQa()
    var b = this.cookieGet('intl_t20_pb')
    if (b) {
      var _ = new Image()
      _.src = b
      var I = this.domainName()
      if (_.complete) {
        e.cookieRemove('intl_t20_pb', '/', I)
      } else {
        _.onload = function() {
          e.cookieRemove('intl_t20_pb', '/', I)
        }
        _.onerror = function() {
          e.cookieRemove('intl_t20_pb', '/', I)
        }
      }
    }
    this.HandleEDMFcFV()
  }
  this.sendNewQa = function() {
    var i = this
    this.getHu(function(e) {
      i.getFingerPrint(function(t) {
        i.initNewPar(e, t)
        i.setQC005()
        i.post(true)
      })
    })
  }
  this.getPtid = function() {
    var t = navigator.userAgent.toLowerCase()
    var e = navigator.platform.toLowerCase()
    var i = '002'
    var r = '101'
    var a = '00'
    var n = '00'
    var o = '000000'
    var s = '01'
    if (/(iphone)|(android)/i.test(t)) {
      s = '02'
    } else if (/pad/i.test(t)) {
      s = '03'
    }
    var l = '01'
    if (/android/i.test(e)) {
      l = '02'
    }
    if (/mac/i.test(e)) {
      l = '08'
    }
    if (/ios/i.test(e)) {
      l = '03'
    }
    return s + l + i + r + a + n + o
  }
  this.extend = function(t, e) {
    var i = t || {}
    var r
    for (var a = 1, n = arguments.length; a < n; a++) {
      r = arguments[a]
      if (r) {
        for (var o in r) {
          if (r.hasOwnProperty(o)) {
            i[o] = r[o]
          }
        }
      }
    }
    return i
  }
  this.initNewPar = function(t, e) {
    var i
    var r = '1_10_101'
    var a = window.intlPageInfo || {}
    if (a.i18n === 'global') {
      var n = navigator.userAgent.toLowerCase()
      if (n.indexOf('electron/') > -1) {
        r = '1_11_223'
        if (n.indexOf('mac') > -1) {
          r = '1_11_222'
        }
      } else {
        r = '1_10_222'
        if (/(android)|(like mac os x)/i.test(n)) {
          r = '2_20_223'
        }
      }
    }
    var o = window.__global__info && window.__global__info.userInfo
    var s = o && o.hu
    s = this.cookieGet('hu') || s
    this.par = {
      u: this.cookieGet('u') || this.getFlashId(),
      pu: this.cookieGet('pu') || this.getUserInfoUid(),
      rn: this.rand(),
      p1: this.cookieGet('p1') || r,
      mkey: this.cookieGet('mkey') || '',
      de: this.cookieGet('de') || this.getQtsid(),
      stime: new Date().getTime(),
      ce: this.getWeid(),
      bstp: 0,
      t: 22,
      v: 1,
      rpage: '',
      hu: s ? s : t,
      dfp: e,
      mod: this.getMod(),
      purl: this.u(this.l.href),
      nu: this.getNu(),
      vfm: this.searchJson.vfm || '',
      rfr: this.u(this.d.referrer),
      pcau: this.getPcau(),
      ptp: '',
      pagev: 'homepage_adv_v1',
      coop: this.getCoop(),
      lrfr: this.getLrfr(),
      ptid: this.getPtid()
    }
    if (a.i18n === 'global') {
      delete this.par['ptid']
      this.par['v'] = this.cookieGet('v') || ''
      if (isPWA()) this.par['stuptype'] = 'shortcut'
      this.par['timezone'] = new Date().toUTCString()
      var l = new lib.kit.Url(document.location.href)
      var c = a.pbInfos || {}
      this.par['rpage'] = c.rpage || ''
      this.par['lang'] =
        l.getParam('lang') || c.lang || this.cookieGet('lang') || 'en_us'
      this.par['re'] = window.screen.width + '*' + window.screen.height
      this.par['s2'] = l.getParam('frmrp') || ''
      this.par['s3'] = l.getParam('frmb') || ''
      if (c.pbShowParams) {
        if (Object.assign) {
          this.par = Object.assign({}, this.par, c.pbShowParams)
        } else {
          this.par = this.extend({}, this.par, c.pbShowParams)
        }
      }
      var f = this.getUtmPbParams()
      this.par['vfm'] = encodeURIComponent(f)
    }
    var u = this.cookieGet('b_ext_ip') || ''
    if (u) {
      this.par['b_ext'] = JSON.stringify({ bip: u })
    }
    i = this.w.Q && this.w.Q.PageInfo && this.w.Q.PageInfo.playPageInfo
    if (i) {
      if (i.videoTemplate) {
        this.par.tmplt = i.videoTemplate || ''
      }
      if (i.cid !== undefined) {
        this.par.c1 = i.cid
      }
      if (i.albumId) {
        this.par.aid = i.albumId
      }
    }
    if (this.w.pingbackParams) {
      if (Object.assign) {
        this.par = Object.assign({}, this.par, this.w.pingbackParams)
      } else {
        var d = Object.extend({}, this.par)
        this.par = Object.extend(d, this.w.pingbackParams)
      }
    }
  }
  var noDfpTag = 1
  this.getFingerPrint = function(e) {
    noDfpTag = 0
    var t = __getLocalDfp()
    if (t) {
      e(t)
    } else if (window.dfp) {
      window.dfp.getFingerPrint(
        function(t) {
          e(t)
        },
        function(t) {
          e()
        }
      )
    } else {
      setTimeout(function() {
        if (noDfpTag) {
          e()
        }
      }, 1e4)
    }
  }
  this.getHu = function(t) {
    var e = window.__global__info && window.__global__info.userInfo
    if (e && e.hu) {
      t(e.hu)
    } else {
      this.getUser(t)
    }
  }
  this.getUser = function(t) {
    var e = this.cookieGet('I00019') || ''
    if (!e) {
      t(-1)
      return
    }
    window.huQueue.push(t)
    if (window.vtypeSending) {
      return
    }
    window.vtypeSending = true
    var i = navigator.userAgent.toLowerCase()
    var r = 3
    if (
      /(android)|(like mac os x)/i.test(i) ||
      (/(intel mac os x)/i.test(i) && 'ontouchend' in document)
    ) {
      r = 4
    }
    var a = this.cookieGet('QC005') || ''
    var n = this.cookieGet('mod') || 'intl'
    var o = this.cookieGet('lang') || 'en_us'
    var s = '//pcw-api.iq.com/api/vtype'
    var l =
      s +
      '?batch=1&deviceId=' +
      a +
      '&modeCode=' +
      n +
      '&langCode=' +
      o +
      '&platformId=' +
      r +
      '&vipInfoVersion=5.0'
    var c = {
      onsuccess: function(t) {
        var e = -1
        var i = []
        window.__global__info = window.__global__info || {}
        if (t.data && t.data.all_vip && t.data.all_vip.length) {
          var r = t.data.all_vip
          window.__allhu__ = r
          for (var a = 0; a < r.length; a++) {
            var n = r[a]
            if (n.status == '1') {
              i.push(n.vipType)
            }
          }
          window.__global__info.userInfo = r[0]
          e = i.length ? i.join(',') : -1
          window.__global__info.userInfo.hu = e
        } else {
          window.__global__info.userInfo = window.__global__info.userInfo || {}
          window.__global__info.userInfo.hu = e
        }
        window.__hu__ = e
        window.vtypeSending = false
        for (var a = 0; a < window.huQueue.length; a++) {
          if (window.huQueue[a]) {
            window.huQueue[a](e)
          }
        }
      },
      onfailure: function() {
        window.vtypeSending = false
        for (var t = 0; t < window.huQueue.length; t++) {
          if (window.huQueue[t]) {
            window.huQueue[t](-1)
          }
        }
      }
    }
    lib.kit.util.jsLoad.jsonp(l, c)
  }
  this.getUserInfoUid = function() {
    try {
      var userInfoStorage =
        window.__global__info && window.__global__info.userInfo
      var uid = userInfoStorage && userInfoStorage.uid
      if (uid) {
        return uid
      } else {
        var userInfo = this.cookieGet('I00002')
        if (userInfo) {
          userInfo =
            userInfo == window.JSON
              ? window.JSON.parse(userInfo)
              : eval('(' + userInfo + ')')
        }
        if (userInfo && userInfo.data) {
          window.__global__info = window.__global__info || {}
          window.__global__info.userInfo = window.__global__info.userInfo || {}
          window.__global__info.userInfo.uid = userInfo.data.uid || ''
          return userInfo.data.uid || ''
        } else {
          return ''
        }
      }
    } catch (e) {
      return ''
    }
  }
  this.u = function(t) {
    try {
      var e = encodeURIComponent
      return e instanceof Function ? e(t) : escape(t)
    } catch (t) {
      return ''
    }
  }
  this.hash = function(t) {
    try {
      var e = 1
      var i = 0
      if (t) {
        e = 0
        for (var r = t.length - 1; r >= 0; r--) {
          i = t.charCodeAt(r)
          e = ((e << 6) & 268435455) + i + (i << 14)
          i = e & 266338304
          e = i !== 0 ? e ^ (i >> 21) : e
        }
      }
      return e
    } catch (t) {
      return ''
    }
  }
  this.rand = function(t) {
    try {
      var e = []
      if (!isNaN(t)) {
        for (var i = 0; i < t; i++) {
          e.push(Math.round(Math.random() * 2147483647).toString(36))
        }
      } else {
        e.push(Math.round(Math.random() * 2147483647))
      }
      return e.join('')
    } catch (t) {
      return ''
    }
  }
  this.cookieGet = function(t) {
    var e = function(t) {
      if (
        new RegExp(
          '^[^\\x00-\\x20\\x7f\\(\\)<>@,;:\\\\\\"\\[\\]\\?=\\{\\}\\/\\u0080-\\uffff]+$'
        ).test(t)
      ) {
        var e = new RegExp('(^| )' + t + '=([^;]*)(;|$)')
        var i = e.exec(document.cookie)
        if (i) {
          return i[2] || ''
        }
      }
      return ''
    }
    try {
      t = e(t)
      if (typeof t === 'string') {
        if (t.length > 1 && t == 'deleted') {
          return ''
        } else {
          return decodeURIComponent(t) || ''
        }
      } else {
        return ''
      }
    } catch (t) {
      return ''
    }
  }
  this.cookieSet = function(t, e, i, r, a, n) {
    try {
      if (window.funcCookieDisable) {
        if (
          t === 'QC173' ||
          t === 'QC010' ||
          t === 'QC008' ||
          t === 'QC007' ||
          t === 'QC006'
        ) {
          return
        }
      }
      var o = []
      o.push(t + '=' + encodeURIComponent(e))
      if (i) {
        var s = new Date()
        var l = s.getTime() + i * 36e5
        s.setTime(l)
        o.push('expires=' + s.toGMTString())
      }
      if (r) {
        o.push('path=' + r)
      }
      if (a) {
        o.push('domain=' + a)
      }
      if (n) {
        o.push(n)
      }
      document.cookie = o.join(';')
    } catch (t) {
      return ''
    }
  }
  this.domainName = function() {
    var t = window.location.hostname
    t = t.split(':')[0]
    var e = t
      .split('.')
      .splice(1)
      .join('.')
    return e
  }
  this.cookieRemove = function(t, e, i, r) {
    try {
      document.cookie =
        t +
        '=;' +
        'expires=Fri, 31 Dec 1999 23:59:59 GMT;' +
        'path=' +
        (e || '/') +
        ';' +
        'domain=' +
        i
    } catch (t) {
      return ''
    }
  }
  this.getJsuid = function() {
    try {
      var t
      var e = this.cookieMap.jsuid
      t = this.cookieGet(e)
      if (!t || !isNaN(t)) {
        t = this.rand(4)
      }
      this.cookieSet(e, t, 365 * 24, '/', lib.SITE_DOMAIN)
      return t
    } catch (t) {
      return ''
    }
  }
  this.getQtsid = function() {
    try {
      var t
      var e = /^\d{10}\.\d{10}\.\d{10}\.\d+$/
      var i = this.cookieMap.qtsid
      var r = function() {
        return parseInt(new Date() / 1e3, 10).toString()
      }
      t = this.cookieGet(i)
      if (this.cookieGet(this.flag)) {
        return t
      }
      if (!e.test(t)) {
        var a = r()
        t = [a, a, a, '1']
      } else {
        t = t.split('.')
        t[1] = t[2]
        t[2] = r()
        t[3] = parseInt(t[3], 10) + 1
      }
      this.cookieSet(i, t.join('.'), 365 * 24, '/', lib.SITE_DOMAIN)
      return t
    } catch (t) {
      return ''
    }
  }
  this.getLrfr = function() {
    try {
      var t
      var e = this
      var i = this.cookieMap.lrfr
      var r = this.d.referrer.match(/http[s]?:\/\/([^\/]*)/)
      r = r ? r[1] : ''
      t = this.cookieGet(i)
      t = t == 'undefined' ? '' : t
      var a = this.l.hostname
      var n = r && r.match(/iq\.com/)
      var o = t
      if (!t) {
        if (!this.d.referrer || n) {
          o = 'DIRECT'
        } else {
          o = this.u(this.d.referrer)
        }
      } else if (!this.d.referrer) {
        o = 'DIRECT'
      } else if (r !== a && r.indexOf(lib.SITE_DOMAIN) === -1) {
        if (!n) {
          o = this.u(this.d.referrer)
        }
      }
      var s = window.intlPageInfo || {}
      if (s.i18n === 'global') {
        var l = new lib.kit.Url(document.location.href)
        if (l.getParam('frmrp') === 'embed_page') {
          let t = l.getParam('lrfr')
          t = t ? 'https://' + t : ''
          o = t || this.u(this.d.referrer) || o
        }
      }
      this.cookieSet(i, o, 0, '/', lib.SITE_DOMAIN)
      return o
    } catch (t) {
      return ''
    }
  }
  this.getFlashId = function() {
    var t = this.cookieMap.flshuid
    var e = this.cookieGet(t) || ''
    return e
  }
  this.getPcau = function() {
    var t = this.cookieMap.pcau
    var e = this.cookieGet(t) || '0'
    return e
  }
  this.setQC005 = function() {
    var t = this.cookieGet('QC005')
    var e = window.localStorage && localStorage.getItem('QC005')
    if (t && !e && window.localStorage) {
      localStorage.setItem('QC005', t)
    }
    if (!t && e) {
      this.cookieSet('QC005', e)
    }
  }
  this.getFc = function() {
    try {
      var t = this.l.search.match(/[\?&]fc=([^&]*)(&|$)/i)
      var e = this.cookieMap.QY_FC
      var i = this.cookieGet(e)
      if (i == 'b22dab601821a896') {
        return i
      }
      if (t) {
        t = t[1]
        this.cookieSet(e, t, 0, '/', lib.SITE_DOMAIN)
      } else {
        t = this.cookieGet(e)
        if (!t || t == 'undefined') {
          t = ''
        }
      }
      return t
    } catch (t) {
      return ''
    }
  }
  this.getFv = function() {
    try {
      var t = this.l.search.match(/[\?&]fv=([^&]*)(&|$)/i)
      var e = this.cookieMap.QY_FV
      if (t) {
        var i = encodeURIComponent(t[1])
        if (i.length > 146) {
          i = i.substring(0, 146)
        }
        i = decodeURIComponent(i)
        t = i
        this.cookieSet(e, t, 24 * 3, '/', lib.SITE_DOMAIN)
      } else {
        t = this.cookieGet(e)
        if (!t || t == 'undefined') {
          t = ''
        }
      }
      return t
    } catch (t) {
      return ''
    }
  }
  this.getFfc = function() {
    try {
      var t = this.l.search.match(/[\?&]ffc=([^&]*)(&|$)/i)
      var e = this.cookieMap.QY_FFC
      if (t) {
        t = t[1]
        this.cookieSet(e, t, 0, '/', lib.SITE_DOMAIN)
      } else {
        t = this.cookieGet(e)
        if (!t || t == 'undefined') {
          t = ''
        }
      }
      return t
    } catch (t) {
      return ''
    }
  }
  this.HandleEDMFcFV = function() {
    try {
      var t = this.l.search.match(/[\?&]utm_source=([^&]*)(&|$)/i)
      if (!t || !t[1] || t[1] !== 'Free_PT_EDM_EDM') {
        return
      }
      var e = this.l.search.match(/[\?&]fv=([^&]*)(&|$)/i)
      var i = this.l.search.match(/[\?&]fc=([^&]*)(&|$)/i)
      var r = this.cookieMap.EDM_FV
      var a = this.cookieMap.EDM_FC
      if (e) {
        var n = encodeURIComponent(e[1])
        if (n.length > 146) {
          n = n.substring(0, 146)
        }
        n = decodeURIComponent(n)
        e = n
        this.cookieSet(r, e, 24 * 3, '/', lib.SITE_DOMAIN)
      }
      if (i) {
        i = i[1]
        this.cookieSet(a, i, 24 * 3, '/', lib.SITE_DOMAIN)
      }
    } catch (t) {
      return ''
    }
  }
  this.getCoop = function() {
    var t = ''
    var e
    if (this.l.host.split('.')[0] == 'mini') {
      e = lib.$url(this.l.href, 'app')
      e = (e && e['app']) || ''
      if (e) {
        t = 'coop_' + e.replace('bdbrowser', 'bdexplorer')
      }
    } else if (
      this.w.INFO &&
      this.w.INFO.flashVars &&
      this.w.INFO.flashVars.coop
    ) {
      t = this.w.INFO.flashVars.coop
    }
    return t
  }
  this.getWeid = function() {
    return (
      window.webEventID ||
      lib.md5V2(+new Date() + Math.round(Math.random() * 2147483647) + '') ||
      ''
    )
  }
  this.getNu = function() {
    return this.cookieGet('QC173') || 0
  }
  this.getPRU = function() {
    return this.cookieGet('P00PRU') || ''
  }
  this.getMod = function() {
    var t = (window.Q && Q.PageInfo) || {}
    if (window.uniqy) {
      t = (window.uniqy && window.uniqy.PageInfo) || {}
    }
    var e = t.i18n !== 'tw_t'
    var i
    if (e) {
      i = 'cn_s'
    } else {
      i = 'tw_t'
    }
    var r = window.intlPageInfo || {}
    if (r.i18n === 'global') {
      var a = r.pbInfos || {}
      var n = new lib.kit.Url(document.location.href)
      i = n.getParam('mod') || a.mod || this.cookieGet('mod') || 'intl'
    }
    return i
  }
  this.post = function(t) {
    var e = this
    try {
      e.pars = []
      var i
      var r = e.filter.length
      var a
      if (r === 0) {
        for (i in e.par) {
          e.pars.push([i, e.par[i]].join('='))
        }
      } else {
        for (i = 0; i < r; i++) {
          a = e.filter[i]
          e.pars.push([a, e.par[a]].join('='))
        }
      }
      e.pars = e.pars.join('&')
      window.jsQa = new Image(1, 1)
      window.jsQa.src = (t ? e.newUrl : e.url) + e.pars
      e.cookieSet(e.flag, e.hash(e.pars), 0, '/', lib.SITE_DOMAIN)
      e.cookieSet(e.urlMap.nu, 0, 0, '/', lib.SITE_DOMAIN)
      e.callback()
    } catch (t) {
      return ''
    }
  }
  this.iframeRequest = function(t) {
    var e = document.createElement('iframe')
    e.scrolling = 'no'
    e.style.display = 'none'
    e.frameborder = 0
    e.src = t
    document.body.appendChild(e)
  }
  this.syncCookie = function(t, i, r) {
    var a = this
    var n
    if (t.indexOf('iqiyi.com') !== -1) {
      n = '//passport.pps.tv/pages/user/proxy.action'
    } else if (t.indexOf('pps.tv') !== -1) {
      n = '//passport.iqiyi.com/pages/user/proxy.action'
    }
    if (n) {
      setTimeout(function() {
        var e = n + '#' + i + '=' + r
        try {
          window.JSHandler.logToConsole('xxx')
        } catch (t) {
          if (!window.external.GetLoginJsonInfo) {
            a.iframeRequest(e)
          }
        }
      }, 0)
    }
  }
  this.queryToJson = function(t) {
    var e =
      Array.isArray ||
      function(t) {
        return Object.prototype.toString.call(t) == '[object Array]'
      }
    t = t || this.l.href
    var i = t.substr(t.lastIndexOf('?') + 1)
    var r = i.split('&')
    var a = r.length
    var n = {}
    var o = 0
    var s
    var l
    var c
    var f
    for (; o < a; o++) {
      if (!r[o]) {
        continue
      }
      f = r[o].split('=')
      s = f.shift()
      l = f.join('=')
      c = n[s]
      if (typeof c === 'undefined') {
        n[s] = l
      } else if (e(c)) {
        c.push(l)
      } else {
        n[s] = [c, l]
      }
    }
    return n
  }
  this.jsonToQuery = function(t, e) {
    var i =
      Array.isArray ||
      function(t) {
        return Object.prototype.toString.call(t) == '[object Array]'
      }
    var r = function(t, e) {
      var i
      var r
      var a
      if (typeof e === 'function') {
        for (r in t) {
          if (t.hasOwnProperty(r)) {
            a = t[r]
            i = e.call(t, a, r)
            if (i === false) {
              break
            }
          }
        }
      }
      return t
    }
    var a = function(t) {
      return String(t).replace(/[#%&+=\/\\\ \u3000\f\r\n\t]/g, function(t) {
        return (
          '%' +
          (256 + t.charCodeAt())
            .toString(16)
            .substring(1)
            .toUpperCase()
        )
      })
    }
    var n = []
    var o
    var s =
      e ||
      function(t) {
        return a(t)
      }
    r(t, function(t, e) {
      if (i(t)) {
        o = t.length
        while (o--) {
          n.push(e + '=' + s(t[o], e))
        }
      } else {
        n.push(e + '=' + s(t, e))
      }
    })
    return n.join('&')
  }
  this.getUtmPbParams = function() {
    var t = new lib.kit.Url(document.location.href)
    var e = t.getParam('utm_source')
    var i = t.getParam('utm_medium')
    var r = t.getParam('utm_campaign')
    var a = t.getParam('utm_content')
    var n = t.getParam('utm_term')
    var o = t.getParam('version')
    var s = t.getParam('is_retargeting')
    var l = e ? `utm_source=${e}&` : ''
    l += i ? `utm_medium=${i}&` : ''
    l += r ? `utm_campaign=${r}&` : ''
    l += a ? `utm_content=${a}&` : ''
    l += n ? `utm_term=${n}&` : ''
    l += o ? `version=${o}&` : ''
    l += s ? `is_retargeting=${s}&` : ''
    l = l ? l.substr(0, l.length - 1) : ''
    var c = this.cookieGet('intlPbVfm')
    if (l && !window.disSetIntlPbVfmTag) {
      this.cookieSet('intlPbVfm', l, 24 * 3, '/', lib.SITE_DOMAIN)
      window.disSetIntlPbVfmTag = 1
    } else if (!l) {
      l = c || ''
    }
    return l
  }
}
;(function() {
  var c = new lib.action.Qa()
  var f = false
  var u = null
  var t = '//msg-intl.qy.net/jpb.gif'
  var e = '//msg-intl.qy.net/act'
  var i = window.intlPageInfo || {}
  if (i.i18n === 'global') {
    t = '//msg-intl.qy.net/jpb.gif'
    e = '//msg-intl.qy.net/act'
  }
  var r = new lib.kit.Url(document.location.href)
  if (i.i18n === 'global' && r.getParam('test') === 'pcw') {
    e = '//qapb.qiyi.domain/act'
  }
  function a(t) {
    var e = document.getElementsByTagName('HEAD').item(0)
    var i = document.createElement('script')
    i.type = 'text/javascript'
    i.src = '//security.iq.com/static/intl/cook/v1/cooksdk.js'
    var r = navigator.userAgent.toLowerCase()
    var a = /ipad/i.test(r) || /iphone os/i.test(r) || /lepad_hls/i.test(r)
    e.appendChild(i)
    var n = []
    window.qaLoadingDfp = function(t) {
      n.push(t)
    }
    var o = function() {
      while (n.length > 0) {
        try {
          var t = n.shift()
          t()
        } catch (t) {}
      }
    }
    var s = function() {
      clearTimeout(u)
      u = setTimeout(function() {
        if (!f) {
          clearTimeout(u)
          f = true
          t()
          window.mainQaInstance = c
          o()
        }
      }, 3e3)
    }
    s()
    var l = /msie/.test(r)
    if (l) {
      i.onreadystatechange = function() {
        if (/loaded|complete/.test(i.readyState)) {
          f = true
          clearTimeout(u)
          t()
          window.mainQaInstance = c
          o()
        }
      }
    } else {
      i.onload = function() {
        f = true
        clearTimeout(u)
        t()
        window.mainQaInstance = c
        o()
      }
    }
  }
  try {
    if (__getLocalDfp()) {
      c.init({ url: t, newUrl: e })
      a(function() {})
      window.mainQaInstance = c
    } else {
      a(function() {
        c.init({ url: t, newUrl: e })
      })
    }
  } catch (t) {}
})()
;(function() {
  var J = navigator.userAgent.toLowerCase()
  var t = '1'
  var e = '10'
  var i = '101'
  if (/(android)|(like mac os x)/i.test(J)) {
    t = '2'
    e = '20'
  }
  if (/(android)/i.test(J)) {
    i = '201'
  } else if (/(like mac os x)/i.test(J)) {
    if (/(iphone)/i.test(J)) {
      i = '201'
    } else {
      i = '202'
    }
  }
  if (mainQaInstance) {
    i = mainQaInstance.cookieGet('p1') || i
  }
  var B = '//msg-intl.qy.net/b?t=20&p=' + e + '&p1=' + i + ('&pf=' + t)
  if (window.uniqy) {
    B = '//msg-intl.qy.net/b?t=20&p=' + e + '&p1=' + i + ('&pf=' + t)
  }
  var r = window.intlPageInfo || {}
  if (r.i18n === 'global') {
    var J = navigator.userAgent.toLowerCase()
    if (J.indexOf('electron/') > -1) {
      i = '1_11_223'
      if (J.indexOf('mac') > -1) {
        i = '1_11_222'
      }
    } else {
      i = '1_10_222'
      if (/(android)|(like mac os x)/i.test(J)) {
        i = '2_20_223'
      }
    }
    if (mainQaInstance) {
      i = mainQaInstance.cookieGet('p1') || i
    }
    B = '//msg-intl.qy.net/act?t=20&p1=' + i
    var a = new lib.kit.Url(document.location.href)
    if (a.getParam('test') === 'pcw') {
      B = '//qapb.qiyi.domain/act?t=20&p1=' + i
    }
  }
  var H = function(t, e) {
    if (t?.block === 'body') {
      return
    }
    t = t || {}
    if (e.indexOf('?') == -1) {
      e += '?'
    } else {
      e += '&'
    }
    var i = +new Date()
    t._ = i
    if (mainQaInstance) {
      var r = mainQaInstance.cookieGet('abtest')
      if (r) {
        r = JSON.parse(r)
        let e = ''
        Object.keys(r).forEach(t => {
          e += r[t] + '-'
        })
        e = e.slice(0, e.length - 1)
        if (e && e.length > 0) {
          t['abtest'] = e
        }
      }
    }
    for (var a in t) {
      if (t.hasOwnProperty(a)) {
        e += encodeURIComponent(a) + '=' + encodeURIComponent(t[a]) + '&'
      }
    }
    if (e[e.length - 1] === '&') {
      e = e.slice(0, -1)
    }
    var n = new lib.action.Qa()
    var o = n.domainName()
    if (t.rlink) {
      n.cookieSet('intl_t20_pb', e, 0, '/', o)
    }
    var s = new Image()
    s.src = e
    if (s.complete) {
      n.cookieRemove('intl_t20_pb', '/', o)
    } else {
      s.onload = function() {
        n.cookieRemove('intl_t20_pb', '/', o)
      }
      s.onerror = function() {
        n.cookieRemove('intl_t20_pb', '/', o)
      }
    }
  }
  function c(t) {
    if (typeof t === 'string') {
      t = t.split(',')
    }
    var e = []
    var i = t.length
    var r = 'block-'
    var a
    var n
    var o
    var s
    var l = function(t) {
      return document.getElementById(t)
    }
    var c = String.fromCharCode
    var f
    for (f = 0; f < i; f++) {
      a = t[f].replace(/\s+/g, '')
      o = r + a
      var u = '*[id=' + o + ']'
      var d = ''
      if (document.querySelectorAll) {
        d = document.querySelectorAll(u)
      } else {
        d = querySelectorAllIE(o)
      }
      var h = d.length
      if (h) {
        if (h > 1) {
          var v = 0
          var p = 1
          while (v < h) {
            s = d[v]
            if (v) {
              s['__bid__'] = a + p
              s['id'] = o + p
              p++
            } else {
              s['__bid__'] = a
            }
            e.push(s)
            v++
          }
        } else {
          s = l(o)
          s['__bid__'] = a
          e.push(s)
        }
      }
    }
    return e
  }
  function f() {
    var t = []
    var e = 'block-'
    var i
    var r
    var a
    var n
    var o = 'A'.charCodeAt()
    var s = function(t) {
      return document.getElementById(t)
    }
    var l = String.fromCharCode
    var c
    for (c = 0; c < 26; c++) {
      i = l(o + c)
      a = e + i
      var f = '*[id=' + a + ']'
      var u = ''
      if (document.querySelectorAll) {
        u = document.querySelectorAll(f)
      } else {
        u = querySelectorAllIE(a)
      }
      var d = u.length
      if (d) {
        if (d > 1) {
          var h = 0
          var v = 1
          while (h < d) {
            n = u[h]
            if (h) {
              n['__bid__'] = i + v
              n['id'] = a + v
              v++
            } else {
              n['__bid__'] = i
            }
            t.push(n)
            h++
          }
        } else {
          n = s(a)
          n['__bid__'] = i
          t.push(n)
        }
      }
    }
    for (c = 0; c < 26; c++) {
      r = l(o + c)
      var p = false
      for (var m = 0; m < 26; m++) {
        i = l(o + m)
        a = e + r + i
        n = s(a)
        if (n) {
          p = true
          n['__bid__'] = r + i
          t.push(n)
        }
      }
    }
    return t
  }
  function W(t) {
    var e = function(t) {
      if (
        new RegExp(
          '^[^\\x00-\\x20\\x7f\\(\\)<>@,;:\\\\\\"\\[\\]\\?=\\{\\}\\/\\u0080-\\uffff]+$'
        ).test(t)
      ) {
        var e = new RegExp('(^| )' + t + '=([^;]*)(;|$)')
        var i = e.exec(document.cookie)
        if (i) {
          return i[2] || ''
        }
      }
      return ''
    }
    try {
      t = e(t)
      if (typeof t === 'string') {
        if (t.length > 1 && t == 'deleted') {
          return ''
        } else {
          return decodeURIComponent(t) || ''
        }
      } else {
        return ''
      }
    } catch (t) {
      return ''
    }
  }
  function u() {
    var t = {}
    var e = []
    var i = 'block-'
    var r = document.getElementsByTagName('qchunk')
    var a
    var n
    for (var o = 0, s = r.length; o < s; o++) {
      a = r[o]
      n = a.getAttribute('data-id') || ''
      if (n.substr(0, i.length).toLowerCase() == i) {
        var l = n.substr(i.length)
        if (!t[l]) {
          t[l] = 1
        } else {
          var c = ++t[l]
          var f
          do {
            f = l[0]
            f += c
            c++
          } while (t[f])
          t[f] = 1
          a.setAttribute('data-id', f)
          l = f
        }
        a['__bid__'] = l
        e.push(a)
      }
    }
    return e
  }
  function Y(t, e, i) {
    if (t._clickMapPBSent) {
      return false
    }
    t._clickMapPBSent = true
    if (e === i) {
      e._c = 1
      return true
    }
    if (e._c >= 1) {
      e._c++
      return false
    } else {
      if (typeof e._c !== 'number') {
        e._c = 1
      } else {
        e._c++
      }
      if (!e._adjustClickMap) {
        var r = function() {
          this._c = 0
        }
        e._adjustClickMap = r.bind(e)
        try {
          if (e.addEventListener) {
            e.addEventListener('mousedown', e._adjustClickMap, false)
          } else {
            e.attachEvent('onmousedown', e._adjustClickMap)
          }
        } catch (t) {}
      }
    }
    return true
  }
  var l = function(t) {
    t = t || window.event
    var e = t.target || t.srcElement
    var i = t.currentTarget || this
    if (!Y(t, e, i)) {
      return
    }
    var r =
      (document.documentElement && document.documentElement.scrollTop) ||
      document.body.scrollTop
    var a =
      (document.documentElement && document.documentElement.scrollLeft) ||
      document.body.scrollLeft
    var n =
      (document.documentElement && document.documentElement.scrollWidth) ||
      document.body.scrollWidth
    var o =
      (document.documentElement && document.documentElement.scrollHeight) ||
      document.body.scrollHeight
    var s =
      (document.documentElement && document.documentElement.clientHeight) ||
      document.body.clientHeight
    var l =
      (document.documentElement && document.documentElement.clientWidth) ||
      document.body.clientWidth
    var c = Math.max(o, s)
    var f = Math.max(n, l)
    var u = this['__bid__'] || ''
    var d = z(
      document.getElementsByTagName('body')[0].getAttribute('data-pb'),
      '&'
    )
    var h = z(this.getAttribute('data-pb'), '&')
    var v
    var p
    var m
    do {
      v = e
      p = v.getAttribute('rseat')
      m = v.tagName.toUpperCase()
      e = e.parentNode
      if (v == this) {
        break
      }
    } while (!p && m !== 'A' && m !== 'IMG')
    var g = z(v.getAttribute('data-pb'), '&')
    var w
    var b
    var _
    var I
    var q = e && e.tagName && e.tagName.toUpperCase() === 'A'
    if (p) {
      I = p.match(/_AB_(.+)_TEST/)
      if (I) {
        var k = p.split(/_AB_.+_TEST/)
        p = k[0] + (k[1] || '')
        I = I[1].split('--')
        I = /(android)|(like mac os x)/i.test(J) ? I[1] : I[0]
      }
      g.rseat = p
    }
    if (m === 'A') {
      w = v.title || ''
      b = 'a'
      _ = v.getAttribute('href') || ''
    } else if (m === 'IMG') {
      w = v.alt || ''
      b = 'i'
      _ = ''
    } else {
      w = v.title || ''
      b = 'e'
      _ = ''
    }
    if (m !== 'A' && q) {
      if (e.getAttribute('href')) {
        if (e.getAttribute('href').indexOf('javascript') == -1) {
          _ =
            location.protocol +
              e.getAttribute('href').replace(/^((http|https):)/, '') || ''
        } else {
          _ = e.getAttribute('href')
        }
      }
    }
    var y = window.__global__info && window.__global__info.userInfo
    var C = y && y.hu
    C = W('hu') || C
    var P
    var S
    var E
    var A
    var M
    A = z(document.cookie, ';')
    P = A['pu'] || A['P00003'] || ''
    S = A['u'] || A['QC005'] || ''
    E = A['QC006'] || ''
    var D = A['v'] || ''
    var R = A['mkey'] || ''
    var G = C ? C : -1
    var F = A['de']
    try {
      M = JSON.parse(A['QYABEX'] || '{}')
    } catch (t) {
      M = {}
    }
    var T = (window.Q && Q.PageInfo) || {}
    if (window.uniqy) {
      T = (window.uniqy && uniqy.PageInfo) || {}
    }
    var L = T.i18n !== 'tw_t'
    var U
    if (L) {
      U = 'cn_s'
    } else {
      U = 'tw_t'
    }
    var j = {
      block: u,
      rt: b,
      r: w,
      rlink: _,
      pu: decodeURIComponent(P),
      u: decodeURIComponent(S),
      jsuid: decodeURIComponent(E),
      ce: window.webEventID || '',
      re: f + '*' + c,
      clkx: t.clientX + a,
      clky: t.clientY + r,
      mod: U,
      v: D,
      mkey: R,
      tm:
        window.__qlt && window.__qlt.statisticsStart
          ? new Date() - window.__qlt.statisticsStart
          : ''
    }
    if (I && M[I] && M[I].abtest) {
      j.abtest = M[I].abtest
    }
    var N = window.intlPageInfo || {}
    if (N.i18n === 'global') {
      var O = N.pbInfos || {}
      var x = new lib.kit.Url(document.location.href)
      if (isPWA()) j['stuptype'] = 'shortcut'
      j['lang'] = x.getParam('lang') || O.lang || ''
      j['mod'] = x.getParam('mod') || O.mod || ''
      j['rpage'] = O.rpage || ''
      j['purl'] = location.href
      j['rfr'] = document.referrer
      j['lrfr'] = A['QC007'] || ''
      if (O.pbClickParams) {
        window.pingbackParams = O.pbClickParams
      }
      var $ = new lib.action.Qa()
      var V = $.getUtmPbParams()
      j['vfm'] = V
    }
    if (window.pingbackParams) {
      if (Object.assign) {
        j = Object.assign({}, window.pingbackParams, j)
      } else {
        j = X({}, window.pingbackParams, j)
      }
    }
    if (
      window.Q &&
      Q.PageInfo &&
      Q.PageInfo.playPageInfo &&
      Q.PageInfo.playPageInfo.videoTemplate
    ) {
      j.tmplt = Q.PageInfo.playPageInfo.videoTemplate || ''
    }
    if (N.i18n === 'global') {
      if (!window.mainQaInstance) {
        return
      }
      window.mainQaInstance.getHu(function(e) {
        window.mainQaInstance.getFingerPrint(function(t) {
          j['dfp'] = t
          j['hu'] = decodeURIComponent(G) || e
          j['pu'] =
            decodeURIComponent(P) || window.mainQaInstance.getUserInfoUid()
          j['de'] = decodeURIComponent(F) || window.mainQaInstance.getQtsid()
          j['ce'] = window.mainQaInstance.getWeid()
          j['stime'] = new Date().getTime()
          j['timezone'] = new Date().toUTCString()
          H(X(j, d, h, g), B)
        })
      })
    } else {
      H(X(j, d, h, g), B)
    }
  }
  var n = function() {
    var t = f()
    var e = window.intlPageInfo || {}
    if (e.i18n === 'global') {
      var i = e.pbInfos || {}
      if (i.blockInfo) {
        t = c(i.blockInfo)
      }
    }
    var r = u()
    var a
    var n
    for (var o = 0, s = t.length; o < s; o++) {
      if (r.indexOf(t[o]) == -1) {
        r.push(t[o])
      }
    }
    var l = document.getElementsByTagName('body')[0]
    l.__bid__ = 'body'
    r.push(l)
    return r
  }
  var d = n()
  var o = function(t) {
    var e = d || []
    if (t && t.data) {
      e = t.data.down('[data-block-name]') || []
      if (e.length === 0) {
        return
      }
      var i = 'block-'
      e.forEach(function(t) {
        t['__bid__'] = t.id.substr(i.length)
      })
    }
    for (var r = 0, a = e.length; r < a; r++) {
      var n = e[r]
      var o = ''
      if (window.Q) {
        o = Q(n)
      } else if (window.jQuery) {
        o = $(n)
      }
      if (o.attr('data-asyn-pb')) {
        continue
      }
      var s = l.bind(n)
      if (n.addEventListener) {
        n.addEventListener('mousedown', s, false)
      } else {
        n.attachEvent('onmousedown', s)
      }
      o.attr('data-asyn-pb', 'true')
    }
  }
  var s = function() {
    var t = d || []
    if (t.length) {
      for (var e = 0, i = t.length; e < i; e++) {
        var r = t[e]
        if (r.getAttribute('data-asyn-pb')) {
          continue
        }
        var a = l.bind(r)
        if (r.addEventListener) {
          r.addEventListener('mousedown', a, false)
        } else {
          r.attachEvent('onmousedown', a)
        }
        r.setAttribute('data-asyn-pb', 'true')
      }
    }
  }
  var h = function(t) {
    if (t.length) {
      for (var e = 0, i = t.length; e < i; e++) {
        var r = t[e]
        if (r.getAttribute('data-asyn-pb')) {
          r.removeAttribute('data-asyn-pb')
        }
      }
    }
  }
  if (window.Q && Q.$) {
    Q.$(window).on('scroll', o)
    Q.$(window).on('resize', o)
    Q.event.customEvent.on('bindingPingback', o)
    o()
  } else if (window.jQuery) {
    try {
      $(window).on('scroll', o)
      $(window).on('resize', o)
      o()
    } catch (t) {}
  } else {
    s()
  }
  function z(t, e) {
    var i = {}
    e = e || '&'
    if (t) {
      var r = t.split(e)
      var a
      for (var n = 0, o = r.length; n < o; n++) {
        a = r[n]
        if (a) {
          a = a.split(/\s*=\s*/g)
          if (a[0]) {
            i[a[0].replace(/^\s*|\s*$/g, '')] = a[1] || ''
          }
        }
      }
    }
    return i
  }
  function X(t, e) {
    var i = t || {}
    var r
    for (var a = 1, n = arguments.length; a < n; a++) {
      r = arguments[a]
      if (r) {
        for (var o in r) {
          if (r.hasOwnProperty(o)) {
            i[o] = r[o]
          }
        }
      }
    }
    return i
  }
  lib.action.Qa.prototype.reloadClickMap = function() {
    d = n()
    this.qchunks = d
    h(d)
    s()
  }
  if (window.lib && window.lib.action && window.lib.action.Qa) {
    if (window.postMessage) {
      window.postMessage('qaLoad', location.href)
    }
  }
})()
