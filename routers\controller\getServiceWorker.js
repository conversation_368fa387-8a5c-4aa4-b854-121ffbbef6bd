// const path = require('path')
const fs = require('fs')
// const patch=require('path')
const paths = require('../../server/path')
// const isProd = process.env.BUILD_TAG === 'prod'

const getServiceWorker = async (ctx, next) => {
  try {
    const filePath = paths._nextSrc(ctx.path)
    if (!fs.existsSync(filePath)) return
    // throw new Error(
    //   'sw error:path err /path:' + filePath + '/ctx.path:' + ctx.path
    // )
    ctx.set('Cache-Control', 'no-store')
    ctx.set('content-type', 'application/javascript; charset=UTF-8') // 设置返回类型
    ctx.status = 200
    ctx.body = fs.readFileSync(filePath)
  } catch (e) {
    console.log(e)
    ctx.status = 404
    await next()
  }
}

module.exports = getServiceWorker
