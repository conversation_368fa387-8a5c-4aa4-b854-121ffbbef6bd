/* eslint-disable no-unused-vars */
/* eslint-disable no-undef */
/**
 * 频道页记载更多组件
 * PS
 */
import React from 'react'
import dynamic from 'next/dynamic'
import { connect } from 'react-redux'
import HomeWrapper from '@/style/channelStyle'
import FocusImg from '@/components/common/FocusImg'
import { getDevice, getMobileType } from '@/kit/device'
import Meta from '@/components/common/Meta'
import { renderRowContainer } from '@/utils/renderRowContainer'
import { getIp } from '@/utils/commonApi'
import {
  fetchConfigAction,
  setConfigAjaxAction
} from '@/store/reducers/commonConfig/commonConfig'
import { getCard } from '@/kit/commonConfig'
import { Loading } from '@/constants/style'
import { LoadingStyle } from '@/components/pages/home/<USER>/loadingStyle'
import { sendBlockPb, sendClickPb } from '@/utils/pingBack'
import throttle from 'lodash.throttle'
import { getWebPreData } from '@/utils/commonUtil'
import { getCookies } from '@/kit/cookie'
import { handleCommonConfigAjax } from '@/store/sagas/commonConfig/handleData'
import FooterFixed from '@/components/common/FooterFixed'
import HomeSchema from '@/components/pages/home/<USER>'
import OrganizationSchema from '@/components/pages/home/<USER>'
import BreadcrumbListSchema from '@/components/pages/home/<USER>'

const TongJiPb = dynamic(() => import('@/components/common/TongJiPb'))
const HomePopTw = dynamic(() => import('@/components/pages/home/<USER>'), {
  ssr: false
})

class Home extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      isMobile: getDevice() === 'mobile',
      deviceType: getMobileType(),
      nextPageNum: 1
    }

    this.containerRef = React.createRef()
  }

  componentDidMount() {
    const isMobile = getDevice() === 'mobile'
    console.log('首页调用数据测试')
    this.getPreData(isMobile)
    this.setState({
      isMobile
    })

    this.getCommonConfigData()

    // eslint-disable-next-line func-names
    window.addEventListener('scroll', function() {
      document.body.toggleAttribute('scroll', true)
      // eslint-disable-next-line no-unused-expressions
      this.timer && clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        document.body.toggleAttribute('scroll')
      }, 500)
    })

    window.addEventListener('resize', () => {
      const { isMobile, deviceType } = this.state
      const newIsMobile = getDevice() === 'mobile'
      const newDeviceType = getMobileType()

      if (isMobile !== newIsMobile) {
        this.setState({
          isMobile: newIsMobile
        })
      }

      if (deviceType !== newDeviceType) {
        this.setState({
          deviceType: newDeviceType
        })
      }
    })
    const langCode = this.props.langCode
    getIp()
    const mod = getCookies('mod') || 'intl'
    const deviceId = getCookies('QC005')
    // testIpV6(langCode, mod, deviceId)
    if (isMobile) {
      this.setScrollListener()
    }

    if (getDevice() === 'pc') {
      if (Notification.permission === 'default') {
        // 代表从来没被询问过
        sendBlockPb('notification_pop', {
          rpage: 'home'
        })
        Notification.requestPermission(status => {
          console.log(status) // 仅当值为 "granted" 时显示通知
          switch (status) {
            case 'granted': // 代表同意
              sendClickPb({
                rpage: 'home',
                rseat: 'pop_confirm',
                block: 'notification_pop'
              })
              break
            case 'denied': // 代表决绝
              sendClickPb({
                rpage: 'home',
                rseat: 'pop_refuse',
                block: 'notification_pop'
              })
              break
            default:
              console.log('pop_close')
              // 既没有同意也没有拒绝
              sendClickPb({
                rpage: 'home',
                rseat: 'pop_close',
                block: 'notification_pop'
              })
              break
          }
        })
      } else if (Notification.permission === 'granted') {
        sendClickPb({
          rpage: 'home',
          rseat: 'pop_confirm_auto',
          block: 'notification_pop'
        })
      }
    }
  }

  // 提前获得数据如热门推荐
  getPreData(isMobile) {
    const params = {
      rpage: 'home',
      isMobile
    }

    const webPreData = getWebPreData('home')
    const hotCard = webPreData?.hotCard
    if (hotCard) {
      const hotCardData = handleCommonConfigAjax(hotCard, 'home_web', params)
      if (hotCardData.data && hotCardData.data.isAjaxEnd) {
        hotCardData.data.isAjaxEnd = false
      }
      this.props.dispatch(setConfigAjaxAction(hotCardData))
    }
  }

  setScrollListener() {
    // mobile 下在这里请求pcw-common接口
    window.addEventListener(
      'scroll',
      throttle(
        () => {
          const { hasMore } = this.props
          if (this.containerRef && this.containerRef.current) {
            const rect = this.containerRef.current.getBoundingClientRect()
            if (rect.bottom - 50 < window.innerHeight && hasMore) {
              this.getCommonConfigData()
            }
          }
        },
        1500,
        { leading: true }
      )
    )
  }

  async getCommonConfigData() {
    const { isMobile } = this.state
    const { requestDate, dispatch } = this.props
    if (this.isFetching) {
      return
    }
    this.isFetching = true
    const {
      data: { nextUrlParams = {}, hasMore }
    } = await dispatch(
      fetchConfigAction({
        pageSt: 'home_web',
        _catch: {},
        requestDate,
        pgSize: 4
      })
    )

    this.setState({
      nextPageNum: +nextUrlParams?.pg_num
    })

    setTimeout(() => {
      this.isFetching = false
      if (nextUrlParams && nextUrlParams.pg_num && nextUrlParams.pg_num <= 3) {
        if (!isMobile && hasMore) {
          this.getCommonConfigData()
        }
      } else {
        this.setScrollListener()
      }
    }, 500)
  }

  static async getInitialProps({ ctx }) {
    const { isServer } = ctx
    const requestDate = new Date().getTime() // sid所需要的时间戳

    return { isServer, requestDate }
  }

  render() {
    const { deviceType, nextPageNum } = this.state
    const {
      langPkg,
      homeHistory,
      curWebData,
      isSeo,
      hasMore,
      pcwCommonIsAjaxEnd,
      langCode
    } = this.props
    const endFocusNum = isSeo ? 100 : global.window ? 100 : 1

    const blockInfo = 'header,focus_banner,popular,footer,popular_info'
    const historyData = homeHistory ? homeHistory.toJS() : undefined
    const trueLangPkg = langPkg.toJS()

    const trueCurWeData = curWebData && curWebData.toJS()
    const { cards = [], resources, sourceMap } = trueCurWeData

    const pcwFocusBanner = getCard('pcw_focus_banner', cards)[0] || []
    let focusImgInfo = pcwFocusBanner['blocks'] || []
    focusImgInfo = focusImgInfo.slice(0, endFocusNum)

    const rowContainerStyle = {
      position: 'relative',
      fontSize: '0',
      overflow: 'visible',
      whiteSpace: 'nowrap',
      minHeight: '60vh'
    }
    if (historyData) {
      historyData.rpage = 'home'
      historyData.rowName = trueLangPkg.continue_watching
    }

    return (
      <>
        <Meta
          desc={trueLangPkg.home_html_description}
          title={trueLangPkg.home_html_title}
          brand={trueLangPkg.brand_html}
          commonKeywords={trueLangPkg.common_keywords}
          seriesKeywords={trueLangPkg.series_keywords}
          brandKeywords={trueLangPkg.brand_keywords}
          isIndex="true"
        />
        <OrganizationSchema langCode={langCode} />
        <HomeSchema langCode={langCode} />
        <BreadcrumbListSchema
          path="/"
          langPkg={trueLangPkg}
          langCode={langCode}
        />
        <div style={{ minHeight: '100vh' }}>
          <FocusImg
            focusImgInfo={focusImgInfo}
            deviceType={deviceType}
            rpage="home"
            gtagCategory="home_TopBanner"
            isAjaxEnd={pcwCommonIsAjaxEnd}
          />
          <HomeWrapper ref={this.containerRef} style={{ minHeight: '60vh' }}>
            <div className="row-container" style={rowContainerStyle}>
              {renderRowContainer({
                rpage: 'home',
                resources,
                historyData,
                intlDataColNew: cards,
                sourceMap,
                isSeo
              })}
              <FooterFixed hasMore={hasMore} nextPageNum={nextPageNum} />
              {hasMore && pcwCommonIsAjaxEnd && (
                <LoadingStyle>
                  <Loading width="24px" />
                </LoadingStyle>
              )}
            </div>
          </HomeWrapper>
          <TongJiPb rpage="home" blockInfo={blockInfo} />
          <HomePopTw />
        </div>
      </>
    )
  }
}

const mapStateToProps = state => ({
  langPkg: state.getIn(['language', 'langPkg']),
  homeHistory: state.getIn(['home', 'homeHistory']),
  curWebData: state.getIn(['commonConfig', 'home_web']),
  hasMore: state.getIn(['commonConfig', 'home_web', 'hasMore']),
  pcwCommonIsAjaxEnd: state.getIn(['commonConfig', 'home_web', 'isAjaxEnd']),
  dispatch: state.dispatch
})
export default connect(mapStateToProps)(Home)
