import React from 'react'
import { connect } from 'react-redux'
import { VipGlobalCashAutoRenew } from '@/components/pages/vip/CashAutoRenew'
import VipBaseWrapper from '@/style/vipStyles/base'
import UserWrap from '@/components/common/Header/userWrap'
import Meta from '@/components/common/Meta'
import { rebuildCommonUrl } from '@/kit/common'
import VipRecommend from '@/components/common/Vip/VipRecommend'
import TongJiPb from '@/components/common/TongJiPb'
import { getCookies } from '@/kit/cookie'
import { sendBlockPb } from '@/utils/pingBack'

class Codekey extends React.Component {
  constructor(props) {
    super(props)
    this.showLogin = this.showLogin.bind(this)
  }

  componentDidMount() {}

  onRef = ref => {
    this.userRef = ref
  }

  showLogin() {
    // eslint-disable-next-line no-unused-expressions
    this.userRef && this.userRef.handleUserLogin()
  }

  render() {
    const {
      userInfo,
      vipList,
      langCode,
      modeCode,
      langPkg,
      vipPid,
      bossCode
    } = this.props
    const { loginTag, nickName } = userInfo.toJS()
    const realVipList = (vipList && vipList.toJS()) || []
    const trueLangPkg = langPkg.toJS()

    const loginPop = !loginTag ? (
      <div className="user-wrapper">
        <UserWrap isVipButton="true" onRef={this.onRef} />
      </div>
    ) : (
      ''
    )

    let autoRenew = <></>
    if (typeof loginTag === 'boolean') {
      if (!loginTag) {
        autoRenew = (
          <VipGlobalCashAutoRenew
            userInfoOrLoginFn={this.showLogin}
            pageInfo={{
              lang: langCode,
              mod: modeCode,
              langPkg: trueLangPkg,
              vipPid,
              bossCode
            }}
          />
        )
      } else {
        autoRenew = (
          <VipGlobalCashAutoRenew
            userInfoOrLoginFn={{ username: nickName }}
            vipList={realVipList}
            pageInfo={{
              lang: langCode,
              mod: modeCode,
              langPkg: trueLangPkg,
              qc005: getCookies('QC005'),
              sendBlockPb,
              vipPid,
              bossCode
            }}
            noInfoHref={rebuildCommonUrl(`vip/order`)}
          >
            <VipRecommend isVip rpage="vipmanage_autorenew" />
          </VipGlobalCashAutoRenew>
        )
      }
    }

    return (
      <>
        <Meta
          desc={trueLangPkg.autorenew_html_description}
          title={trueLangPkg.autorenew_html_title}
        />
        <VipBaseWrapper>
          <div className="global-cash-content">
            {/* 自动续费组件 */}
            {autoRenew}
            <div>{loginPop}</div>
            <TongJiPb rpage="vipmanage_autorenew" />
          </div>
        </VipBaseWrapper>
      </>
    )
  }
}

const mapStateToProps = state => ({
  langPkg: state.getIn(['vip', 'cashierLangPkg']),
  userInfo: state.getIn(['user', 'userInfo']),
  vipList: state.getIn(['user', 'vipList'])
})

export default connect(mapStateToProps)(Codekey)
