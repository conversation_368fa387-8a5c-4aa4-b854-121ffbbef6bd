import React, { useEffect } from 'react'
import { connect } from 'react-redux'
import { checkQipuIdStr } from '@/kit/url'
import {
  fetchVideoInfoAction,
  getPlayStatusCodeAction
} from '@/store/reducers/play/play'
import { DECODE_ID } from '@/constants/interfaces'
import { getCtxDevice, getCookies } from '@/kit/cookie'
import { getUid } from '@/utils/userInfo'
import $http from '@/kit/fetch'

import { commonDeviceIdParams } from '@/kit/common'
import MiddleContent from '@/components/pages/middle/middleContent'
import { fetchConfigAction } from '@/store/reducers/commonConfig/commonConfig'
import MiddlemMeta from '@/components/pages/middle/meta'
import TongJiPb from '@/components/common/TongJiPb'
import { getMobileType } from '@/kit/device'

const Middle = props => {
  const { albumInfo, curVideoInfo, curUrl, modeLangObj, qipuId, pid } = props
  useEffect(() => {
    props.dispatch(
      fetchConfigAction({
        pageSt: 'short_drama_share',
        channelId: 35
      })
    )
  }, [])

  const isAndroid = getMobileType() === 'android'
  let rpage = isAndroid ? 'longmiddle_adr' : 'longmiddle_ios'

  if (albumInfo.get('isShort')) {
    rpage = isAndroid ? 'shortmiddle_adr' : 'shortmiddle_ios'
  }

  return (
    <>
      <MiddlemMeta
        curUrl={curUrl}
        mod={modeLangObj.get('mod')}
        pid={pid}
        curVideoInfo={curVideoInfo}
      />
      <MiddleContent
        albumInfo={albumInfo}
        curVideoInfo={curVideoInfo}
        qipuId={qipuId}
        rpage={rpage}
      />
      <TongJiPb rpage={rpage} blockInfo="header,footer" />
    </>
  )
}

Middle.getInitialProps = async ({ ctx }) => {
  const { query, store, res } = ctx
  const { id, pid } = query
  const isMobile = getCtxDevice(ctx) === 'mobile'
  const finalId = checkQipuIdStr(id)
  const curUrl = ctx.req.url.split('?')[0]
  let qipuId = ''
  if (!finalId) {
    await store.dispatch(getPlayStatusCodeAction(404))
    res.setHeader('x-page-type', 'not-found')
  } else {
    const videoInfo = await store.dispatch(
      fetchVideoInfoAction({
        id: finalId,
        isSupportSEO: true,
        ctx,
        isMobile,
        _catch: {}
      })
    )
    if (videoInfo && typeof videoInfo === 'number') {
      res.setHeader('x-page-type', 'not-found')
    }
    if (videoInfo && videoInfo.controlStatus === 0) {
      res.setHeader('x-page-type', 'play-control')
    }
    const params = {
      ...commonDeviceIdParams(),
      langCode:
        ctx.req.headers['langCode'] ||
        ctx.req.headers['langcode'] ||
        getCookies('lang', ctx) ||
        'en_us',
      modeCode:
        ctx.req.headers['modeCode'] ||
        ctx.req.headers['modecode'] ||
        getCookies('mod', ctx) ||
        'intl',
      uid: getUid()
    }
    const options = { params }
    options.ctx = ctx
    qipuId = videoInfo.tvId
    if (!qipuId) {
      const decodeIdData = await $http(DECODE_ID + finalId, options)
      if (decodeIdData.code === '0') {
        qipuId = decodeIdData.data
      }
    }
  }
  return { finalId, curUrl, qipuId, pid }
}

const mapStateToProps = state => {
  return {
    albumInfo: state.getIn(['play', 'albumInfo']),
    // videoInfo: state.getIn(['play', 'videoInfo']),
    curVideoInfo: state.getIn(['play', 'curVideoInfo']),
    modeLangObj: state.getIn(['language', 'modeLangObj'])
  }
}

export default connect(mapStateToProps)(Middle)
