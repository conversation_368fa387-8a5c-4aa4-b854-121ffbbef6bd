/* eslint-disable */

/*
 * 说明: 此文件为从SVN (https://svn.qiyi.domain:18080/svn/RIA/RIA/projects/intlPingback/trunk)迁移而来
 * 依据之前SVN打包逻辑（copy /b  common\kit\util\jsload.js + common\kit\url.js + common\md5V2.js + common\action\qa.js + map.js qa.js）合并
 * 为方便查阅之前文件逻辑 各个文件以 start xxx | end xxx标注
 */

/* start jsload.js */
/** @ shaomin | <EMAIL>
 * @desc :
 *     Script Loader loads data as string from any domain.
 *
 *     The web service deal with request sent from Script Loader always receives
 *     a parameter named of 'requestId' with unique value to identify current
 *     reuqest. The service must send the value of 'requestId' back as the 1st
 *     piese of data
 *
 *     responsed data looks like below:
 *
 *     Responded text can be plane text or serialized xml document. And in the
 *     2nd case,  Xml-String will be convert back to xml-document automatically
 *     which will be sent to user defined listener as the 2nd argument.
 *     通过 HTML SCRIPT 标签异步加载数据，优点：跨域读取数据。
 *     通过 script 标签以 get 方式载入任何域下的数据。
 *     返回数据的格式必须按照给定的格式输出，包括一个JsLoad发送的请求 id 和返回数据
 *
 *
 * @example

         lib.kit.util.jsLoad.request(url, {
            GET :{
              param1 : myvalue1,
              param2 : myvalue2
            }
          onSuccess  : Function (Object responsedData),
          onError : Function ();
          timeout : 30000
        });

         lib.kit.util.jsLoad.request(url, {
          onSuccess  : Function (Array responsedData)
        });

         lib.kit.util.jsLoad.request(url, {});

         lib.kit.util.jsLoad.request([{ "url" : "http://.../?"}, { "url" : "http://.../?"}], {
          onSuccess  : Function (Array responsedData)
        });
 *
 *
 */
window.lib = window.lib || {}
lib.kit = lib.kit || {}
lib = lib || {}
lib.kit = lib.kit || {}
lib.kit.util = lib.kit.util || {}
lib.kit.util.jsLoad = {}
var callbackNameValue = ''

window.Q = window.Q || {}
window.huQueue = [] // vtype 接口请求队列
window.vtypeSending = false
Q.__callbacks__ = Q.__callbacks__ || {}
;(function() {
  // function parseParam(oSource, oParams) {
  //   var key
  //   try {
  //     if (typeof oParams !== 'undefined') {
  //       for (key in oSource) {
  //         if (oParams[key]) {
  //           oSource[key] = oParams[key]
  //         }
  //       }
  //     }
  //   } finally {
  //     key = null
  //     return oSource
  //   }
  // }
  // function createScripts(oOpts, oCFG) {
  //   processUrl(oOpts, oCFG)
  //   var urls = oOpts.urls
  //   var js = document.createElement('script')
  //   js.src = urls.url
  //   js.charset = urls.charset
  //   js.onload = js.onerror = js.onreadystatechange = function() {
  //     if (
  //       js &&
  //       js.readyState &&
  //       js.readyState != 'loaded' &&
  //       js.readyState != 'complete'
  //     ) {
  //       return
  //     }
  //     oCFG.script_loaded_num++
  //     // 清理script标记
  //     js.onload = js.onreadystatechange = js.onerror = null
  //     js.src = ''
  //     js.parentNode.removeChild(js)
  //     js = null
  //   }
  //   document.getElementsByTagName('head')[0].appendChild(js)
  // }

  // function processUrl(oOpts, oCFG) {
  //   var urls = oOpts.urls
  //   var key
  //   var url_cls
  //   var varname
  //   var rnd
  //   if (typeof oOpts.CACHE !== 'undefined') {
  //     var get_hash = oOpts.CACHE
  //     url_cls = urls.url
  //     for (key in get_hash) {
  //       if (key == 'varname') {
  //         varname = get_hash[key]
  //         // oCFG.script_var_arr.push(varname);
  //         continue
  //       }
  //       url_cls += get_hash[key] + '/'
  //     }
  //     urls.url = url_cls.toString()
  //     if (oOpts.spliter === '0') {
  //       urls.url = urls.url.slice(0, -1)
  //     }
  //   }
  //   if (typeof oOpts.GET !== 'undefined') {
  //     var _url = new lib.kit.Url(urls.url)
  //     var get_datas = oOpts.GET
  //     for (var key in get_datas) {
  //       if (key == 'cb') {
  //         varname = get_datas[key]
  //         // continue;
  //       }
  //       _url.setParam(key, encodeURIComponent(get_datas[key]))
  //     }
  //     urls.url = _url.toString()
  //   }
  //   if (varname) {
  //     oCFG.script_var_arr.push(varname)
  //   }
  //   urls.charset = oOpts.charset || 'utf-8'
  // }

  // function ancestor(aUrls, oOpts) {
  //   //   for(var i in oOpts['CACHE'])
  //   //     alert(i +" , "+oOpts[i]);
  //   var _opts = {
  //     urls: [],
  //     charset: 'utf-8',
  //     timeout: -1,
  //     CACHE: oOpts.CACHE,
  //     GET: oOpts.GET,
  //     spliter: '1',
  //     onSuccess() {},
  //     onError() {}
  //   }

  //   var _cfg = {
  //     script_loaded_num: 0,
  //     is_timeout: false,
  //     is_loadcomplete: false,
  //     script_var_arr: []
  //   }
  //   _opts.urls = typeof aUrls === 'string' ? { url: aUrls } : aUrls
  //   parseParam(_opts, oOpts)
  //   createScripts(_opts, _cfg)
  //   // 定时检查完成情况
  //   ;(function() {
  //     if (_opts.noreturn == true && _opts.onComplete == null) {
  //       return
  //     }
  //     var i
  //     var data = []
  //     // 全部完成
  //     if (_cfg.script_loaded_num == 1) {
  //       _cfg.is_loadcomplete = true
  //       if (_opts.onSuccess != null) {
  //         for (i = 0; i < _cfg.script_var_arr.length; i++) {
  //           var key = _cfg.script_var_arr[i].trim()
  //           var o
  //           if (key.indexOf('.') != -1) {
  //             o = eval(key)
  //           } else {
  //             o = window[key]
  //           }
  //           data.push(o)
  //         }
  //         if (_cfg.script_var_arr.length < 2) {
  //           // 兼容没有code的数据格式，没有code则认为是成功
  //           if (
  //             typeof data[0] !== 'undefined' &&
  //             (!data[0].code || data[0].code == 'A00000')
  //           )
  //             _opts.onSuccess(
  //               typeof data[0].data === 'undefined' ? data[0] : data[0].data
  //             )
  //           else {
  //             _opts.onError(data[0])
  //           }
  //         } else if (!data.code || data.code == 'A00000')
  //           _opts.onSuccess(typeof data.data === 'undefined' ? data : data.data)
  //         else {
  //           _opts.onError(data)
  //         }
  //       }
  //       return
  //     }
  //     // 达到超时
  //     if (_cfg.is_timeout == true) {
  //       return
  //     }
  //     setTimeout(arguments.callee, 50)
  //   })()

  //   // 超时处理
  //   if (_opts.timeout > 0) {
  //     setTimeout(function() {
  //       if (!_cfg.is_loadcomplete) {
  //         if (_opts.onError) {
  //           _opts.onError()
  //         }
  //         _cfg.is_timeout = true
  //       }
  //     }, _opts.timeout)
  //   }
  // }

  // lib.kit.util.jsLoad.request = function(aUrls, oOpts) {
  //   new ancestor(aUrls, oOpts)
  // }

  // 拼接http地址
  var paramFormat = function(a) {
    var s = []
    if (typeof a === 'object') {
      for (var key in a) {
        s[s.length] = encodeURIComponent(key) + '=' + encodeURIComponent(a[key])
      }
    }
    return s.join('&').replace(/%20/g, '+')
  }

  lib.kit.util.jsLoad.jsonp = function(url, params) {
    var timerId
    var scriptDom
    var jsonpUrl
    var timeout
    var ns = 'window.Q.__callbacks__.'
    callbackNameValue =
      'cb' + Math.floor(Math.random() * 2147483648).toString(36)
    var fn = function() {}
    params.timeout = params.timeout || 10000
    params.params = params.params || {}
    params.params['callback'] = ns + callbackNameValue
    var _removeScriptTag = function(scriptDom) {
      if (scriptDom.clearAttributes) {
        scriptDom.clearAttributes()
      }
      if (scriptDom && scriptDom.parentNode) {
        scriptDom.parentNode.removeChild(scriptDom)
      }
      scriptDom = null
    }
    var globalCallbacks = Q.__callbacks__
    var completeCallback = params.oncomplete || fn
    var successCallback = params.onsuccess || fn
    var failureCallback = params.onfailure || fn
    var timeoutCallback = params.onTimeout || failureCallback || fn

    globalCallbacks[callbackNameValue] = function(data) {
      if (timerId) {
        clearTimeout(timerId)
      }
      completeCallback(data)
      if (data && (data.code == 'A00000' || data.code == '0')) {
        successCallback(data)
      } else {
        failureCallback(data)
      }
      _removeScriptTag(scriptDom)
      setTimeout(function() {
        delete globalCallbacks[callbackNameValue]
      }, 6000)
    }
    jsonpUrl = url + (/\?/.test(url) ? '&' : '?') + paramFormat(params.params)
    scriptDom = document.createElement('script')
    scriptDom.type = 'text/javascript'
    if (params.charset) {
      scriptDom.charset = params.charset
    }
    scriptDom.src = jsonpUrl
    scriptDom.onerror = function() {
      failureCallback({ code: 'E00000' })
      _removeScriptTag(scriptDom)
    }
    document.getElementsByTagName('head')[0].appendChild(scriptDom)
    timerId = setTimeout(function() {
      timeoutCallback({ code: 'E00000' })
      timeout = true
      _removeScriptTag(scriptDom)
    }, params.timeout)
  }
})()
/* end jsload.js */

/**
 * 获取缓存dfp
 */
var __getLocalDfp = function() {
  var _localStorage =
    window.localStorage && window.localStorage instanceof Storage
  var __dfp = ''
  if (_localStorage) {
    __dfp = localStorage.getItem('__dfp')
  }
  if (__dfp && qa_isDpfUseful(__dfp)) {
    return __dfp.split('@')[0]
  }
  return ''
}
var qa_isDpfUseful = function(dfp) {
  if (dfp && dfp.length > 0) {
    var dfpArr = dfp.split('@')
    var st = dfpArr[2]
    var et = dfpArr[1]
    st = isNaN(st) ? 0 : Number(st)
    et = isNaN(et) ? 0 : Number(et)
    var now = new Date().getTime()
    if (now < et && now > st) {
      return true
    }
  }
  return false
}
var isPWA = function() {
  return window.matchMedia('(display-mode: standalone )').matches
}

/* start url.js */
/* author shaomin@<EMAIL>
                           用来操作各种url，比如加参数或解析参数
                        
                        */

var Url = function(url) {
  url = url || ''
  this.url = url
  this.query = {}
  this.hrParam = {}
  this.info = {}
  this.parse()
}
;(function(_) {
  _.prototype = {
    /* 解析URL，注意解析锚点必须在解析GET参数之前，以免锚点影响GET参数的解析
     * @param{String} url? 如果传入参数，则将会覆盖初始化时的传入的url串
     */
    parse: function(url) {
      if (url) {
        this.url = url
      }
      this.parseAnchor()
      this.parseParam()
      this.parseInfo()
    },
    /**
     * 解析锚点 #anchor
     */
    parseAnchor: function() {
      var anchor = this.url.match(/\#(.*)/)
      anchor = anchor ? anchor[1] : null
      this._anchor = anchor
      if (anchor != null) {
        this.anchor = this.getNameValuePair(anchor)
        this.url = this.url.replace(/\#.*/, '')
      }
    },

    /**
     * 解析GET参数 ?name=value;
     */
    parseParam: function() {
      var query = this.url.match(/\?([^\?]*)/)
      query = query ? query[1] : null
      if (query != null) {
        this.query = this.getNameValuePair(query)
        this.hrParam = this.getNameValuePair(query, '-')
        this.url = this.url.replace(/\?([^\?]*)/, '')
      }
    },
    parseInfo: function() {
      var reHost = /(\w+):\/\/([^\/:]+):?(\d*)((?:\/|$).*)/
      var parts = this.url.match(reHost)
      if (parts) {
        var protocol = parts[1]
        var host = parts[2]
        var port = parts[3]
        var path = parts[4]
        this.info = { protocol: protocol, host: host, port: port, path: path }
      }
    },
    /**
     * 目前对json格式的value 不支持
     * @param {String} str 为值对形式,其中value支持 '1,2,3'逗号分割
     * @return 返回str的分析结果对象
     */
    getNameValuePair: function(str, sep) {
      var o = {}
      var seperator = sep || '&'
      var pattern = new RegExp(
        '([^' + seperator + '=]*)(?:=([^' + seperator + ']*))?',
        'gim'
      )
      str.replace(pattern, function(w, n, v) {
        if (n == '') {
          return o
        }
        // v = v || "";//alert(v)
        // o[n] = ((/[a-z\d]+(,[a-z\d]+)*/.test(v)) || (/^[\u00ff-\ufffe,]+$/.test(v)) || v=="") ? v : (v.j2o() ? v.j2o() : v);
        o[n] = v || ''
      })
      return o
    },
    /**
     * 从 URL 中获取指定参数的值
     * @param {Object} sPara
     */
    getParam: function(sPara) {
      return this.query[sPara] || ''
    },
    // getHrParam(sPara) {
    //   return this.hrParam[sPara] || ''
    // },
    // getHostname() {
    //   return this.info.host
    // },
    /**
     * 清除URL实例的GET请求参数
     */
    // clearParam() {
    //   this.query = {}
    // },

    /**
     * 设置GET请求的参数，当个设置
     * @param {String} name 参数名
     * @param {String} value 参数值
     */
    // setParam(name, value) {
    //   if (name == null || name == '' || typeof name !== 'string') {
    //     throw new Error('no param name set')
    //   }
    //   this.query = this.query || {}
    //   this.query[name] = value
    // },

    /**
     * 设置多个参数，注意这个设置是覆盖式的，将清空设置之前的所有参数。设置之后，URL.query将指向o，而不是duplicate了o对象
     * @param {Object} o 参数对象，其属性都将成为URL实例的GET参数
     */
    // setParams(o) {
    //   this.query = o
    // },

    /**
     * 序列化一个对象为值对的形式
     * @param {Object} o 待序列化的对象，注意，只支持一级深度，多维的对象请绕过，重新实现
     * @return {String} 序列化之后的标准的值对形式的String
     */
    serialize: function(o) {
      var ar = []
      for (var i in o) {
        if (o[i] == null || o[i] == '') {
          ar.push(i + '=')
        } else {
          ar.push(i + '=' + o[i])
        }
      }
      return ar.join('&')
    }
    /**
     * 将URL对象转化成为标准的URL地址
     * @return {String} URL地址
     */
    // toString() {
    //   var queryStr = this.serialize(this.query)
    //   return (
    //     this.url +
    //     (queryStr.length > 0 ? '?' + queryStr : '') +
    //     (this.anchor ? '#' + this.serialize(this.anchor) : '')
    //   )
    // },

    /**
     * 得到anchor的串
     * @param {Boolean} forceSharp 强制带#符号
     * @return {String} 锚anchor的串
     */
    // getHashStr(forceSharp) {
    //   return this.anchor
    //     ? '#' + this.serialize(this.anchor)
    //     : forceSharp
    //     ? '#'
    //     : ''
    // }
  }
})(Url)

window.lib = window.lib || {}
lib.kit = lib.kit || {}
lib.kit.Url = Url
if (window.$reg) {
  $reg('lib.kit.Url', function() {
    lib.kit.Url = Url
  })
}
/* end url.js */

/* start md5v2.js */
/**
 * Created by siwangli on 2014/8/28.
 */
/**
 * md5编码模块,用于生成MD5值，来自于http://www.veryhuo.com/uploads/Common/js/jQuery.md5.js
 * 从lib工程中的platform/crypto/md5.js复制过来的
 */
var md5V2 = (function() {
  /**
                             * jQuery MD5 hash algorithm function
                        
                             * @alias Muhammad Hussein Fattahizadeh < muhammad [AT] semnanweb [DOT] com >
                             * @link http://www.semnanweb.com/jquery-plugin/md5.html
                             * @see http://www.webtoolkit.info/
                             * @license http://www.gnu.org/licenses/gpl.html [GNU General Public License]
                             * @param {jQuery} {md5:function(string))
                                 */
  var rotateLeft = function(lValue, iShiftBits) {
    return (lValue << iShiftBits) | (lValue >>> (32 - iShiftBits))
  }
  var addUnsigned = function(lX, lY) {
    var lX4
    var lY4
    var lX8
    var lY8
    var lResult
    lX8 = lX & 0x80000000
    lY8 = lY & 0x80000000
    lX4 = lX & 0x40000000
    lY4 = lY & 0x40000000
    lResult = (lX & 0x3fffffff) + (lY & 0x3fffffff)
    if (lX4 & lY4) {
      return lResult ^ 0x80000000 ^ lX8 ^ lY8
    }
    if (lX4 | lY4) {
      if (lResult & 0x40000000) {
        return lResult ^ 0xc0000000 ^ lX8 ^ lY8
      } else {
        return lResult ^ 0x40000000 ^ lX8 ^ lY8
      }
    } else {
      return lResult ^ lX8 ^ lY8
    }
  }
  var F = function(x, y, z) {
    return (x & y) | (~x & z)
  }
  var G = function(x, y, z) {
    return (x & z) | (y & ~z)
  }
  var H = function(x, y, z) {
    return x ^ y ^ z
  }
  var I = function(x, y, z) {
    return y ^ (x | ~z)
  }
  var FF = function(a, b, c, d, x, s, ac) {
    a = addUnsigned(a, addUnsigned(addUnsigned(F(b, c, d), x), ac))
    return addUnsigned(rotateLeft(a, s), b)
  }
  var GG = function(a, b, c, d, x, s, ac) {
    a = addUnsigned(a, addUnsigned(addUnsigned(G(b, c, d), x), ac))
    return addUnsigned(rotateLeft(a, s), b)
  }
  var HH = function(a, b, c, d, x, s, ac) {
    a = addUnsigned(a, addUnsigned(addUnsigned(H(b, c, d), x), ac))
    return addUnsigned(rotateLeft(a, s), b)
  }
  var II = function(a, b, c, d, x, s, ac) {
    a = addUnsigned(a, addUnsigned(addUnsigned(I(b, c, d), x), ac))
    return addUnsigned(rotateLeft(a, s), b)
  }
  var convertToWordArray = function(string) {
    var lWordCount
    var lMessageLength = string.length
    var lNumberOfWordsTempOne = lMessageLength + 8
    var lNumberOfWordsTempTwo =
      (lNumberOfWordsTempOne - (lNumberOfWordsTempOne % 64)) / 64
    var lNumberOfWords = (lNumberOfWordsTempTwo + 1) * 16
    var lWordArray = Array(lNumberOfWords - 1)
    var lBytePosition = 0
    var lByteCount = 0
    while (lByteCount < lMessageLength) {
      lWordCount = (lByteCount - (lByteCount % 4)) / 4
      lBytePosition = (lByteCount % 4) * 8
      lWordArray[lWordCount] =
        lWordArray[lWordCount] |
        (string.charCodeAt(lByteCount) << lBytePosition)
      lByteCount++
    }
    lWordCount = (lByteCount - (lByteCount % 4)) / 4
    lBytePosition = (lByteCount % 4) * 8
    lWordArray[lWordCount] = lWordArray[lWordCount] | (0x80 << lBytePosition)
    lWordArray[lNumberOfWords - 2] = lMessageLength << 3
    lWordArray[lNumberOfWords - 1] = lMessageLength >>> 29
    return lWordArray
  }
  var wordToHex = function(lValue) {
    var WordToHexValue = ''
    var WordToHexValueTemp = ''
    var lByte
    var lCount
    for (lCount = 0; lCount <= 3; lCount++) {
      lByte = (lValue >>> (lCount * 8)) & 255
      WordToHexValueTemp = '0' + lByte.toString(16)
      WordToHexValue += WordToHexValueTemp.substr(
        WordToHexValueTemp.length - 2,
        2
      )
    }
    return WordToHexValue
  }
  var uTF8Encode = function(string) {
    string = string.replace(/\x0d\x0a/g, '\x0a')
    var output = ''
    for (var n = 0; n < string.length; n++) {
      var c = string.charCodeAt(n)
      if (c < 128) {
        output += String.fromCharCode(c)
      } else if (c > 127 && c < 2048) {
        output += String.fromCharCode((c >> 6) | 192)
        output += String.fromCharCode((c & 63) | 128)
      } else {
        output += String.fromCharCode((c >> 12) | 224)
        output += String.fromCharCode(((c >> 6) & 63) | 128)
        output += String.fromCharCode((c & 63) | 128)
      }
    }
    return output
  }
  return function(string) {
    string += '' // 转换成字符
    var x = Array()
    var k
    var AA
    var BB
    var CC
    var DD
    var a
    var b
    var c
    var d
    var S11 = 7
    var S12 = 12
    var S13 = 17
    var S14 = 22
    var S21 = 5
    var S22 = 9
    var S23 = 14
    var S24 = 20
    var S31 = 4
    var S32 = 11
    var S33 = 16
    var S34 = 23
    var S41 = 6
    var S42 = 10
    var S43 = 15
    var S44 = 21
    string = uTF8Encode(string)
    x = convertToWordArray(string)
    a = 0x67452301
    b = 0xefcdab89
    c = 0x98badcfe
    d = 0x10325476
    for (k = 0; k < x.length; k += 16) {
      AA = a
      BB = b
      CC = c
      DD = d
      a = FF(a, b, c, d, x[k + 0], S11, 0xd76aa478)
      d = FF(d, a, b, c, x[k + 1], S12, 0xe8c7b756)
      c = FF(c, d, a, b, x[k + 2], S13, 0x242070db)
      b = FF(b, c, d, a, x[k + 3], S14, 0xc1bdceee)
      a = FF(a, b, c, d, x[k + 4], S11, 0xf57c0faf)
      d = FF(d, a, b, c, x[k + 5], S12, 0x4787c62a)
      c = FF(c, d, a, b, x[k + 6], S13, 0xa8304613)
      b = FF(b, c, d, a, x[k + 7], S14, 0xfd469501)
      a = FF(a, b, c, d, x[k + 8], S11, 0x698098d8)
      d = FF(d, a, b, c, x[k + 9], S12, 0x8b44f7af)
      c = FF(c, d, a, b, x[k + 10], S13, 0xffff5bb1)
      b = FF(b, c, d, a, x[k + 11], S14, 0x895cd7be)
      a = FF(a, b, c, d, x[k + 12], S11, 0x6b901122)
      d = FF(d, a, b, c, x[k + 13], S12, 0xfd987193)
      c = FF(c, d, a, b, x[k + 14], S13, 0xa679438e)
      b = FF(b, c, d, a, x[k + 15], S14, 0x49b40821)
      a = GG(a, b, c, d, x[k + 1], S21, 0xf61e2562)
      d = GG(d, a, b, c, x[k + 6], S22, 0xc040b340)
      c = GG(c, d, a, b, x[k + 11], S23, 0x265e5a51)
      b = GG(b, c, d, a, x[k + 0], S24, 0xe9b6c7aa)
      a = GG(a, b, c, d, x[k + 5], S21, 0xd62f105d)
      d = GG(d, a, b, c, x[k + 10], S22, 0x2441453)
      c = GG(c, d, a, b, x[k + 15], S23, 0xd8a1e681)
      b = GG(b, c, d, a, x[k + 4], S24, 0xe7d3fbc8)
      a = GG(a, b, c, d, x[k + 9], S21, 0x21e1cde6)
      d = GG(d, a, b, c, x[k + 14], S22, 0xc33707d6)
      c = GG(c, d, a, b, x[k + 3], S23, 0xf4d50d87)
      b = GG(b, c, d, a, x[k + 8], S24, 0x455a14ed)
      a = GG(a, b, c, d, x[k + 13], S21, 0xa9e3e905)
      d = GG(d, a, b, c, x[k + 2], S22, 0xfcefa3f8)
      c = GG(c, d, a, b, x[k + 7], S23, 0x676f02d9)
      b = GG(b, c, d, a, x[k + 12], S24, 0x8d2a4c8a)
      a = HH(a, b, c, d, x[k + 5], S31, 0xfffa3942)
      d = HH(d, a, b, c, x[k + 8], S32, 0x8771f681)
      c = HH(c, d, a, b, x[k + 11], S33, 0x6d9d6122)
      b = HH(b, c, d, a, x[k + 14], S34, 0xfde5380c)
      a = HH(a, b, c, d, x[k + 1], S31, 0xa4beea44)
      d = HH(d, a, b, c, x[k + 4], S32, 0x4bdecfa9)
      c = HH(c, d, a, b, x[k + 7], S33, 0xf6bb4b60)
      b = HH(b, c, d, a, x[k + 10], S34, 0xbebfbc70)
      a = HH(a, b, c, d, x[k + 13], S31, 0x289b7ec6)
      d = HH(d, a, b, c, x[k + 0], S32, 0xeaa127fa)
      c = HH(c, d, a, b, x[k + 3], S33, 0xd4ef3085)
      b = HH(b, c, d, a, x[k + 6], S34, 0x4881d05)
      a = HH(a, b, c, d, x[k + 9], S31, 0xd9d4d039)
      d = HH(d, a, b, c, x[k + 12], S32, 0xe6db99e5)
      c = HH(c, d, a, b, x[k + 15], S33, 0x1fa27cf8)
      b = HH(b, c, d, a, x[k + 2], S34, 0xc4ac5665)
      a = II(a, b, c, d, x[k + 0], S41, 0xf4292244)
      d = II(d, a, b, c, x[k + 7], S42, 0x432aff97)
      c = II(c, d, a, b, x[k + 14], S43, 0xab9423a7)
      b = II(b, c, d, a, x[k + 5], S44, 0xfc93a039)
      a = II(a, b, c, d, x[k + 12], S41, 0x655b59c3)
      d = II(d, a, b, c, x[k + 3], S42, 0x8f0ccc92)
      c = II(c, d, a, b, x[k + 10], S43, 0xffeff47d)
      b = II(b, c, d, a, x[k + 1], S44, 0x85845dd1)
      a = II(a, b, c, d, x[k + 8], S41, 0x6fa87e4f)
      d = II(d, a, b, c, x[k + 15], S42, 0xfe2ce6e0)
      c = II(c, d, a, b, x[k + 6], S43, 0xa3014314)
      b = II(b, c, d, a, x[k + 13], S44, 0x4e0811a1)
      a = II(a, b, c, d, x[k + 4], S41, 0xf7537e82)
      d = II(d, a, b, c, x[k + 11], S42, 0xbd3af235)
      c = II(c, d, a, b, x[k + 2], S43, 0x2ad7d2bb)
      b = II(b, c, d, a, x[k + 9], S44, 0xeb86d391)
      a = addUnsigned(a, AA)
      b = addUnsigned(b, BB)
      c = addUnsigned(c, CC)
      d = addUnsigned(d, DD)
    }
    var tempValue = wordToHex(a) + wordToHex(b) + wordToHex(c) + wordToHex(d)
    return tempValue.toLowerCase()
  }
})()
window.lib = window.lib || {}
lib.md5V2 = md5V2
/* end md5v2.js */

/* start qa.js */

/* global jsQa:true */
/* jshint -W059: true */
Object.extend = function(tObj, sObj) {
  for (var o in sObj) {
    tObj[o] = sObj[o]
  }
  return tObj
}
window.lib = window.lib || {}
if (!lib.SITE_DOMAIN) {
  var getDomain = function() {
    var domainLevel = 2
    var domains = window.location.hostname.split('.')
    domains = domains.slice(domains.length - domainLevel)
    return domains.join('.')
  }
  lib.SITE_DOMAIN = getDomain()
}
// 项目版本,Todo 压缩工具自动生成并替换
lib.PROJECT_VERSION = '201104221818300317'

lib.action = lib.action || {}
lib.action.Qa = function() {
  this.init = function(parm) {
    var self = this
    var isPPS = lib.SITE_DOMAIN.match(/pps/)
    try {
      var ua = navigator.userAgent.toLowerCase()
      this.par = {}
      this.pars = []
      this.custom = {}
      this.filter = []
      this.time = 0
      this.w = window
      this.l = window.location
      this.d = window.document
      this.searchJson = this.queryToJson(this.l.href)
      this.urlMap = {
        rdm: 'rdm',
        // 当前页面url
        qtcurl: 'qtcurl',
        // referral上一页面
        rfr: 'rfr',
        // Session的来源
        lrfr: 'lrfr',
        // JS cookie生成用户ID
        jsuid: 'jsuid',
        // ftime.ltime.ctime.session-counter
        qtsid: 'qtsid',
        // 注册用户ID
        ppuid: 'ppuid',
        platform: 'platform',
        weid: 'weid',
        pru: 'pru',
        flshuid: 'flshuid',
        fcode: 'fcode',
        ffcode: 'ffcode',
        coop: 'coop',
        odfrm: 'odfrm',
        fvcode: 'fvcode',
        nu: 'nu', // 新用户nu为1,老用户nu为0
        mod: 'mod',
        pcau: 'pcau' // pca检测
      }
      this.cookieMap = {
        // flash生成的唯一用户标识
        flshuid: 'QC005',
        // JS生成的唯一用户标识
        jsuid: 'QC006',
        // 后端种的家庭成员账户id
        pru: 'P00PRU',
        // Session的来源
        lrfr: 'QC007',
        // 用户的登录行为ftime.ltime.ctime.session-counter
        qtsid: 'QC008',
        // 奇悦推广码
        QY_FC: 'QC009',
        // 奇悦推广码(新)
        QY_FFC: 'QC014',
        // ga 1/2000 标识
        gaflag: 'QC011',
        // 渠道来源，若此次访问的URL中带有odfrm参数，则本次pv及后续session中的pv，及pv内的点击pingback均带有此参数
        odfrm: 'QC132',
        // 奇悦推广码
        QY_FV: 'QC142',
        EDM_FC: 'QCedm001',
        EDM_FV: 'QCedm002',
        // PCA检测
        pcau: 'PCAU'
      }
      parm = parm || {}
      this.times = parm.times || 5
      this.timeouts = parm.timeouts || 1000
      this.url = parm.url || '//msg-intl.qy.net/jspb.gif'
      this.newUrl = parm.newUrl || '//msg-intl.qy.net/act'
      // 国际站
      var intlPageInfo = window.intlPageInfo || {}
      if (intlPageInfo.i18n === 'global') {
        this.url = parm.url || '//msg-intl.qy.net/jspb.gif'
        this.newUrl = parm.newUrl || '//msg-intl.qy.net/act'
      }

      if (this.url.indexOf('?') == -1) {
        this.url += '?'
      } else if (this.url.slice(-1) != '&') {
        this.url += '&'
      }

      if (this.newUrl.indexOf('?') == -1) {
        this.newUrl += '?'
      } else if (this.newUrl.slice(-1) != '&') {
        this.newUrl += '&'
      }

      this.flag = parm.flag || 'QC010'
      this.callback = parm.callback || function() {}
      if (typeof parm.urlMap === 'object') {
        Object.extend(this.urlMap, parm.urlMap)
      }
      if (typeof parm.cookieMap === 'object') {
        Object.extend(this.cookieMap, parm.cookieMap)
      }
      if (typeof parm.custom === 'object') {
        Object.extend(this.custom, parm.custom)
      }
      if (parm.filter instanceof Array) {
        this.filter = parm.filter
      }

      var p = this.urlMap
      // 防止cache的随机串
      this.par[p.rdm] = this.rand()
      // 当前页面url
      this.par[p.qtcurl] = this.u(this.l.href)
      // referral上一页面
      this.par[p.rfr] = this.u(this.d.referrer)
      // Session的来源
      this.par[p.lrfr] = this.getLrfr()
      // JS cookie生成用户ID
      this.par[p.jsuid] = this.getJsuid()
      // ftime.ltime.ctime.session-counter
      this.par[p.qtsid] = this.getQtsid()
      // 注册用户ID
      this.par[p.ppuid] = this.getUserInfoUid()
      // new user
      this.par[p.nu] = this.getNu()
      // 平台，加入pps判断逻辑
      this.par[p.platform] = /ipad/i.test(ua)
        ? '21'
        : /(iphone os)|(android)/i.test(ua)
        ? '31'
        : '11'
      if (isPPS) {
        this.par[p.platform] = '20' + this.par[p.platform]
      }
      if (this.w.pingbackParams) {
        if (Object.assign) {
          this.par = Object.assign({}, this.w.pingbackParams, this.par)
        } else {
          var obj = Object.extend({}, this.w.pingbackParams)
          this.par = Object.extend(obj, this.par)
        }
      }
      // 播放页模板参数统计
      if (
        this.w.Q &&
        Q.PageInfo &&
        Q.PageInfo.playPageInfo &&
        Q.PageInfo.playPageInfo.videoTemplate
      ) {
        this.par['tmplt'] = Q.PageInfo.playPageInfo.videoTemplate || ''
      }
      this.par[p.fcode] = this.getFc()
      this.par[p.ffcode] = this.getFfc()
      this.par[p.coop] = this.getCoop()
      this.par[p.weid] = this.getWeid()
      this.par[p.pru] = this.getPRU()
      this.par[p.fvcode] = this.getFv()
      this.par[p.mod] = this.getMod()

      Object.extend(this.par, this.custom)

      // @note by liuyongsheng 2015-01-26
      // 获取 odfrm，写入session cookie和body元素的data-pb属性上
      var searchJson = this.searchJson
      var odfrmKey = this.cookieMap[this.urlMap.odfrm]
      var odfrm =
        searchJson[this.urlMap.odfrm] || this.cookieGet(odfrmKey) || ''
      if (odfrm) {
        odfrm = odfrm
        this.par[p.odfrm] = odfrm
        this.cookieSet(odfrmKey, odfrm, 0, '/', lib.SITE_DOMAIN)
        var body = this.d.getElementsByTagName('body')[0]
        var pbObj = this.queryToJson(body.getAttribute('data-pb') || '') || {}
        pbObj[p.odfrm] = odfrm
        var pbStr = this.jsonToQuery(pbObj)
        body.setAttribute('data-pb', pbStr)
      }
      // @note by liuyongsheng 2015-03-20 针对播放页，添加模板tmplt字段
      var playerArea = document.getElementById('block-B')
      if (playerArea && playerArea.getAttribute('data-pb')) {
        var pbData = playerArea.getAttribute('data-pb')
        var matchResult = pbData.match(/(^|&)?tmplt=([^&]+)/i)
        if (matchResult && matchResult[2]) {
          self.par['tmplt'] = matchResult[2]
        }
      }
      // 去掉5s后发送pingback逻辑，直接发送。
      var isIpad =
        /ipad/i.test(ua) || /iphone os/i.test(ua) || /lepad_hls/i.test(ua)
      if (isIpad) {
        self.par[p.flshuid] = self.getJsuid()
      } else {
        self.par[p.flshuid] = self.getFlashId()
      }
      // PCA 检测
      self.par[p.pcau] = self.getPcau()
      var _key = 'ChEnYH0415dadrrEDFf2016'
      var _jsuid = isIpad ? self.par[p.flshuid] : self.getJsuid()

      // self.par['as'] = lib.md5V2(
      //   self.par[p.platform] + _jsuid + self.par[p.weid] + _key
      // )
      // if (window.dfp) {
      //   window.dfp.getFingerPrint(
      //     function(data) {
      //       self.par['dfp'] = data
      //       self.setQC005()
      //       self.post()
      //     },
      //     function(err) {
      //       self.setQC005()
      //       self.post()
      //     }
      //   )
      // } else {
      //   self.setQC005()
      //   self.post()
      // }
      this.getFingerPrint(function(data) {
        if (data) {
          self.par['dfp'] = data
        }
        self.setQC005()
        self.post()
      })
    } catch (e) {}
    this.sendNewQa()
    var intl_t20_pb = this.cookieGet('intl_t20_pb')
    if (intl_t20_pb) {
      // 点击pingback页面跳转发送失败，这里补发
      var t20Img = new Image()
      t20Img.src = intl_t20_pb
      var domain = this.domainName()
      if (t20Img.complete) {
        self.cookieRemove('intl_t20_pb', '/', domain)
      } else {
        t20Img.onload = function() {
          self.cookieRemove('intl_t20_pb', '/', domain)
        }
        t20Img.onerror = function() {
          self.cookieRemove('intl_t20_pb', '/', domain)
        }
      }
    }
    this.HandleEDMFcFV()
  }

  /**
   * PCWDEV-2187
   * <AUTHOR>
   */
  this.sendNewQa = function() {
    var that = this

    this.getHu(function(hu) {
      that.getFingerPrint(function(dfp) {
        that.initNewPar(hu, dfp)
        that.setQC005()
        that.post(true)
      })
    })
  }

  /**
   * wiki:http://wiki.qiyi.domain/pages/viewpage.action?pageId=27694939
   * generate:http://************/qypid.html
   * PTID=[硬件形态][操作系统][客户端形态][品牌][牌照方][本地化版本][保留]
   * 硬件形态: 01(pc) / 02(phone) / 03(pad) / 00(unknown)
   * 操作系统: 01(windows desktop) / 02(android) / 03(ios) / 08(OSX)
   * 客户端形态: 002(爱奇艺web主站)
   * 品牌: 101(爱奇艺)
   * 牌照方: 00 (爱奇艺&PPS*)
   * 本地化版本: 00(大陆) / 10(台湾)
   * 保留: 000000
   * @return string
   */
  this.getPtid = function() {
    // /ipad/i.test(ua) ? '21' : ((/(iphone)|(android)/i.test(ua)) ? '31' : '11');
    var ua = navigator.userAgent.toLowerCase()
    var platform = navigator.platform.toLowerCase()
    var client = '002'
    var brand = '101'
    var license = '00'
    var localCode = '00'
    var left = '000000'

    var hard = '01'
    if (/(iphone)|(android)/i.test(ua)) {
      hard = '02'
    } else if (/pad/i.test(ua)) {
      hard = '03'
    }

    var osCode = '01'
    if (/android/i.test(platform)) {
      osCode = '02'
    }
    if (/mac/i.test(platform)) {
      osCode = '08'
    }
    if (/ios/i.test(platform)) {
      osCode = '03'
    }

    return hard + osCode + client + brand + license + localCode + left
  }
  this.extend = function(target, src) {
    var ret = target || {}
    var b
    for (var i = 1, len = arguments.length; i < len; i++) {
      b = arguments[i]
      if (b) {
        for (var p in b) {
          if (b.hasOwnProperty(p)) {
            ret[p] = b[p]
          }
        }
      }
    }
    return ret
  }
  /**
   * 初始化newpar的值
   * <AUTHOR>
   */
  this.initNewPar = function(hu, dfp) {
    var playPageInfo
    var p1 = '1_10_101'
    // 国际站
    var intlPageInfo = window.intlPageInfo || {}
    if (intlPageInfo.i18n === 'global') {
      var ua = navigator.userAgent.toLowerCase()

      if (ua.indexOf('electron/') > -1) {
        p1 = '1_11_223'
        if (ua.indexOf('mac') > -1) {
          p1 = '1_11_222'
        }
      } else {
        p1 = '1_10_222'
        if (/(android)|(like mac os x)/i.test(ua)) {
          p1 = '2_20_223'
        }
      }

      // p1 = '1_10_222'
      // var ua = navigator.userAgent.toLowerCase()
      // if (/(android)|(like mac os x)/i.test(ua)) {
      //   p1 = '2_20_223'
      // }
    }

    // 页面也调用hu方法，调用逻辑太绕，这里取hu的时候加一层兼容逻辑
    var userInfoStorage =
      window.__global__info && window.__global__info.userInfo
    var userHu = userInfoStorage && userInfoStorage.hu
    userHu = this.cookieGet('hu') || userHu

    this.par = {
      u: this.cookieGet('u') || this.getFlashId(),
      pu: this.cookieGet('pu') || this.getUserInfoUid(),
      rn: this.rand(),
      p1: this.cookieGet('p1') || p1,
      mkey: this.cookieGet('mkey') || '',
      de: this.cookieGet('de') || this.getQtsid(),
      stime: new Date().getTime(),
      ce: this.getWeid(),
      bstp: 0,
      t: 22,
      v: 1,
      rpage: '',
      hu: userHu ? userHu : hu,
      dfp: dfp,
      mod: this.getMod(),
      purl: this.u(this.l.href),
      nu: this.getNu(),
      vfm: this.searchJson.vfm || '',
      rfr: this.u(this.d.referrer),
      pcau: this.getPcau(),
      ptp: '',
      pagev: 'homepage_adv_v1',
      coop: this.getCoop(),
      lrfr: this.getLrfr(),
      ptid: this.getPtid()
    }
    if (intlPageInfo.i18n === 'global') {
      delete this.par['ptid']
      // delete this.par['v']
      this.par['v'] = this.cookieGet('v') || ''
      if (isPWA()) this.par['stuptype'] = 'shortcut'
      this.par['timezone'] = new Date().toUTCString()
      var url = new lib.kit.Url(document.location.href)
      var pbInfos = intlPageInfo.pbInfos || {}
      this.par['rpage'] = pbInfos.rpage || ''
      this.par['lang'] =
        url.getParam('lang') ||
        pbInfos.lang ||
        this.cookieGet('lang') ||
        'en_us'
      this.par['re'] = window.screen.width + '*' + window.screen.height
      this.par['s2'] = url.getParam('frmrp') || ''
      this.par['s3'] = url.getParam('frmb') || ''
      if (pbInfos.pbShowParams) {
        if (Object.assign) {
          this.par = Object.assign({}, this.par, pbInfos.pbShowParams)
        } else {
          this.par = this.extend({}, this.par, pbInfos.pbShowParams)
        }
      }
      var vfm22 = this.getUtmPbParams()
      this.par['vfm'] = encodeURIComponent(vfm22)
    }

    var b_ext_ip = this.cookieGet('b_ext_ip') || ''
    if (b_ext_ip) {
      this.par['b_ext'] = JSON.stringify({ bip: b_ext_ip })
    }

    playPageInfo =
      this.w.Q && this.w.Q.PageInfo && this.w.Q.PageInfo.playPageInfo
    if (playPageInfo) {
      if (playPageInfo.videoTemplate) {
        this.par.tmplt = playPageInfo.videoTemplate || ''
      }
      if (playPageInfo.cid !== undefined) {
        this.par.c1 = playPageInfo.cid
      }
      if (playPageInfo.albumId) {
        this.par.aid = playPageInfo.albumId
      }
    }

    if (this.w.pingbackParams) {
      if (Object.assign) {
        this.par = Object.assign({}, this.par, this.w.pingbackParams)
      } else {
        var obj = Object.extend({}, this.par)
        this.par = Object.extend(obj, this.w.pingbackParams)
      }
    }
  }
  var noDfpTag = 1
  this.getFingerPrint = function(cb) {
    noDfpTag = 0
    var locDfp = __getLocalDfp()
    if (locDfp) {
      cb(locDfp)
    } else if (window.dfp) {
      window.dfp.getFingerPrint(
        function(dfp) {
          cb(dfp)
        },
        function(err) {
          cb()
        }
      )
    } else {
      setTimeout(function() {
        if (noDfpTag) {
          cb()
        }
      }, 10000) // 临时策略加10s判断
    }
  }

  /**
   * 会员类型
   * http://wiki.qiyi.domain/pages/viewpage.action?pageId=318540396
   * @return {Number}
   */
  this.getHu = function(cb) {
    var userInfoStorage =
      window.__global__info && window.__global__info.userInfo
    if (userInfoStorage && userInfoStorage.hu) {
      cb(userInfoStorage.hu)
    } else {
      this.getUser(cb)
    }
  }
  this.getUser = function(cb) {
    var isLogin = this.cookieGet('I00019') || ''
    if (!isLogin) {
      cb(-1)
      return
    }
    window.huQueue.push(cb)
    // 如果当前页面正在请求vtype接口就加入队列不在进行请求
    if (window.vtypeSending) {
      return
    }
    window.vtypeSending = true
    var ua = navigator.userAgent.toLowerCase()
    var platformId = 3
    if (
      /(android)|(like mac os x)/i.test(ua) ||
      (/(intel mac os x)/i.test(ua) && 'ontouchend' in document)
    ) {
      platformId = 4
    }
    var devId = this.cookieGet('QC005') || ''
    var mod = this.cookieGet('mod') || 'intl'
    var lang = this.cookieGet('lang') || 'en_us'
    // var pvpUrl = '//pcw-api.iq.com/api/pvp'
    // if (window.location.hostname.indexOf('.iqiyi.com') !== -1) {
    //   pvpUrl = '//intl-pcw.iqiyi.com/api/pvp'
    // }
    // var url =
    //   pvpUrl +
    //   '?fields=userinfo&deviceId=' +
    //   devId +
    //   '&modeCode=' +
    //   mod +
    //   '&langCode=' +
    //   lang +
    //   '&platformId=' +
    //   platformId
    /**
     * http://pms.qiyi.domain/browse/GLOBALLINEDEV-5311
     * 需要获取用户所有会员类型，故改为调用vtype接口
     */
    var vtypeUrl = '//pcw-api.iq.com/api/vtype'
    var url =
      vtypeUrl +
      '?batch=1&deviceId=' +
      devId +
      '&modeCode=' +
      mod +
      '&langCode=' +
      lang +
      '&platformId=' +
      platformId +
      '&vipInfoVersion=5.0'

    var options = {
      // GET: {
      //     callback: 'userinfodetailCallback'
      // },
      onsuccess: function(data) {
        var hu = -1
        var vTypes = []
        window.__global__info = window.__global__info || {}
        if (data.data && data.data.all_vip && data.data.all_vip.length) {
          var allvip = data.data.all_vip
          window.__allhu__ = allvip
          for (var i = 0; i < allvip.length; i++) {
            var vipInfo = allvip[i]
            if (vipInfo.status == '1') {
              //status==1为生效状态
              vTypes.push(vipInfo.vipType)
            }
          }
          window.__global__info.userInfo = allvip[0]
          //啥会员都不是就传入-1（非会员）
          hu = vTypes.length ? vTypes.join(',') : -1
          window.__global__info.userInfo.hu = hu
        } else {
          window.__global__info.userInfo = window.__global__info.userInfo || {}
          window.__global__info.userInfo.hu = hu
        }
        window.__hu__ = hu
        window.vtypeSending = false
        for (var i = 0; i < window.huQueue.length; i++) {
          if (window.huQueue[i]) {
            window.huQueue[i](hu)
          }
        }
        // cb(hu)
      },
      onfailure: function() {
        window.vtypeSending = false
        for (var i = 0; i < window.huQueue.length; i++) {
          if (window.huQueue[i]) {
            window.huQueue[i](-1)
          }
        }
        // cb(-1)
      }
    }

    lib.kit.util.jsLoad.jsonp(url, options)
  }

  this.getUserInfoUid = function() {
    try {
      var userInfoStorage =
        window.__global__info && window.__global__info.userInfo
      var uid = userInfoStorage && userInfoStorage.uid
      if (uid) {
        return uid
      } else {
        // I00002可以拿cookie值了，故无需接口直接取
        var userInfo = this.cookieGet('I00002')
        if (userInfo) {
          userInfo =
            userInfo == window.JSON
              ? window.JSON.parse(userInfo)
              : eval('(' + userInfo + ')')
        }
        if (userInfo && userInfo.data) {
          window.__global__info = window.__global__info || {}
          window.__global__info.userInfo = window.__global__info.userInfo || {}
          window.__global__info.userInfo.uid = userInfo.data.uid || ''
          return userInfo.data.uid || ''
        } else {
          return ''
        }
      }
      // if (
      //   userInfoStorage &&
      //   userInfoStorage.data &&
      //   userInfoStorage.code == '0'
      // ) {
      //   var userInfo = userInfoStorage.data.userinfo || {}
      //   uid = userInfo.uid || ''
      // }
      // return uid || ''
    } catch (e) {
      return ''
    }
  }
  this.u = function(str) {
    try {
      var f = encodeURIComponent
      return f instanceof Function ? f(str) : escape(str)
    } catch (e) {
      return ''
    }
  }
  this.hash = function(str) {
    try {
      var n = 1
      var f = 0
      if (str) {
        n = 0
        for (var l = str.length - 1; l >= 0; l--) {
          f = str.charCodeAt(l)
          n = ((n << 6) & 268435455) + f + (f << 14)
          f = n & 266338304
          n = f !== 0 ? n ^ (f >> 21) : n
        }
      }
      return n
    } catch (e) {
      return ''
    }
  }
  this.rand = function(l) {
    try {
      var str = []
      if (!isNaN(l)) {
        for (var i = 0; i < l; i++) {
          str.push(Math.round(Math.random() * 2147483647).toString(36))
        }
      } else {
        str.push(Math.round(Math.random() * 2147483647))
      }
      return str.join('')
    } catch (e) {
      return ''
    }
  }
  this.cookieGet = function(name) {
    var getRaw = function(key) {
      if (
        new RegExp(
          '^[^\\x00-\\x20\\x7f\\(\\)<>@,;:\\\\\\"\\[\\]\\?=\\{\\}\\/\\u0080-\\uffff]+\x24'
        ).test(key)
      ) {
        var reg = new RegExp('(^| )' + key + '=([^;]*)(;|\x24)')
        var result = reg.exec(document.cookie)
        if (result) {
          return result[2] || ''
        }
      }
      return ''
    }
    try {
      name = getRaw(name)
      if (typeof name === 'string') {
        // 解决多核切换的浏览器cookies读取问题
        if (name.length > 1 && name == 'deleted') {
          return ''
        } else {
          return decodeURIComponent(name) || ''
        }
      } else {
        return ''
      }
    } catch (e) {
      return ''
    }
  }
  this.cookieSet = function(name, value, expire, path, domain, secure) {
    try {
      if (window.funcCookieDisable) {
        if (
          name === 'QC173' ||
          name === 'QC010' ||
          name === 'QC008' ||
          name === 'QC007' ||
          name === 'QC006'
        ) {
          return
        }
      }
      var cstr = []
      cstr.push(name + '=' + encodeURIComponent(value))
      if (expire) {
        var dd = new Date()
        var expires = dd.getTime() + expire * 3600000
        dd.setTime(expires)
        cstr.push('expires=' + dd.toGMTString())
      }
      if (path) {
        cstr.push('path=' + path)
      }
      if (domain) {
        cstr.push('domain=' + domain)
      }
      if (secure) {
        cstr.push(secure)
      }
      document.cookie = cstr.join(';')
    } catch (e) {
      return ''
    }
  }
  this.domainName = function() {
    var host = window.location.hostname
    host = host.split(':')[0]
    var domain = host
      .split('.')
      .splice(1)
      .join('.')
    return domain
  }
  this.cookieRemove = function(name, path, domain, secure) {
    try {
      document.cookie =
        name +
        '=;' +
        'expires=Fri, 31 Dec 1999 23:59:59 GMT;' +
        'path=' +
        (path || '/') +
        ';' +
        'domain=' +
        domain
    } catch (e) {
      return ''
    }
  }
  this.getJsuid = function() {
    try {
      var key
      var jsUid = this.cookieMap.jsuid
      key = this.cookieGet(jsUid)
      if (!key || !isNaN(key)) {
        key = this.rand(4)
      }
      this.cookieSet(jsUid, key, 365 * 24, '/', lib.SITE_DOMAIN)
      return key
    } catch (e) {
      return ''
    }
  }
  this.getQtsid = function() {
    try {
      var key
      var reg = /^\d{10}\.\d{10}\.\d{10}\.\d+$/
      var qtSid = this.cookieMap.qtsid
      var timestamp = function() {
        return parseInt(new Date() / 1000, 10).toString()
      }
      key = this.cookieGet(qtSid)
      if (this.cookieGet(this.flag)) {
        return key
      }
      // 第一次访问时间.上一次访问时间.此次访问时间.访问次数
      if (!reg.test(key)) {
        var ts = timestamp()
        key = [ts, ts, ts, '1']
      } else {
        key = key.split('.')
        key[1] = key[2]
        key[2] = timestamp()
        key[3] = parseInt(key[3], 10) + 1
      }
      this.cookieSet(qtSid, key.join('.'), 365 * 24, '/', lib.SITE_DOMAIN)
      return key
    } catch (e) {
      return ''
    }
  }
  this.getLrfr = function() {
    try {
      var key
      var _this = this
      var lrfr = this.cookieMap.lrfr
      var refDomain = this.d.referrer.match(/http[s]?:\/\/([^\/]*)/)
      refDomain = refDomain ? refDomain[1] : ''
      key = this.cookieGet(lrfr)
      key = key == 'undefined' ? '' : key
      var locDomain = this.l.hostname
      // pps、iqiyi互相跳转的场景
      var fromBrother = refDomain && refDomain.match(/iq\.com/)
      var newKey = key
      if (!key) {
        // 第一次到访
        if (!this.d.referrer || fromBrother) {
          newKey = 'DIRECT'
        } else {
          // 从非pps.tv第一次跳到iqiyi.com，或者从非iqiyi.com第一次跳到pps.tv
          newKey = this.u(this.d.referrer)
        }
      } else if (!this.d.referrer) {
        // 直接打开新窗口访问
        newKey = 'DIRECT'
      } else if (
        refDomain !== locDomain &&
        refDomain.indexOf(lib.SITE_DOMAIN) === -1
      ) {
        // changelog 2014-08-12 从 pps跳到iqiyi时，保留pps落地页的referer
        if (!fromBrother) {
          newKey = this.u(this.d.referrer)
        }
      }
      // 国际站
      var intlPageInfo = window.intlPageInfo || {}
      if (intlPageInfo.i18n === 'global') {
        // GLOBALREQ-1507 lrfr优选取utm_source的值
        var url = new lib.kit.Url(document.location.href)
        /**
         * http://pms.qiyi.domain/browse/GLOBALREQ-1507
         * lrfr不取utm_source的值，故去掉
         * */

        // newKey = url.getParam('utm_source') || newKey

        // GLOBALLINEDEV-3064 当url带参数frmrp=embed_page时，lrfr优选取referrer的值
        // GLOBALREQ-4879:站外播放器页面存储lrfr到url的lrfr中，当url带参数frmrp=embed_page时优先获取
        if (url.getParam('frmrp') === 'embed_page') {
          let _lrfr = url.getParam('lrfr')
          _lrfr = _lrfr ? 'https://' + _lrfr : ''
          newKey = _lrfr || this.u(this.d.referrer) || newKey
        }
      }
      this.cookieSet(lrfr, newKey, 0, '/', lib.SITE_DOMAIN)
      // PCWDEV-1921 由于https://passport.iqiyi.com/pages/user/proxy.action接口取消，以下（QC007，登录来源统计）代码作注释处理
      // if(key != newKey){
      //     //session来源变了，同步cookie到另一个域下
      //     this.syncCookie(locDomain, lrfr, newKey);
      // }
      return newKey
    } catch (e) {
      return ''
    }
  }
  this.getFlashId = function() {
    var flshuid = this.cookieMap.flshuid
    var key = this.cookieGet(flshuid) || ''
    return key
  }
  this.getPcau = function() {
    var pcau = this.cookieMap.pcau
    var key = this.cookieGet(pcau) || '0'
    return key
  }
  this.setQC005 = function() {
    var cookieQC005 = this.cookieGet('QC005')
    var localStorageQC005 = window.localStorage && localStorage.getItem('QC005')
    if (cookieQC005 && !localStorageQC005 && window.localStorage) {
      localStorage.setItem('QC005', cookieQC005)
    }
    if (!cookieQC005 && localStorageQC005) {
      this.cookieSet('QC005', localStorageQC005)
    }
  }
  this.getFc = function() {
    try {
      var str = this.l.search.match(/[\?&]fc=([^&]*)(&|$)/i)
      var fc = this.cookieMap.QY_FC

      var dellExt = this.cookieGet(fc)
      if (dellExt == 'b22dab601821a896') {
        return dellExt
      }

      if (str) {
        str = str[1]
        this.cookieSet(fc, str, 0, '/', lib.SITE_DOMAIN)
      } else {
        str = this.cookieGet(fc)
        if (!str || str == 'undefined') {
          str = ''
        }
      }
      return str
    } catch (e) {
      return ''
    }
  }
  this.getFv = function() {
    try {
      var str = this.l.search.match(/[\?&]fv=([^&]*)(&|$)/i)
      var fv = this.cookieMap.QY_FV

      if (str) {
        // FV二级、三级渠道由站外写入，可能存在特殊字符，遇特殊字符需前端做转码、解码处理。
        // 具体做法是：截取<=146位FV,写入cookie前做转码处理。
        var strVal = encodeURIComponent(str[1])
        if (strVal.length > 146) {
          strVal = strVal.substring(0, 146)
        }
        strVal = decodeURIComponent(strVal)
        str = strVal
        this.cookieSet(fv, str, 24 * 3, '/', lib.SITE_DOMAIN)
      } else {
        str = this.cookieGet(fv)
        if (!str || str == 'undefined') {
          str = ''
        }
      }
      return str
    } catch (e) {
      return ''
    }
  }
  this.getFfc = function() {
    try {
      var str = this.l.search.match(/[\?&]ffc=([^&]*)(&|$)/i)
      var ffc = this.cookieMap.QY_FFC
      if (str) {
        str = str[1]
        this.cookieSet(ffc, str, 0, '/', lib.SITE_DOMAIN)
      } else {
        str = this.cookieGet(ffc)
        if (!str || str == 'undefined') {
          str = ''
        }
      }
      return str
    } catch (e) {
      return ''
    }
  }

  this.HandleEDMFcFV = function() {
    try {
      // 只有在edm中过来的链接才进行写入写出操作
      var utmSource = this.l.search.match(/[\?&]utm_source=([^&]*)(&|$)/i)
      if (!utmSource || !utmSource[1] || utmSource[1] !== 'Free_PT_EDM_EDM') {
        return
      }
      var str = this.l.search.match(/[\?&]fv=([^&]*)(&|$)/i)
      var strfc = this.l.search.match(/[\?&]fc=([^&]*)(&|$)/i)
      var edmfv = this.cookieMap.EDM_FV
      var edmfc = this.cookieMap.EDM_FC
      if (str) {
        // FV二级、三级渠道由站外写入，可能存在特殊字符，遇特殊字符需前端做转码、解码处理。
        // 具体做法是：截取<=146位FV,写入cookie前做转码处理。
        var strVal = encodeURIComponent(str[1])
        if (strVal.length > 146) {
          strVal = strVal.substring(0, 146)
        }
        strVal = decodeURIComponent(strVal)
        str = strVal
        this.cookieSet(edmfv, str, 24 * 3, '/', lib.SITE_DOMAIN)
      }
      if (strfc) {
        strfc = strfc[1]
        this.cookieSet(edmfc, strfc, 24 * 3, '/', lib.SITE_DOMAIN)
      }
    } catch (e) {
      return ''
    }
  }
  this.getCoop = function() {
    var coop = ''
    var app
    if (this.l.host.split('.')[0] == 'mini') {
      app = lib.$url(this.l.href, 'app')
      app = (app && app['app']) || ''
      if (app) {
        coop = 'coop_' + app.replace('bdbrowser', 'bdexplorer')
      }
    } else if (
      this.w.INFO &&
      this.w.INFO.flashVars &&
      this.w.INFO.flashVars.coop
    ) {
      coop = this.w.INFO.flashVars.coop
    }
    return coop
  }
  this.getWeid = function() {
    return (
      window.webEventID ||
      lib.md5V2(+new Date() + Math.round(Math.random() * 2147483647) + '') ||
      ''
    )
  }
  // 获取新用户标识
  this.getNu = function() {
    return this.cookieGet('QC173') || 0
  }
  this.getPRU = function() {
    return this.cookieGet('P00PRU') || ''
  }
  this.getMod = function() {
    var pageInfo = (window.Q && Q.PageInfo) || {}
    // 台湾新版页面没有Q，挂到uniqy下面
    if (window.uniqy) {
      pageInfo = (window.uniqy && window.uniqy.PageInfo) || {}
    }
    var i18nType = pageInfo.i18n !== 'tw_t'
    var modType
    if (i18nType) {
      modType = 'cn_s' // 大陆简体
    } else {
      modType = 'tw_t' // 台湾繁体
    }
    var intlPageInfo = window.intlPageInfo || {}
    if (intlPageInfo.i18n === 'global') {
      var pbInfos = intlPageInfo.pbInfos || {}
      var url = new lib.kit.Url(document.location.href)
      modType =
        url.getParam('mod') || pbInfos.mod || this.cookieGet('mod') || 'intl'
    }
    return modType
  }
  this.post = function(isNew) {
    var self = this
    try {
      self.pars = []
      var i
      var l = self.filter.length
      var p
      if (l === 0) {
        for (i in self.par) {
          self.pars.push([i, self.par[i]].join('='))
        }
      } else {
        for (i = 0; i < l; i++) {
          p = self.filter[i]
          self.pars.push([p, self.par[p]].join('='))
        }
      }
      self.pars = self.pars.join('&')
      window.jsQa = new Image(1, 1)
      window.jsQa.src = (isNew ? self.newUrl : self.url) + self.pars
      self.cookieSet(self.flag, self.hash(self.pars), 0, '/', lib.SITE_DOMAIN)
      self.cookieSet(self.urlMap.nu, 0, 0, '/', lib.SITE_DOMAIN)
      self.callback()
    } catch (e) {
      return ''
    }
  }
  this.iframeRequest = function(src) {
    var ifr = document.createElement('iframe')
    ifr.scrolling = 'no'
    ifr.style.display = 'none'
    ifr.frameborder = 0
    ifr.src = src
    document.body.appendChild(ifr)
  }
  this.syncCookie = function(locDomain, key, val) {
    var _this = this
    var proxyUrl
    if (locDomain.indexOf('iqiyi.com') !== -1) {
      proxyUrl = '//passport.pps.tv/pages/user/proxy.action'
    } else if (locDomain.indexOf('pps.tv') !== -1) {
      proxyUrl = '//passport.iqiyi.com/pages/user/proxy.action'
    }
    if (proxyUrl) {
      setTimeout(function() {
        var src = proxyUrl + '#' + key + '=' + val
        try {
          /*
                                            @changelog 2014-08-28 为mac客户端打的hack， 新版mac客户端发版之后，删去hack代码
                                            @bug描述：发送iframe请求时，mac客户端会在浏览器新窗口中打开src链接
                                            */
          window.JSHandler.logToConsole('xxx')
        } catch (e) {
          /* 图文端搜索结果页面无需执行此方法,否则会在浏览器新窗口中打开src链接,window.external.GetLoginJsonInfo()图文搜索结果独有方法  */
          if (!window.external.GetLoginJsonInfo) {
            _this.iframeRequest(src)
          }
        }
      }, 0)
    }
  }
  // 下面的queryToJson和jsonToQuery都是从Q里面copy过来的
  this.queryToJson = function(url) {
    var _isArray =
      Array.isArray ||
      function(arg) {
        return Object.prototype.toString.call(arg) == '[object Array]'
      }
    url = url || this.l.href
    var query = url.substr(url.lastIndexOf('?') + 1)
    var params = query.split('&')
    var len = params.length
    var result = {}
    var i = 0
    var key
    var value
    var item
    var param

    for (; i < len; i++) {
      if (!params[i]) {
        continue
      }
      param = params[i].split('=')
      key = param.shift()
      value = param.join('=')

      item = result[key]
      if (typeof item === 'undefined') {
        result[key] = value
      } else if (_isArray(item)) {
        item.push(value)
      } else {
        result[key] = [item, value]
      }
    }
    return result
  }
  this.jsonToQuery = function(json, replacer_opt) {
    var _isArray =
      Array.isArray ||
      function(arg) {
        return Object.prototype.toString.call(arg) == '[object Array]'
      }
    var _forEach = function(source, iterator) {
      var returnValue
      var key
      var item
      if (typeof iterator === 'function') {
        for (key in source) {
          if (source.hasOwnProperty(key)) {
            item = source[key]
            returnValue = iterator.call(source, item, key)

            if (returnValue === false) {
              break
            }
          }
        }
      }
      return source
    }
    var _escapeSymbol = function(source) {
      return String(source).replace(/[#%&+=\/\\\ \u3000\f\r\n\t]/g, function(
        all
      ) {
        return (
          '%' +
          (0x100 + all.charCodeAt())
            .toString(16)
            .substring(1)
            .toUpperCase()
        )
      })
    }
    var result = []
    var itemLen
    var replacer =
      replacer_opt ||
      function(value) {
        return _escapeSymbol(value)
      }

    _forEach(json, function(item, key) {
      if (_isArray(item)) {
        itemLen = item.length
        while (itemLen--) {
          result.push(key + '=' + replacer(item[itemLen], key))
        }
      } else {
        result.push(key + '=' + replacer(item, key))
      }
    })

    return result.join('&')
  }
  this.getUtmPbParams = function() {
    var url = new lib.kit.Url(document.location.href)
    var utmSource = url.getParam('utm_source')
    var utmMedium = url.getParam('utm_medium')
    var utmCampaign = url.getParam('utm_campaign')
    var utmContent = url.getParam('utm_content')
    var utmTerm = url.getParam('utm_term')
    var version = url.getParam('version')
    var isRetargeting = url.getParam('is_retargeting')
    var vfm = utmSource ? `utm_source=${utmSource}&` : ''
    vfm += utmMedium ? `utm_medium=${utmMedium}&` : ''
    vfm += utmCampaign ? `utm_campaign=${utmCampaign}&` : ''
    vfm += utmContent ? `utm_content=${utmContent}&` : ''
    vfm += utmTerm ? `utm_term=${utmTerm}&` : ''
    vfm += version ? `version=${version}&` : ''
    vfm += isRetargeting ? `is_retargeting=${isRetargeting}&` : ''

    vfm = vfm ? vfm.substr(0, vfm.length - 1) : ''
    var intlPbVfm = this.cookieGet('intlPbVfm')
    if (vfm && !window.disSetIntlPbVfmTag) {
      this.cookieSet('intlPbVfm', vfm, 24 * 3, '/', lib.SITE_DOMAIN)
      window.disSetIntlPbVfmTag = 1
    } else if (!vfm) {
      vfm = intlPbVfm || ''
    }
    return vfm
  }
}
;(function() {
  var mainQa = new lib.action.Qa()
  var loaded = false
  var timer = null // 加一个定时器，判断cooksdk.js是否加载成功
  var oldUrl = '//msg-intl.qy.net/jpb.gif'
  var newUrl = '//msg-intl.qy.net/act'

  // 国际站
  var intlPageInfo = window.intlPageInfo || {}
  if (intlPageInfo.i18n === 'global') {
    oldUrl = '//msg-intl.qy.net/jpb.gif'
    newUrl = '//msg-intl.qy.net/act'
  }

  function __initpb__(cb) {
    var var_head = document.getElementsByTagName('HEAD').item(0)
    var var_script = document.createElement('script')
    var_script.type = 'text/javascript'
    var_script.src = '//security.iq.com/static/intl/cook/v1/cooksdk.js'
    var _ua = navigator.userAgent.toLowerCase()
    var isIpad =
      /ipad/i.test(_ua) || /iphone os/i.test(_ua) || /lepad_hls/i.test(_ua)
    // if (isIpad) {
    //     var_script.src = '//security.iqiyi.com/static/cook/v1/cooksdkpcwpad.js';
    // }
    var_head.appendChild(var_script)
    var callbackList = []
    window.qaLoadingDfp = function(cb) {
      callbackList.push(cb)
    }
    var checkLoading = function() {
      while (callbackList.length > 0) {
        try {
          var callFunc = callbackList.shift()
          callFunc()
        } catch (e) {}
      }
    }
    // 判断是否加载成功PCWDEV-1855
    var isLoadSuc = function() {
      clearTimeout(timer)
      // 加一个2s定时器，若2s后cooksdk还未加载成功，则mainQa.init并执行chedkLoading
      timer = setTimeout(function() {
        if (!loaded) {
          clearTimeout(timer)
          loaded = true
          cb()
          // mainQa.init({
          //   url: oldUrl,
          //   newUrl: newUrl
          // })
          // 方便国际站PCW 调用
          window.mainQaInstance = mainQa
          checkLoading()
        }
      }, 3000)
    }
    isLoadSuc()
    var isIE = /msie/.test(_ua)
    if (isIE) {
      // 根据不同浏览器判断加载完成时机
      var_script.onreadystatechange = function() {
        if (/loaded|complete/.test(var_script.readyState)) {
          loaded = true
          clearTimeout(timer)
          cb()
          // mainQa.init({
          //   url: oldUrl,
          //   newUrl: newUrl
          // })
          // 方便国际站PCW 调用
          window.mainQaInstance = mainQa
          checkLoading()
        }
      }
    } else {
      var_script.onload = function() {
        loaded = true
        clearTimeout(timer)
        cb()
        // mainQa.init({
        //   url: oldUrl,
        //   newUrl: newUrl
        // })
        // 方便国际站PCW 调用
        window.mainQaInstance = mainQa
        checkLoading()
      }
    }
  }

  try {
    if (__getLocalDfp()) {
      // 内存有
      mainQa.init({
        url: oldUrl,
        newUrl: newUrl
      })
      __initpb__(function() {})
      // 方便国际站PCW 调用
      window.mainQaInstance = mainQa
    } else {
      __initpb__(function() {
        mainQa.init({
          url: oldUrl,
          newUrl: newUrl
        })
      })
    }
  } catch (e) {}
})()

/* 页面点击地图 */
// changelog 2014-07-14 刘永生 添加用body对点击事件兜底处理
;(function() {
  var ua = navigator.userAgent.toLowerCase()
  var pf = '1'
  var p = '10'
  var p1 = '101'
  if (/(android)|(like mac os x)/i.test(ua)) {
    pf = '2'
    p = '20'
  }
  // } else if (isPPS) {
  //   pf = '201'
  // }
  // p1->pc：101，手机201，pad202
  if (/(android)/i.test(ua)) {
    p1 = '201'
  } else if (/(like mac os x)/i.test(ua)) {
    if (/(iphone)/i.test(ua)) {
      p1 = '201'
    } else {
      // pad
      p1 = '202'
    }
  }
  if (mainQaInstance) {
    p1 = mainQaInstance.cookieGet('p1') || p1
  }
  // 兼容pps网站
  var logServerUrl =
    '//msg-intl.qy.net/b?t=20&p=' + p + '&p1=' + p1 + ('&pf=' + pf)
  if (window.uniqy) {
    // 台湾新版页面支持h5和pc
    logServerUrl =
      '//msg-intl.qy.net/b?t=20&p=' + p + '&p1=' + p1 + ('&pf=' + pf)
  }
  // 国际站
  var intlPageInfo = window.intlPageInfo || {}
  if (intlPageInfo.i18n === 'global') {
    var ua = navigator.userAgent.toLowerCase()

    if (ua.indexOf('electron/') > -1) {
      p1 = '1_11_223'
      if (ua.indexOf('mac') > -1) {
        p1 = '1_11_222'
      }
    } else {
      p1 = '1_10_222'
      if (/(android)|(like mac os x)/i.test(ua)) {
        p1 = '2_20_223'
      }
    }
    // p1 = '1_10_222'
    // var ua = navigator.userAgent.toLowerCase()
    // if (/(android)|(like mac os x)/i.test(ua)) {
    //   p1 = '2_20_223'
    // }
    if (mainQaInstance) {
      p1 = mainQaInstance.cookieGet('p1') || p1
    }
    logServerUrl = '//msg-intl.qy.net/act?t=20&p1=' + p1
  }

  var sender = function(params, url) {
    if (params?.block === 'body') {
      return
    }

    // 用image.src发送pingback
    params = params || {}
    if (url.indexOf('?') == -1) {
      url += '?'
    } else {
      url += '&'
    }
    var timeStamp = +new Date()
    params._ = timeStamp // 防止缓存
    if (mainQaInstance) {
      var abtest = mainQaInstance.cookieGet('abtest')
      if (abtest) {
        abtest = JSON.parse(abtest)
        let abtestStr = ''
        Object.keys(abtest).forEach(key => {
          abtestStr += abtest[key] + '-'
        })
        abtestStr = abtestStr.slice(0, abtestStr.length - 1)
        if (abtestStr && abtestStr.length > 0) {
          params['abtest'] = abtestStr
        }
      }
    }
    for (var key in params) {
      if (params.hasOwnProperty(key)) {
        url +=
          encodeURIComponent(key) + '=' + encodeURIComponent(params[key]) + '&'
      }
    }
    if (url[url.length - 1] === '&') {
      url = url.slice(0, -1)
    }
    var qa = new lib.action.Qa()
    var domain = qa.domainName()
    if (params.rlink) {
      qa.cookieSet('intl_t20_pb', url, 0, '/', domain)
    }
    var image = new Image()
    image.src = url
    if (image.complete) {
      qa.cookieRemove('intl_t20_pb', '/', domain)
    } else {
      image.onload = function() {
        qa.cookieRemove('intl_t20_pb', '/', domain)
      }
      image.onerror = function() {
        qa.cookieRemove('intl_t20_pb', '/', domain)
      }
    }
  }
  // if (!document.querySelectorAll) {
  //   /*  处理兼容性IE8以下 */
  //   var querySelectorAllIE = function(selector) {
  //     if (!selector) {
  //       return
  //     }
  //     var elementCollection = []
  //     var doc = document
  //     var allElements = doc.getElementsByTagName('*') || doc.all
  //     for (var i = 0, len = allElements.length; i < len; i++) {
  //       if (allElements[i].id == selector) {
  //         elementCollection.push(allElements[i])
  //       }
  //     }
  //     return elementCollection
  //   }
  // }
  function getElementsByBlockName(blockArr) {
    if (typeof blockArr === 'string') {
      blockArr = blockArr.split(',')
    }
    var ret = []
    var blockLen = blockArr.length
    var idPrefix = 'block-'
    var idSuffix
    var idMidfix
    var id
    var elem
    // var startCharCode = 'A'.charCodeAt();
    var $ = function(id) {
      return document.getElementById(id)
    }
    var fcc = String.fromCharCode
    var i
    for (i = 0; i < blockLen; i++) {
      idSuffix = blockArr[i].replace(/\s+/g, '')
      id = idPrefix + idSuffix
      var query = '*[id=' + id + ']'
      var temp = ''
      if (document.querySelectorAll) {
        temp = document.querySelectorAll(query)
      } else {
        temp = querySelectorAllIE(id)
      }
      var len = temp.length
      if (len) {
        if (len > 1) {
          var m = 0
          var n = 1
          while (m < len) {
            elem = temp[m]
            if (m) {
              elem['__bid__'] = idSuffix + n
              elem['id'] = id + n
              n++
            } else {
              elem['__bid__'] = idSuffix
            }
            ret.push(elem)
            m++
          }
        } else {
          elem = $(id)
          elem['__bid__'] = idSuffix
          ret.push(elem)
        }
      }
    }
    return ret
  }
  function getElementsByBlockId() {
    var ret = []
    var idPrefix = 'block-'
    var idSuffix
    var idMidfix
    var id
    var elem
    var startCharCode = 'A'.charCodeAt()
    var $ = function(id) {
      return document.getElementById(id)
    }
    var fcc = String.fromCharCode
    var i
    for (i = 0; i < 26; i++) {
      idSuffix = fcc(startCharCode + i)
      id = idPrefix + idSuffix
      var query = '*[id=' + id + ']'
      var temp = ''
      if (document.querySelectorAll) {
        temp = document.querySelectorAll(query)
      } else {
        temp = querySelectorAllIE(id)
      }
      var len = temp.length
      if (len) {
        if (len > 1) {
          var m = 0
          var n = 1
          while (m < len) {
            elem = temp[m]
            if (m) {
              elem['__bid__'] = idSuffix + n
              elem['id'] = id + n
              n++
            } else {
              elem['__bid__'] = idSuffix
            }
            ret.push(elem)
            m++
          }
        } else {
          elem = $(id)
          elem['__bid__'] = idSuffix
          ret.push(elem)
        }
      }
    }
    for (i = 0; i < 26; i++) {
      idMidfix = fcc(startCharCode + i)
      var hit = false
      for (var j = 0; j < 26; j++) {
        idSuffix = fcc(startCharCode + j)
        id = idPrefix + idMidfix + idSuffix
        elem = $(id)
        if (elem) {
          hit = true
          elem['__bid__'] = idMidfix + idSuffix
          ret.push(elem)
        }
      }
    }
    return ret
  }

  function cookieGet(name) {
    var getRaw = function(key) {
      if (
        new RegExp(
          '^[^\\x00-\\x20\\x7f\\(\\)<>@,;:\\\\\\"\\[\\]\\?=\\{\\}\\/\\u0080-\\uffff]+\x24'
        ).test(key)
      ) {
        var reg = new RegExp('(^| )' + key + '=([^;]*)(;|\x24)')
        var result = reg.exec(document.cookie)
        if (result) {
          return result[2] || ''
        }
      }
      return ''
    }
    try {
      name = getRaw(name)
      if (typeof name === 'string') {
        // 解决多核切换的浏览器cookies读取问题
        if (name.length > 1 && name == 'deleted') {
          return ''
        } else {
          return decodeURIComponent(name) || ''
        }
      } else {
        return ''
      }
    } catch (e) {
      return ''
    }
  }

  function getElementsByTagNameQchunk() {
    // 对重复data-id 以1,2,3...顺序排列
    var flagObj = {} // 标识位
    var ret = []
    var idPrefix = 'block-'
    var chunks = document.getElementsByTagName('qchunk')
    var chunk
    var id
    for (var i = 0, len = chunks.length; i < len; i++) {
      chunk = chunks[i]
      id = chunk.getAttribute('data-id') || ''
      if (id.substr(0, idPrefix.length).toLowerCase() == idPrefix) {
        var _bid = id.substr(idPrefix.length)
        // 若不存在，记录并标识
        if (!flagObj[_bid]) {
          flagObj[_bid] = 1
        } else {
          // 若已存在，记录并继续向下查找
          var num = ++flagObj[_bid]
          var temp
          do {
            temp = _bid[0]
            temp += num
            num++
          } while (flagObj[temp])
          flagObj[temp] = 1
          chunk.setAttribute('data-id', temp)
          _bid = temp
        }
        chunk['__bid__'] = _bid
        ret.push(chunk)
      }
    }
    return ret
  }
  // 检查点击事件的pingback是否已经被处理发送过
  function checkEvent(event, target, currentTarget) {
    // chrome中事件冒泡时，event对象始终只有一个，所以在event对象上进行核查,确保在捕获-冒泡过程中只处理一次
    if (event._clickMapPBSent) {
      return false
    }
    event._clickMapPBSent = true
    if (target === currentTarget) {
      target._c = 1
      return true
    }
    // IE 8中事件冒泡时，event对象每次都不一样，所以在target上进行核查
    if (target._c >= 1) {
      target._c++
      return false
    } else {
      // 在Bubbling Phase，将计数器累加
      if (typeof target._c !== 'number') {
        target._c = 1
      } else {
        target._c++
      }
      if (!target._adjustClickMap) {
        var adjustClickMap = function() {
          this._c = 0 // 在 Target Phase，将计数器重置
        }
        target._adjustClickMap = adjustClickMap.bind(target)
        try {
          if (target.addEventListener) {
            target.addEventListener('mousedown', target._adjustClickMap, false)
          } else {
            target.attachEvent('onmousedown', target._adjustClickMap)
          }
        } catch (e) {}
      }
    }
    return true
  }
  var clickListener = function(event) {
    event = event || window.event
    var target = event.target || event.srcElement
    var currentTarget = event.currentTarget || this // clickListener被bind过了，因此this是currentTarget
    if (!checkEvent(event, target, currentTarget)) {
      // 检查点击事件的pingback是否已经被处理发送过
      return
    }
    // re=分辨率(宽*高)&clkx=x坐标&clky=y坐标
    var scrollTop =
      (document.documentElement && document.documentElement.scrollTop) ||
      document.body.scrollTop
    var scrollLeft =
      (document.documentElement && document.documentElement.scrollLeft) ||
      document.body.scrollLeft

    var scrollWidth =
      (document.documentElement && document.documentElement.scrollWidth) ||
      document.body.scrollWidth
    var scrollHeight =
      (document.documentElement && document.documentElement.scrollHeight) ||
      document.body.scrollHeight

    var clientHeight =
      (document.documentElement && document.documentElement.clientHeight) ||
      document.body.clientHeight
    var clientWidth =
      (document.documentElement && document.documentElement.clientWidth) ||
      document.body.clientWidth

    var pageHeight = Math.max(scrollHeight, clientHeight)
    var pageWidth = Math.max(scrollWidth, clientWidth)

    var blockId = this['__bid__'] || ''
    var pageQuery = parseQuery(
      document.getElementsByTagName('body')[0].getAttribute('data-pb'),
      '&'
    )
    var blockQuery = parseQuery(this.getAttribute('data-pb'), '&')
    var pingbackTarget
    var rseat
    var tagName

    do {
      pingbackTarget = target
      rseat = pingbackTarget.getAttribute('rseat')
      tagName = pingbackTarget.tagName.toUpperCase()
      target = target.parentNode
      if (pingbackTarget == this) {
        break // 最多回溯到区块上，便不再继续上溯了
      }
    } while (!rseat && tagName !== 'A' && tagName !== 'IMG')

    var targetQuery = parseQuery(pingbackTarget.getAttribute('data-pb'), '&')

    var r
    var rt
    var rlink
    var abtest
    var targetIsA =
      target && target.tagName && target.tagName.toUpperCase() === 'A'
    if (rseat) {
      // AB实验分割投递值
      abtest = rseat.match(/_AB_(.+)_TEST/)
      if (abtest) {
        var temp = rseat.split(/_AB_.+_TEST/)
        rseat = temp[0] + (temp[1] || '')
        abtest = abtest[1].split('--')
        abtest = /(android)|(like mac os x)/i.test(ua) ? abtest[1] : abtest[0]
      }
      targetQuery.rseat = rseat
    }
    if (tagName === 'A') {
      r = pingbackTarget.title || ''
      rt = 'a'
      // 页面上的href为"http://i.iqiyi.com"时，a.href为"http://i.iqiyi.com/"
      rlink = pingbackTarget.getAttribute('href') || ''
    } else if (tagName === 'IMG') {
      r = pingbackTarget.alt || ''
      rt = 'i'

      rlink = ''
    } else {
      r = pingbackTarget.title || ''
      rt = 'e'
      rlink = ''
    }
    if (tagName !== 'A' && targetIsA) {
      if (target.getAttribute('href')) {
        if (target.getAttribute('href').indexOf('javascript') == -1) {
          rlink =
            location.protocol +
              target.getAttribute('href').replace(/^((http|https):)/, '') || ''
        } else {
          rlink = target.getAttribute('href')
        }
      }
    }

    // 页面也调用hu方法，调用逻辑太绕，这里取hu的时候加一层兼容逻辑
    var userInfoStorage =
      window.__global__info && window.__global__info.userInfo
    var userHu = userInfoStorage && userInfoStorage.hu
    userHu = cookieGet('hu') || userHu

    var pu
    var u
    var jsuid
    var cookieMap
    var abResult
    cookieMap = parseQuery(document.cookie, ';')
    pu = cookieMap['pu'] || cookieMap['P00003'] || ''
    u = cookieMap['u'] || cookieMap['QC005'] || ''
    jsuid = cookieMap['QC006'] || ''
    var vVal = cookieMap['v'] || ''
    var mkeyVal = cookieMap['mkey'] || ''
    var huVal = userHu ? userHu : -1
    var deVal = cookieMap['de']
    try {
      abResult = JSON.parse(cookieMap['QYABEX'] || '{}')
    } catch (e) {
      abResult = {}
    }
    var pageInfo = (window.Q && Q.PageInfo) || {}
    // 台湾新版页面没有Q，挂到uniqy下面
    if (window.uniqy) {
      pageInfo = (window.uniqy && uniqy.PageInfo) || {}
    }
    var i18nType = pageInfo.i18n !== 'tw_t'
    var modType
    if (i18nType) {
      modType = 'cn_s' // 大陆简体
    } else {
      modType = 'tw_t' // 台湾繁体
    }
    var params = {
      block: blockId,
      rt: rt,
      r: r,
      rlink: rlink,
      pu: decodeURIComponent(pu),
      u: decodeURIComponent(u),
      jsuid: decodeURIComponent(jsuid),
      ce: window.webEventID || '',
      re: pageWidth + '*' + pageHeight,
      clkx: event.clientX + scrollLeft,
      clky: event.clientY + scrollTop,
      mod: modType,
      v: vVal,
      mkey: mkeyVal,
      tm:
        window.__qlt && window.__qlt.statisticsStart
          ? new Date() - window.__qlt.statisticsStart
          : ''
    }
    if (abtest && abResult[abtest] && abResult[abtest].abtest) {
      params.abtest = abResult[abtest].abtest
    }

    // 国际站
    var intlPageInfo = window.intlPageInfo || {}
    if (intlPageInfo.i18n === 'global') {
      var pbInfos = intlPageInfo.pbInfos || {}
      var url = new lib.kit.Url(document.location.href)
      if (isPWA()) params['stuptype'] = 'shortcut'
      params['lang'] = url.getParam('lang') || pbInfos.lang || ''
      params['mod'] = url.getParam('mod') || pbInfos.mod || ''
      params['rpage'] = pbInfos.rpage || ''
      params['purl'] = location.href
      params['rfr'] = document.referrer
      params['lrfr'] = cookieMap['QC007'] || ''
      if (pbInfos.pbClickParams) {
        window.pingbackParams = pbInfos.pbClickParams
      }
      var qa = new lib.action.Qa()
      var vfm20 = qa.getUtmPbParams()
      params['vfm'] = vfm20
    }
    if (window.pingbackParams) {
      if (Object.assign) {
        params = Object.assign({}, window.pingbackParams, params)
      } else {
        params = extend({}, window.pingbackParams, params)
      }
    }
    // 播放页模板参数统计
    if (
      window.Q &&
      Q.PageInfo &&
      Q.PageInfo.playPageInfo &&
      Q.PageInfo.playPageInfo.videoTemplate
    ) {
      params.tmplt = Q.PageInfo.playPageInfo.videoTemplate || ''
    }

    if (intlPageInfo.i18n === 'global') {
      if (!window.mainQaInstance) {
        return
      }
      window.mainQaInstance.getHu(function(hu) {
        window.mainQaInstance.getFingerPrint(function(dfp) {
          params['dfp'] = dfp
          params['hu'] = decodeURIComponent(huVal) || hu
          params['pu'] =
            decodeURIComponent(pu) || window.mainQaInstance.getUserInfoUid()
          params['de'] =
            decodeURIComponent(deVal) || window.mainQaInstance.getQtsid()
          params['ce'] = window.mainQaInstance.getWeid()
          params['stime'] = new Date().getTime()
          params['timezone'] = new Date().toUTCString()

          sender(
            extend(params, pageQuery, blockQuery, targetQuery),
            logServerUrl
          )
        })
      })
    } else {
      sender(extend(params, pageQuery, blockQuery, targetQuery), logServerUrl)
    }
  }

  var getQchunks = function() {
    var blocks = getElementsByBlockId()
    // 国际站
    var intlPageInfo = window.intlPageInfo || {}
    if (intlPageInfo.i18n === 'global') {
      var pbInfos = intlPageInfo.pbInfos || {}
      if (pbInfos.blockInfo) {
        blocks = getElementsByBlockName(pbInfos.blockInfo)
      }
    }
    var qchunks = getElementsByTagNameQchunk()
    var elem
    var blockClickListener
    // 加入去重，否则一些带id的qchunk区块会被绑定两次
    for (var k = 0, klen = blocks.length; k < klen; k++) {
      if (qchunks.indexOf(blocks[k]) == -1) {
        qchunks.push(blocks[k])
      }
    }
    // qchunks = qchunks.concat(blocks);
    // body负责对页面中的点击事件进行兜底：如果没有区块处理，最终body会进行处理
    var body = document.getElementsByTagName('body')[0]
    body.__bid__ = 'body'
    qchunks.push(body)
    return qchunks
  }
  var _qchunks = getQchunks()

  // Bug 106092 - 【提升DAU-数据统计】龙源大首页点击地图显示
  // 通栏异步加载的时候，为返回的通栏结构绑定点击事件
  var bindingAsync = function(asyncBlocks) {
    var _blocks = _qchunks || []
    if (asyncBlocks && asyncBlocks.data) {
      // 异步加载的模块
      _blocks = asyncBlocks.data.down('[data-block-name]') || []
      if (_blocks.length === 0) {
        return
      }
      var idPrefix = 'block-'
      _blocks.forEach(function(_bk) {
        _bk['__bid__'] = _bk.id.substr(idPrefix.length)
      })
    }
    for (var j = 0, jlen = _blocks.length; j < jlen; j++) {
      var chunk = _blocks[j]
      var _chunk = ''
      if (window.Q) {
        _chunk = Q(chunk)
      } else if (window.jQuery) {
        _chunk = $(chunk)
      }
      if (_chunk.attr('data-asyn-pb')) {
        continue
      }
      var chunkClickListener = clickListener.bind(chunk)
      if (chunk.addEventListener) {
        chunk.addEventListener('mousedown', chunkClickListener, false)
      } else {
        chunk.attachEvent('onmousedown', chunkClickListener)
      }

      // 设置标志位，说明本模块已经添加了事件响应了
      _chunk.attr('data-asyn-pb', 'true')
    }
  }

  var bindingByNativeJs = function() {
    var _blocks = _qchunks || []
    if (_blocks.length) {
      for (var j = 0, jlen = _blocks.length; j < jlen; j++) {
        var chunk = _blocks[j]

        if (chunk.getAttribute('data-asyn-pb')) {
          continue
        }
        var chunkClickListener = clickListener.bind(chunk)
        if (chunk.addEventListener) {
          chunk.addEventListener('mousedown', chunkClickListener, false)
        } else {
          chunk.attachEvent('onmousedown', chunkClickListener)
        }

        // 设置标志位，说明本模块已经添加了事件响应了
        chunk.setAttribute('data-asyn-pb', 'true')
      }
    }
  }

  // 清除属性 data-asyn-pb
  var clearLoadFlag = function(list) {
    if (list.length) {
      for (var j = 0, jlen = list.length; j < jlen; j++) {
        var chunk = list[j]
        if (chunk.getAttribute('data-asyn-pb')) {
          chunk.removeAttribute('data-asyn-pb')
        }
      }
    }
  }

  // 第三方使用则无Q，因此需要判断
  if (window.Q && Q.$) {
    Q.$(window).on('scroll', bindingAsync)
    Q.$(window).on('resize', bindingAsync)
    Q.event.customEvent.on('bindingPingback', bindingAsync)
    bindingAsync()
  } else if (window.jQuery) {
    try {
      $(window).on('scroll', bindingAsync)
      $(window).on('resize', bindingAsync)
      bindingAsync()
    } catch (e) {}
  } else {
    bindingByNativeJs()
  }
  function parseQuery(query, sep) {
    var ret = {}
    sep = sep || '&'
    if (query) {
      var kvPairs = query.split(sep)
      var kvPair
      for (var i = 0, len = kvPairs.length; i < len; i++) {
        kvPair = kvPairs[i]
        if (kvPair) {
          kvPair = kvPair.split(/\s*=\s*/g)
          if (kvPair[0]) {
            ret[kvPair[0].replace(/^\s*|\s*$/g, '')] = kvPair[1] || ''
          }
        }
      }
    }
    return ret
  }
  function extend(target, src) {
    var ret = target || {}
    var b
    for (var i = 1, len = arguments.length; i < len; i++) {
      b = arguments[i]
      if (b) {
        for (var p in b) {
          if (b.hasOwnProperty(p)) {
            ret[p] = b[p]
          }
        }
      }
    }
    return ret
  }
  // 重新加载点击地图
  lib.action.Qa.prototype.reloadClickMap = function() {
    _qchunks = getQchunks()
    this.qchunks = _qchunks
    clearLoadFlag(_qchunks)
    bindingByNativeJs()
  }
  if (window.lib && window.lib.action && window.lib.action.Qa) {
    if (window.postMessage) {
      // 广播qa加载完成
      window.postMessage('qaLoad', location.href)
    }
  }
})()
