import React from 'react'
import { connect } from 'react-redux'
import {
  locUrl,
  rebuildCommonUrl,
  rebuildHttpsUrl,
  hasAgentType
} from '@/kit/common'
import { getTimeZone } from '@/utils/common'
import Meta from '@/components/common/Meta'
import { isLogin, getUid } from '@/utils/userInfo'
import $http, { serialize } from '@/kit/fetch'
import {
  bankDoPayInterface,
  SUBMIT_PAY_PHONE,
  INIT_3DS_VERIFY
} from '@/constants/interfaces'
import { bankLangPkg } from '@/components/pages/vip/config/language'
import { getCookies, setCookies, delClientCookies } from '@/kit/cookie'
import { sendQosLog, calCE } from '@/kit/qosTag'
// import { getCookies } from '@/kit/cookie'
import { getDevice } from '@/kit/device'
import { queryFromUrl } from '@/kit/url'
import UserWrap from '@/components/common/Header/userWrap'
import VipPageContainer from '@/components/pages/vip/VipPageContainer'
import TabContent from '@/components/common/Vip/TabContent'
import NewPayResult from '@/components/common/Vip/NewPayResult'
import DSModal from '@/components/common/Vip/DSModal'
import Gopay from '@/components/common/Vip/Gopay'

// import EmptyComponent from '@/components/pages/vip/SelectPackage/emptyComponent'
import DeviceCtx from '@/components/context'
import {
  getVipData,
  // handleVipInfo,
  newHandleVipInfo,
  getOrderQuery,
  getNewAuth,
  createVipOrder,
  getQueryPaymentUrl,
  getBankPayUrl,
  getAbtest,
  getTvInfo,
  getUGInfo,
  getUserCard
} from '@/components/pages/vip/api'
import TemplateErrorPop from '@/components/pages/vip/Error'
import TongJiPb from '@/components/common/TongJiPb'

const isMobile = getDevice() === 'mobile'
class Order extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      vipInfo: null,
      orderInfo: undefined,
      error: '',
      initParams: {},
      showPayResult: 'pkgSelect',
      popLoading: true,
      resultType: '',
      resultInfo: {},
      defaultVipModalInfo: undefined,
      showFreeze: false,
      dopayError: '',
      resultSuccess: false,
      selectedPkg: null,
      // payTypeOption: null,
      currentPayTypeOptions: [],
      createOrdering: false,
      gopayLoading: false,
      gopayError: '',
      // 判断是不是兑换优惠券之后的请求
      hasRedeemed: false,
      userCards: null,
      tvInfo: undefined,
      show3DSModal: false,
      //  3ds验证接口返回的数据
      DSResData: ''
    }
    this.getData = this.getData.bind(this)
  }

  static async getInitialProps({ ctx }) {
    const { query, req } = ctx
    const url = req.url
    const mytv = queryFromUrl(url, 'mytv')
    const myvip = queryFromUrl(url, 'myvip')
    const params = {
      fc: queryFromUrl(url, 'fc') || '',
      fv: getCookies('playFV', ctx) || queryFromUrl(url, 'fv') || '',
      abtest: queryFromUrl(url, 'abtest') || '',
      bstp: 56,
      cashier_type: mytv || myvip ? 'otp' : 'norm'
    }
    const testRes = getAbtest()
    params.abtest = `page_cashier${testRes ? `,${testRes}` : ''}${
      params.abtest ? ',' + params.abtest : ''
    }`
    const cookieFC = getCookies('QCedm001', ctx)
    const cookieFV = getCookies('QCedm002', ctx)
    const fcList = (params.fc && params.fc.split(',')) || []
    if (fcList.indexOf(cookieFC) < 0 && cookieFC) {
      params.fc = `${params.fc ? params.fc + ',' : ''}${cookieFC}`
      params.fv = cookieFV
    }
    const isRN = +query.isRN === 1
    return { isRN, pbParams: params, mytv, myvip }
  }

  async componentDidMount() {
    const params = this.getInitParams()
    const cookieFC = getCookies('QCedm001')
    const cookieFV = getCookies('QCedm002')
    const fcList = (params.fc && params.fc.split(',')) || []
    if (fcList.indexOf(cookieFC) < 0 && cookieFC) {
      params.fc = `${params.fc ? params.fc + ',' : ''}${cookieFC}`
      params.fv = cookieFV
    }

    this.setState({
      initParams: params
    })
    // ug检测
    const tvId = queryFromUrl(locUrl(), 'qipuId')
    const utmCampaign = queryFromUrl(locUrl(), 'utm_campaign') || '' // ug标识
    const utms = utmCampaign.split('_')
    if (utms.length >= 9) {
      // 规则: 代理_客户_ 人群_ 策略_平台_地区_内容_日期_自定义
      const ugType = utms[utms.length - 1] // TEXT文案 PIC图片 VID视频
      if (ugType && !(ugType === 'VID' && !isMobile)) {
        let tvInfo
        if (ugType === 'VID' && tvId) {
          // h5带视频 需要取视频信息
          tvInfo = await getTvInfo(tvId)
        }

        // 接口只针对图片和文字
        const ugInfo = await getUGInfo({
          campaign: utmCampaign,
          type: ugType,
          content: utms[6]
        })
        this.setState({
          ugInfo,
          tvInfo
        })
      }
    }

    params.startT = Date.now()
    window.globalVipCE =
      (window._performance && window._performance.eventId) || calCE() || ''
    this.getData(params)
    const vipPageInfoCookie = getCookies('vip_page_info')
    const defaultVipModalInfo = vipPageInfoCookie
      ? JSON.parse(vipPageInfoCookie)
      : ''

    // params.defaultVipModalInfo = defaultVipModalInfo
    if (vipPageInfoCookie) {
      this.setState({
        defaultVipModalInfo
      })
      delClientCookies('vip_page_info')
    }
    // http://pms.qiyi.domain/browse/GLOBALLINEDEV-3561 支持老台湾站用户同步到新站点pcw收银台
    // 注意:老台湾弃用后请删除此段逻辑，url query会占用token参数，所以在这个需求没下线之前不要在url上新增token的query
    const syncToken = queryFromUrl(locUrl(), 'token')
    function delParam(paramKey) {
      // 删除url指定某个query
      let url = window.location.href
      const urlParam = window.location.search.substr(1)
      const beforeUrl = url.substr(0, url.indexOf('?'))
      let nextUrl = ''

      const arr = new Array([])
      if (urlParam !== '') {
        const urlParamArr = urlParam.split('&')
        for (let i = 0; i < urlParamArr.length; i++) {
          const paramArr = urlParamArr[i].split('=')
          if (paramArr[0] !== paramKey) {
            arr.push(urlParamArr[i])
          }
        }
      }
      if (arr.length > 0) {
        nextUrl = '?' + arr.join('&')
      }
      url = beforeUrl + nextUrl
      return url
    }
    if (syncToken === 'not_login') {
      window.history.pushState(
        { url: delParam('token'), title: document.title },
        document.title,
        delParam('token')
      )
      if (isLogin()) {
        this.loginRef.handleUserLogout()
      }
    } else if (syncToken) {
      if (isLogin()) {
        await this.loginRef.handleUserLogout()
      }
      // 实现跨域登陆
      await getNewAuth(syncToken, params.ptid).then(data => {
        if (data.authcookie) {
          // 如果是这种方式同步过来的用户，强制直接ntw用户
          setCookies('mod', 'ntw', {
            time: 60 * 60 * 24 * 365
          })
          window.history.pushState(
            { url: delParam('token'), title: document.title },
            document.title,
            delParam('token')
          )
          window.location.reload()
        } else {
          if (data instanceof Error) {
            // window.location.reload()
          }
          if (typeof data === 'string') {
            window.alert(data)
          }
        }
      })
    }
  }

  getData = async (params, couponCode) => {
    const startT = params.startT
    delete params.startT
    const { i18nShowCoupon } = this.props
    const vipLangPkg = this.props.vipLangPkg.toJS()
    this.setState({
      popLoading: true
    })
    const checkoutT = Date.now()
    try {
      sendQosLog({
        diy_evt: 'norm_checkout_start',
        ce: window.globalVipCE
      })
    } catch (err) {
      console.log(err)
    }

    const initParams = { ...this.state.initParams, ...params }
    try {
      const newVipInfoRes = await getVipData(
        initParams,
        this.props.modeLangObj.toJS()
      )
      let hasCard = false
      if (isLogin()) {
        const userCardData = await getUserCard()
        if (userCardData.code === 'A00000') {
          const { data } = userCardData
          hasCard = data.length > 0
          this.setState({ userCards: data.slice(0, 5) })
        }
      } else {
        this.setState({ userCards: null })
      }

      let resData = null
      if (newVipInfoRes.code === 'A00000') {
        try {
          sendQosLog({
            diy_evt: 'norm_checkout_end',
            tm: Date.now() - checkoutT,
            ce: window.globalVipCE,
            diy_success: 1
          })
        } catch (err) {
          console.log(err)
        }

        resData = newVipInfoRes.data
        const vipInfo = newHandleVipInfo(
          resData,
          vipLangPkg,
          i18nShowCoupon || '',
          couponCode || params.couponCode,
          params.defaultVipModalInfo,
          hasCard
        )
        try {
          sendQosLog({
            diy_evt: 'norm_handleData_end',
            tm: Date.now() - startT
          })
        } catch (err) {
          console.log(err)
        }

        if (couponCode || params.couponCode) {
          this.setState({
            hasRedeemed: true
          })
        } else {
          this.setState({
            hasRedeemed: false
          })
        }
        this.setState({
          vipInfo,
          popLoading: false
        })
      }
    } catch (err) {
      sendQosLog({
        diy_evt: 'norm_checkout_end',
        tm: Date.now() - checkoutT,
        ce: window.globalVipCE,
        diy_success: 0,
        diy_msg: 'checkout Err ==>' + err
      })
      console.log(err)
      this.setState({
        error: err ? err.msg || err.message : 'error'
      })
    }
  }

  getInitParams() {
    const { mytv, myvip } = this.props
    const _url = locUrl()
    const productName = queryFromUrl(_url, 'productName')
    const typeParam = queryFromUrl(_url, 'vipType')
    const { vipPid, bossCode, ptid } = this.context
    const vipType =
      typeParam || (productName ? vipPid[productName].viptype : '')
    const initParams = {
      vipType,
      aid: queryFromUrl(_url, 'aid') || '',
      fc: queryFromUrl(_url, 'fc') || '',
      fv: getCookies('playFV') || queryFromUrl(_url, 'fv') || '',
      amount: queryFromUrl(_url, 'amount') || '',
      payAutoRenew: queryFromUrl(_url, 'payAutoRenew') || '',
      albumId: queryFromUrl(_url, 'albumId') || '',
      fr_version: queryFromUrl(_url, 'fr') || '',
      abtest: queryFromUrl(_url, 'abtest') || '',
      couponCode: queryFromUrl(_url, 'couponCode') || '',
      partnerID: queryFromUrl(_url, 'partnerID'),
      returnUrl: queryFromUrl(_url, 'returnUrl') || '',
      vipPid,
      bossCode,
      ptid
    }
    // 安卓tv和webtv透传来的参数，安卓tv: end = 1， webtv: end = 2或无
    if (mytv || myvip) {
      const tvEnd = queryFromUrl(_url, 'end')
      initParams.device_id = queryFromUrl(_url, 'device_id')
      if (tvEnd === '1') {
        initParams.tv_end = tvEnd
        initParams.mkey = queryFromUrl(_url, 'mkey')
        initParams.platform = queryFromUrl(_url, 'platform')
      } else {
        initParams.lang = queryFromUrl(_url, 'lang')
        initParams.platform = queryFromUrl(_url, 'platform')
        initParams.app_lm = queryFromUrl(_url, 'app_lm')
      }
      initParams.key = queryFromUrl(_url, 'key')
    }
    return initParams
  }

  handleSubmitFirst = async selectedPkg => {
    if (!isLogin()) {
      // this.loginRef.handleUserLogin()
      if (!window.sdkPackManager.globalLogin) {
        if (!window.sdkPackManager.initLogin) {
          await this.loginRef.initLogin()
        } else {
          await window.sdkPackManager.initLogin()
        }
      }
      window.sdkPackManager.globalLogin.openLoginRegWindow({
        vipPageInfo: { productSetCode: selectedPkg.productSetCode }
      })
      return
    }

    // const cookieValue = getCookies('QC005')f
    // const last = cookieValue && cookieValue[cookieValue.length - 1]
    // let jumpTest = false
    // if (Number(last)) {
    //   jumpTest = last < 5
    // } else {
    //   jumpTest = last < 'd'
    // }
    // jumpTest = true // test
    // const { vipInfo } = this.state

    // const jumpAgreement =
    //   vipInfo &&
    //   (!vipInfo.vipTextNodes.vipServiceAgreement ||
    //     vipInfo.isSelectVipServiceAgreement)
    // const hasOnlyPay = selectedPkg.payTypeOptions.length === 1

    // if (jumpTest && jumpAgreement && hasOnlyPay) {
    //   this.handleCreateVipOrder(selectedPkg)
    // } else {

    const query = getOrderQuery(locUrl())
    const selectedQuery = `selectedId=${selectedPkg.id}&pid=${selectedPkg.pid}&v_prod=${selectedPkg.productSetCode}`
    const search = query ? `${query}&${selectedQuery}` : `?${selectedQuery}`

    window.location.href = rebuildCommonUrl(`vip/paytype${search}`)
    // }
  }

  handleCreateVipOrder = async selectedPkg => {
    const payTypeOption = selectedPkg.payTypeOptions[0]
    const newwin =
      getDevice() === 'pc' ? window.open('about:blank', '_blank') : window
    const { initParams, vipInfo } = this.state
    const abtest = getAbtest()
    try {
      const res = await createVipOrder(
        selectedPkg,
        payTypeOption,
        {
          ...initParams,
          _abtest: vipInfo.abtest ? `${vipInfo.abtest},${abtest}` : abtest
        },
        this.props.modeLangObj.toJS()
      )
      const { vipOrder, order, redirectUrl } = res

      if (getDevice() === 'pc') {
        window.location.href = getQueryPaymentUrl(
          vipOrder,
          selectedPkg,
          payTypeOption
        )
      }
      if (redirectUrl) {
        newwin.location.href = redirectUrl
      } else {
        newwin.location.href = getBankPayUrl(
          vipOrder,
          order,
          selectedPkg,
          payTypeOption.payType
        )
      }
    } catch (err) {
      console.error(err)
      newwin.close()
      this.setState({
        error: err ? err.msg || err.message : 'error'
      })
    }
  }

  getUserInfo = () => {
    let userInfo = {}
    if (isLogin()) {
      userInfo.uid = getUid()
      // 之前收银台的逻辑
      let lastGlobalLoginMsg = getCookies('lastGlobalLoginMsg')
      if (
        lastGlobalLoginMsg &&
        lastGlobalLoginMsg !== null &&
        lastGlobalLoginMsg !== ''
      ) {
        lastGlobalLoginMsg = window.JSON.parse(lastGlobalLoginMsg)
        userInfo.username = lastGlobalLoginMsg.realEmail || ''
      } else {
        userInfo.username = ''
      }
    } else {
      userInfo = {}
    }
    return userInfo
  }

  getParams = (
    orderInfo,
    inputInfo,
    iaTransactionId,
    gatewayRecommendation,
    lang
  ) => {
    const PAY_SUC_URL = 'vip/payResult?vipOrder='
    const userInfo = this.getUserInfo()

    const { order, vipOrder, partner, cashierType } = orderInfo || {}
    const { hasCard, boundCardInfo } = inputInfo
    const _defaultUrl = rebuildHttpsUrl(
      PAY_SUC_URL + vipOrder + `&cashierType=${cashierType}`
    )
    const _time = (inputInfo.cardInfo.expireTime || '').split('/')
    let params = {
      partner: partner || 'qiyue_global',
      sign: 'PCW',
      authcookie: 'PCW',
      order_code: order,
      uid: userInfo.uid || '',
      check3d_response_url: encodeURIComponent(_defaultUrl),
      dfp: (window.dfp && window.dfp.tryGetFingerPrint()) || '',
      userName: userInfo.username || '',
      agent_type: '',
      local_lang: lang,
      ds_version: 2,
      iaTransactionId,
      gatewayRecommendation,
      _t: new Date().getTime()
    }
    const secureParam = {
      ds_browse: window.navigator.userAgent,
      ds_SecureChallengeWindowSize: 'FULL_SCREEN',
      ds_colorDepth: window.screen.colorDepth,
      ds_javaEnabled: false,
      ds_timeZone: new Date().getTimezoneOffset(),
      ds_language: window.navigator.language,
      ds_screenHeight: window.screen.height,
      ds_screenWidth: window.screen.width
    }
    if (gatewayRecommendation === 'PROCEED') {
      params = Object.assign({}, params, secureParam)
    }
    if (!hasCard) {
      params = Object.assign({}, params, {
        card_issuer: inputInfo.cardInfo.cardType,
        card_num: inputInfo.cardInfo.bankNumber.replace(/\s/g, ''),
        expiry_month: (_time[0] || '').padStart(2, '0'),
        expiry_year: _time[1] || '',
        security_code: inputInfo.cardInfo.cvvInput
      })
    } else {
      params.card_id = boundCardInfo.cardId
    }

    return params
  }

  // 请求
  fetchInitVerify = async (orderInfo, inputInfo) => {
    const { order } = orderInfo
    const options = { timeout: 40000 }
    const { hasCard, boundCardInfo } = inputInfo
    let params = {
      sign: 'PCW',
      authcookie: 'PCW',
      order_code: order,
      card_num: inputInfo.cardInfo.bankNumber.replace(/\s/g, '')
    }
    if (hasCard) {
      params = {
        sign: 'PCW',
        authcookie: 'PCW',
        order_code: order,
        card_id: boundCardInfo.cardId
      }
    }

    options.params = params

    try {
      // return
      const data = await $http(INIT_3DS_VERIFY, options)
      if (data.code === 'A00000') {
        const { iaTransactionId, redirectHtml, gatewayRecommendation } = data
        if (gatewayRecommendation === 'PROCEED') {
          const verifyDiv = document.createElement('div')
          verifyDiv.style.display = 'none'
          verifyDiv.innerHTML = redirectHtml
          document.body.append(verifyDiv)
          const element = document.getElementById(
            'initiate3dsSimpleRedirectForm'
          )
          if (element) {
            element.submit()
            element.remove()
          }
        }
        this.fetchBankData(
          orderInfo,
          inputInfo,
          iaTransactionId,
          gatewayRecommendation
        )
      } else {
        this.setState({
          dopayError: data.msg || 'Error',
          createOrdering: false
        })
      }
    } catch (err) {
      console.log(err)
    }
  }

  fetchBankData = async (
    orderInfo,
    inputInfo,
    iaTransactionId,
    gatewayRecommendation
  ) => {
    const { modeLangObj } = this.props
    const lang = modeLangObj.get('lang')
    // const { lang } = this.state
    const i18n = bankLangPkg[lang] || bankLangPkg['en_us']
    const options = { timeout: 40000, method: 'POST', credentials: true }
    options.params = this.getParams(
      orderInfo,
      inputInfo,
      iaTransactionId,
      gatewayRecommendation,
      lang
    )

    let result
    try {
      const data = await $http(bankDoPayInterface, options)
      if (data) {
        if (data.is_success === 'T' && parseInt(data.order_status, 10) === 1) {
          result = {
            msg: 'sus',
            status: 'ok'
          }
        } else if (data.code === 'CHECK_3d_ACS') {
          this.setState({
            show3DSModal: true,
            DSResData: data
          })
          if (document) {
            document.body.style.height = '100%'
            document.body.style.overflowY = 'hidden'
          }
          // console.log('进入这里')
          // 到3d认证页面
        } else {
          result = {
            msg: data.msg || '__sysErr'
          }
        }
      } else {
        result = {
          msg: '__sysErr'
        }
      }
    } catch (error) {
      result = {
        msg: error.message.match('timeout') ? '__timeout' : '__sysErr'
      }
    }
    if (result && result.msg) {
      if (result.status === 'ok') {
        // 支付成功，跳到支付结果页
        this.setState({
          dopayError: '',
          resultType: 'pending',
          showPayResult: 'result',
          createOrdering: false
        })
      } else if (result.msg === '__timeout') {
        this.setState({
          resultType: 'neterr',
          showPayResult: 'result',
          createOrdering: false
        })
      } else if (result.msg === '__sysErr') {
        this.setState({
          dopayError: i18n.err_system,
          createOrdering: false
        })
      } else {
        this.setState({
          dopayError: result.msg,
          createOrdering: false
        })
      }
    }
  }

  // 3ds验证弹窗点击
  modalConfirm = resData => {
    const newWindow = window.open('about:blank', '_blank')
    const frameReg = /<iframe.*<\/iframe>/
    const version3ds1 = !!/redirectTo3ds1AcsSimple/.test(
      resData.htmlBodyContent
    )
    if (isMobile && !version3ds1) {
      newWindow.document.write(resData.htmlBodyContent)
    } else {
      newWindow.document.write(resData.htmlBodyContent.replace(frameReg, ''))
    }
    const form =
      newWindow.document.querySelector('#threedsChallengeRedirect') ||
      newWindow.document.querySelector('#redirectTo3ds1AcsSimple')
    if (form) {
      if (version3ds1) {
        const submitForm = newWindow.document.querySelector('form')
        if (submitForm) {
          submitForm.submit()
        }
        setTimeout(() => {
          newWindow.close()
        }, 5000)
      } else if (!isMobile) {
        setTimeout(() => {
          newWindow.close()
        }, 5000)
      }
    }
    // 跳到第三方等待页面
    this.goPayResult()
    this.setState({
      resultType: 'pending',
      show3DSModal: false
    })
    if (document) {
      document.body.style.height = ''
      document.body.style.overflowY = ''
    }
  }

  modalCancel = () => {
    this.setState({
      createOrdering: false,
      show3DSModal: false
    })
    if (document) {
      document.body.style.height = ''
      document.body.style.overflowY = ''
    }
  }

  fetchGopayData = async mobile => {
    const { modeLangObj } = this.props
    const lang = modeLangObj.get('lang')
    const { resultInfo, gopayLoading } = this.state
    if (gopayLoading) return
    const params = {
      sign: 'PCW',
      authcookie: 'PCW',
      order_code: resultInfo.order,
      dfp: (window.dfp && window.dfp.tryGetFingerPrint()) || '',
      mobile,
      local_lang: lang
    }

    try {
      this.setState({
        gopayLoading: true
      })
      const res = await $http(SUBMIT_PAY_PHONE, { params })
      if (res.code === 'A00000') {
        const newwin =
          getDevice() === 'pc' ? window.open('about:blank', '_blank') : window
        newwin.location.href = res.redirectUrl
        this.setState({
          gopayError: '',
          createOrdering: false,
          gopayLoading: false
        })
        this.goPayResult() // setStep('queryPaymentStep')
      } else {
        // eslint-disable-next-line no-unused-expressions
        // getDevice() === 'pc' && newwin.close()
        this.setState({
          gopayError: res.msg,
          createOrdering: false,
          gopayLoading: false
        })
      }
    } catch (err) {
      // eslint-disable-next-line no-unused-expressions
      // getDevice() === 'pc' && newwin.close()
      this.setState({
        createOrdering: false,
        gopayLoading: false
      })
      console.log(err)
    }
  }

  // 展示结果页面
  goPayResult = step => {
    this.setState({
      showPayResult: step || 'result',
      showFreeze: false,
      createOrdering: false,
      gopayError: '',
      dopayError: ''
    })
  }

  compSetProd = prod => {
    // console.log(prod, '-------000000000-------')
    this.setState({
      selectedPkg: prod,
      currentPayTypeOptions: prod.payTypeOptions
    })
  }

  // 重置是否兑换
  resetRedeem = () => {
    this.setState({
      hasRedeemed: false
    })
  }

  goBack = step => {
    this.setState({
      showPayResult: step || 'pkgSelect'
    })
  }

  newHandleCreateVipOrder = async (
    selectedPkg,
    payTypeOption,
    selectedCOU = {},
    inputInfo,
    payPrice
  ) => {
    // return orderCallback()
    const { vipInfo, initParams, createOrdering } = this.state
    const mod = getCookies('mod') || 'intl'
    const { modeLangObj } = this.props
    const lang = modeLangObj.get('lang')
    const abtest = vipInfo.groupCode
    let newWin = null
    const isGopay = payTypeOption.payType.toString() === '10021'
    const isMaster =
      payTypeOption.payType.toString() === '10010' ||
      payTypeOption.payType.toString() === '10009'
    if (createOrdering) return
    this.setState({
      createOrdering: true
    })
    const dopayT = Date.now()
    try {
      sendQosLog({
        diy_evt: 'norm_dopay_start',
        ce: window.globalVipCE
      })
    } catch (err) {
      console.log(err)
    }

    try {
      const doPayResult = await createVipOrder(
        selectedPkg,
        payTypeOption,
        {
          ...initParams,
          couponCode: selectedCOU.couponCode || '',
          _abtest: vipInfo.abtest ? `${vipInfo.abtest},${abtest}` : abtest
        },
        this.props.modeLangObj.toJS()
      )

      // console.log(doPayResult, '下单接口的返回=======')
      const addloggers = {
        popupDopay: {
          time: new Date(),
          uid: getUid(),
          abtest,
          doPayResult
        }
      }
      try {
        sendQosLog({
          diy_evt: 'norm_dopay_end',
          tm: Date.now() - dopayT,
          diy_success: 1,
          ce: window.globalVipCE,
          pay_type: payTypeOption.payType || ''
        })
      } catch (err) {
        console.log(err)
      }

      const loggers = localStorage.getItem('QiyiPlayerLogger')
      window.localStorage.setItem(
        'QiyiPlayerLogger',
        loggers + JSON.stringify(addloggers)
      )
      if (doPayResult.code === 'COUPON_STATUS_FROZEN') {
        this.setState({
          showFreeze: true,
          createOrdering: false
        })
        return
      }
      // let nextStep = PAGE_MAP.queryPaymentStep.name
      let openUrl
      let mobile
      const orderInfo = {
        typeName: selectedPkg.vipTypeName,
        vipOrder: doPayResult.vipOrder,
        order: doPayResult.order,
        name: selectedPkg.text3,
        currencySymbol: selectedPkg.currencySymbol,
        price: selectedPkg.price,
        originalPrice: selectedPkg.originalPrice,
        autorenewTip: selectedPkg.autorenewTip,
        detail: selectedPkg.detail,
        cashierType: initParams.cashierType,
        payType: payTypeOption.payType,
        payPrice
      }
      // return orderCallback(orderInfo, false)
      // 银行卡逻辑
      this.setState({ orderInfo, dopayError: '' })

      if (isMaster) {
        this.fetchInitVerify(orderInfo, inputInfo)
        // return
        // console.log('确定是银行卡支付')
        // this.fetchBankData(orderInfo, inputInfo)
      } else if (
        (isGopay && doPayResult.redirectUrl.match(/cashier\.iqiyi\.com/)) ||
        (isGopay && doPayResult.redirectUrl.match(/vip\/usermobile/))
      ) {
        orderInfo.order = decodeURIComponent(
          queryFromUrl(doPayResult.redirectUrl, 'order_code')
        )
        doPayResult.order = decodeURIComponent(
          queryFromUrl(doPayResult.redirectUrl, 'order_code')
        )
        mobile = window.atob(
          decodeURIComponent(queryFromUrl(doPayResult.redirectUrl, 'mobile')) ||
            ''
        )
        this.setState({
          showPayResult: 'gopay'
        })
        // gopay逻辑
        // return
        // this.fetchGopayData(orderInfo, inputInfo)
      } else if (doPayResult.redirectUrl) {
        openUrl = doPayResult.redirectUrl
        this.goPayResult()
      }

      if (openUrl) {
        newWin =
          getDevice() === 'pc' ? window.open('about:blank', '_blank') : window
        newWin.location.href = openUrl
        newWin.opener = null
      }

      this.setState({
        // step: nextStep,
        // payTypeOption,
        resultInfo: {
          vipOrder: doPayResult.vipOrder,
          order: doPayResult.order,
          mobile,
          platform: initParams.bossCode,
          lang,
          mod,
          timeZone: getTimeZone(mod)
        }
      })
    } catch (err) {
      sendQosLog({
        diy_evt: 'norm_dopay_end',
        tm: Date.now() - dopayT,
        diy_success: 0,
        ce: window.globalVipCE,
        pay_type: payTypeOption.payType || '',
        diy_msg: 'dopay Err ==>' + err
      })
      if (getDevice() === 'pc' && newWin) {
        newWin.close()
      }
      if (err.message.match('timeout')) {
        this.setErrorPop('timeout')
        this.setState({
          resultType: 'neterr',
          showPayResult: 'result'
        })
      } else {
        this.setState({
          // createPayTypeError: err.message || err.msg || '',
          dopayError: err.message || err.msg || ''
        })
      }
    }
  }

  handleLogin = () => {}

  handleLogout = () => {
    window.location.reload()
  }

  bindLoginRef = ref => {
    this.loginRef = ref
  }

  setResultSuccess = success => {
    this.setState({
      resultSuccess: success
    })
  }

  setHideFreeze = () => {
    this.setState({
      showFreeze: false
    })
  }

  clearError = () => {
    this.setState({
      gopayError: '',
      dopayError: ''
    })
  }

  hide = () => {
    const { resultSuccess } = this.state
    this.setState({
      popLoading: true,
      defaultVipModalInfo: undefined,
      vipInfo: null,
      // visible: false,
      // step: '',
      // isError: false,
      // errorType: '',
      currentPayTypeOptions: [],
      selectedPkg: {},
      showPayResult: 'pkgSelect',
      dopayError: '',
      gopayError: '',
      showFreeze: false,
      createOrdering: false,
      gopayLoading: false
    })
    // this.clearLocalState()
    // 购买成功之后刷新页面
    // eslint-disable-next-line
    delete window.isRefresh
    // eslint-disable-next-line no-unused-expressions
    resultSuccess && window.location.reload()
  }

  render() {
    const {
      cashierLangPkg,
      i18nShowCoupon,
      modeLangObj,
      pbParams,
      mytv,
      myvip
    } = this.props
    const vipLangPkg = this.props.vipLangPkg.toJS()
    const {
      vipInfo,
      orderInfo,
      error,
      initParams = {},
      showPayResult,
      resultType,
      resultInfo,
      popLoading,
      defaultVipModalInfo,
      showFreeze,
      dopayError,
      selectedPkg,
      currentPayTypeOptions,
      createOrdering,
      gopayLoading,
      gopayError,
      hasRedeemed,
      userCards,
      tvInfo,
      ugInfo,
      show3DSModal,
      DSResData
    } = this.state
    const { ptid } = this.context
    const lang = modeLangObj.get('lang')
    const mod = modeLangObj.get('mod')
    // const abtestQuery = initParams ? initParams.abtest : ''
    // const testRes = getAbtest()
    // const abtest = `page_cashier${testRes ? `,${testRes}` : ''}${
    //   abtestQuery ? ',' + abtestQuery : ''
    // }`

    const selectedPayTypeIndex = currentPayTypeOptions.findIndex(
      item => item.recommend === 1
    )
    let selectedPayType = currentPayTypeOptions[0]
    if (selectedPayTypeIndex > -1) {
      selectedPayType = currentPayTypeOptions[selectedPayTypeIndex]
    }
    const pbInfo = {
      cashier_type: mytv || myvip ? 'otp' : 'norm',
      abtest: vipInfo ? vipInfo.groupCode : '' || '',
      fc: initParams.fc || '',
      fv: initParams.fv || '',
      v_pid: selectedPkg ? selectedPkg.pid : '',
      v_prod: selectedPkg ? selectedPkg.productSetCode : '',
      pay_type: selectedPayType ? selectedPayType.payType : ''
    }
    const pbInfoStr = serialize(pbInfo) + '&bstp=56'
    const feedback = `//www.iq.com/intl-common/feedback.html?mod=${mod}&lang=${lang}&entranceId=iqiyi_intl_${
      getDevice() === 'mobile' ? 'h5' : 'pcw'
    }_vip_checkout&agentType=${hasAgentType()}&ptid=${ptid}&logType=14&subLogType=63`
    const timeoutErr = error && error.match('timeout')

    return (
      <>
        <Meta
          desc={cashierLangPkg.getIn(['cashier_html_description'])}
          title={cashierLangPkg.getIn(['upgrade_VIP'])}
        />
        <script
          type="text/javascript"
          async
          src="//security.iq.com/static/iq/v2/verifycenter/js/verifycenter.js"
        />
        <link
          rel="stylesheet"
          async
          type="text/css"
          href="//security.iq.com/static/iq/v2/verifycenter/css/verifycenter.css"
        />
        {error && (
          <VipPageContainer autoWidth>
            <TemplateErrorPop
              image={
                timeoutErr
                  ? '//www.iqiyipic.com/common/fix/global/api_network_error.png'
                  : '//www.iqiyipic.com/common/fix/global/api_oops.png'
              }
              title={
                timeoutErr
                  ? vipLangPkg.pcashier_error_network
                  : vipLangPkg.pcashier_error_errorOccur
              }
              btnText={
                timeoutErr
                  ? vipLangPkg.pcashier_error_retry
                  : vipLangPkg.pcashier_result_backPlan
              }
              btnClick={() => {
                this.setState({
                  error: '',
                  vipInfo: null
                })
                this.getData()
              }}
            />
          </VipPageContainer>
        )}
        {/* {!error (
          <VipPageContainer>
            <EmptyComponent />
          </VipPageContainer>
        )} */}
        {!error && (
          <VipPageContainer isMobile={isMobile}>
            {showPayResult === 'result' ? (
              <div className="new-vip-container">
                <NewPayResult
                  pbInfo={pbInfo}
                  pbInfoStr={pbInfoStr}
                  resultType={resultType}
                  resultInfo={resultInfo}
                  vipLangPkg={vipLangPkg}
                  feedbackUrl={feedback}
                  getVipData={this.getData}
                  goBack={this.goBack}
                  isRN={this.props.isRN}
                  setResultSuccess={this.setResultSuccess}
                />
              </div>
            ) : (
              ''
            )}

            <div
              className="new-vip-container"
              style={{
                display: `${showPayResult !== 'pkgSelect' ? 'none' : 'flex'}`
              }}
            >
              <TabContent
                lang={lang}
                mod={mod}
                pbInfo={pbInfo}
                pbInfoStr={pbInfoStr}
                vipInfo={vipInfo}
                userCards={userCards}
                orderInfo={orderInfo}
                hasCoupon={i18nShowCoupon}
                defaultVipModalInfo={defaultVipModalInfo}
                cashierLangPkg={cashierLangPkg.toJS()}
                popLoading={popLoading}
                dopayError={dopayError}
                showFreeze={showFreeze}
                setHideFreeze={this.setHideFreeze}
                createOrdering={createOrdering}
                getVipData={this.getData}
                handleCreateVipOrder={this.newHandleCreateVipOrder}
                compSetProd={this.compSetProd}
                goPayResult={this.goPayResult}
                resetRedeem={this.resetRedeem}
                hasRedeemed={hasRedeemed}
                tvInfo={tvInfo}
                ugInfo={ugInfo}
                tvvip={mytv || myvip}
              />
            </div>

            {showPayResult === 'gopay' ? (
              <Gopay
                pbInfo={pbInfo}
                vipLangPkg={vipLangPkg}
                orderInfo={orderInfo}
                mobile={resultInfo.mobile}
                gopayError={gopayError}
                gopayLoading={gopayLoading}
                goPayResult={this.goPayResult}
                hide={this.hide}
                fetchGopayData={this.fetchGopayData}
                clearError={this.clearError}
              />
            ) : (
              ''
            )}
          </VipPageContainer>
        )}
        <div className="user-wrapper">
          <UserWrap
            isVipButton="true"
            onRef={this.bindLoginRef}
            onLogin={this.handleLogin}
            onLogout={this.handleLogout}
          />
        </div>
        <TongJiPb
          rpage={
            mytv
              ? 'tv_cashier_otp_mytv'
              : myvip
              ? 'tv_cashier_otp_myvip'
              : 'cashier_norm'
          }
          pbShowParams={pbParams}
        />
        {show3DSModal ? (
          <DSModal
            isMobile={isMobile}
            vipLangPkg={vipLangPkg}
            confirmClk={() => this.modalConfirm(DSResData)}
            cancelClk={() => this.modalCancel()}
          />
        ) : (
          ''
        )}
      </>
    )
  }
}

Order.contextType = DeviceCtx
const mapStateToProps = state => ({
  vipLangPkg: state.getIn(['vip', 'vipLangPkg']),
  cashierLangPkg: state.getIn(['vip', 'cashierLangPkg']),
  modeLangObj: state.getIn(['language', 'modeLangObj']),
  userInfo: state.getIn(['user', 'userInfo'])
})

export default connect(mapStateToProps)(Order)
