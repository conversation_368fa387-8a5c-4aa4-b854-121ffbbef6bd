import React from 'react'
import { connect } from 'react-redux'
// import Router from 'next/router'
import { rebuildCommonUrl, platformId } from '@/kit/common'
import { getDevice, getMobileType } from '@/kit/device'
import { LANG_PKG } from '@/constants/interfaces'
import Meta from '@/components/common/Meta'
import History from '@/components/pages/personal/history/'
import Settings from '@/components/pages/personal/settings/'
import Collect from '@/components/pages/personal/collect'
import Translation from '@/components/pages/personal/translation'
import Reservation from '@/components/pages/personal/reservation'
import TongJiPb from '@/components/common/TongJiPb'
import PageTab from '@/components/common/PageTab'
import PersonalStyle from '@/style/personalStyle'
import $http from '@/kit/fetch'
import { isLogin } from '@/utils/userInfo'
import { fetchPersonalAction } from '@/store/reducers/personal/personal'
import { fetchConfigAction } from '@/store/reducers/commonConfig/commonConfig'

class Personal extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      isMobile: getDevice() === 'mobile',
      deviceType: getMobileType()
    }
  }

  componentDidMount() {
    const { dispatch } = this.props
    this.hasLogin()
    const isMobile = getDevice() === 'mobile'
    this.setState({
      isMobile
    })

    window.addEventListener('resize', () => {
      const { isMobile, deviceType } = this.state
      const newIsMobile = getDevice() === 'mobile'
      const newDeviceType = getMobileType()
      if (isMobile !== newIsMobile) {
        this.setState({
          isMobile: newIsMobile
        })
      }
      if (deviceType !== newDeviceType) {
        this.setState({
          deviceType: newDeviceType
        })
      }
    })
    // dispatch(fetchHistoryAction())
    dispatch(fetchConfigAction({ pageSt: 'personal_web' }))
  }

  static async getInitialProps({ ctx }) {
    const { isServer, query, store } = ctx
    const mod = query.mod || ''
    const type = encodeURIComponent(query.type) || ''
    await store.dispatch(fetchPersonalAction())
    const state = store.getState()
    let transLang = {}
    try {
      const param = {
        businessName: 'PCW_CROWDSOURCING_TRANSLATION',
        langCode: state.getIn(['language', 'modeLangObj', 'lang']),
        platformId: platformId()
      }
      const options = {}
      options.params = {
        ...param
      }
      const data = await $http(LANG_PKG, options)
      if (data.code === '0') {
        transLang = { ...data.data }
      }
    } catch (e) {
      console.log('request error')
      console.log(e)
    }
    const fromDelete = ctx.req.url.indexOf('delete') > -1 // type=settings&delete google注销页
    return { isServer, mod, type, transLang, fromDelete }
  }

  hasLogin = () => {
    if (this.props.fromDelete) {
      // type=settings&delete
      return
    }
    // 判断是否登录
    if (!isLogin()) {
      window.localStorage.setItem('QIYIRquest_url', window.location.href)
      setTimeout(() => {
        window.location.href = rebuildCommonUrl(`?type=login`)
      }, 100)
    }
  }

  render() {
    const { type, lang, transLang, modeLangObj, fromDelete } = this.props
    const LangPkg = lang.toJS()
    const modeObj = modeLangObj.toJS()
    const tabs = [
      {
        text: LangPkg.personal_settings,
        key: 'settings'
      },
      {
        text: LangPkg.watch_record,
        key: 'history'
      },
      {
        text: LangPkg.personal_favorite,
        key: 'favorite'
      },
      {
        text: LangPkg.reservation,
        key: 'reservation'
      },
      {
        text: LangPkg.subtitle_translation,
        key: 'translation'
      },
      {
        text: LangPkg.navigation_logout,
        key: 'logout'
      }
    ]
    const rpage = {
      favorite: 'favorite',
      translation: 'subtitle_translation',
      history: 'history',
      settings: 'settings',
      reservation: 'reservation'
    }
    return (
      <>
        <Meta
          desc={LangPkg.home_html_description}
          title={LangPkg.home_html_title}
        />
        <PersonalStyle>
          <PageTab type={type} tabs={tabs}>
            <History keys="history" />
            <Settings keys="settings" fromDelete={fromDelete} />
            <Collect keys="favorite" lang={modeObj.lang} />
            <Translation
              keys="translation"
              transLang={transLang}
              langObj={{ modeObj, langPkg: LangPkg }}
            />
            <Reservation keys="reservation" />
          </PageTab>
        </PersonalStyle>
        <TongJiPb rpage={rpage[type] || ''} />
      </>
    )
  }
}

const mapStateToProps = state => ({
  lang: state.getIn(['language', 'langPkg']),
  modeLangObj: state.getIn(['language', 'modeLangObj']),
  personalLangPkg: state.getIn(['personal', 'personalLangPkg']),
  userInfo: state.getIn(['user', 'userInfo'])
})

export default connect(mapStateToProps)(Personal)
