import React from 'react'
import PropTypes from 'prop-types'
import { connect } from 'react-redux'
import ErrorPage from '@/components/common/ErrorPage'
import TongJiPb from '@/components/common/TongJiPb'
import { fetchRecInfoAction } from '@/store/reducers/home/<USER>'
import RowBlock from '@/components/common/RowBlock'
import { sendBlockPb } from '@/utils/pingBack'
import { getCookies } from '@/kit/cookie'

class Error extends React.Component {
  static propTypes = {
    statusCode: PropTypes.number
  }

  static defaultProps = {
    statusCode: 200
  }

  constructor(props) {
    super(props)
    this.state = {
      isSendPb: false
    }
    this.rowBlockRef = React.createRef()
  }

  static getInitialProps({ ctx }) {
    const { res, err } = ctx
    res.setHeader('x-page-type', 'not-found')
    const statusCode = res ? res.statusCode : err ? err.statusCode : null

    return { statusCode }
  }

  async componentDidMount() {
    const { dispatch } = this.props
    const resId = getCookies('mod') === 'ntw' ? '31066249212' : '19518380412'
    window.addEventListener('scroll', () => {
      this.detectBlockShow()
    })

    await dispatch(
      fetchRecInfoAction({
        resId,
        size: 12,
        rpage: 404,
        type: 'hot',
        block: 'popular',
        position: 1,
        reqType: 'out'
      })
    )
  }

  detectBlockShow() {
    const current = this.rowBlockRef.current
    const isSendPb = this.state.isSendPb
    if (current) {
      const rect = current.getBoundingClientRect()
      if (window.innerHeight - rect.top > 50 && rect.bottom > 50) {
        if (!isSendPb) {
          sendBlockPb('popular', {
            rpage: '404'
          })
          this.setState({
            isSendPb: true
          })
        }
      } else {
        this.setState({
          isSendPb: false
        })
      }
    }
  }

  render() {
    const { statusCode, resInfo, langPkg, reason404 } = this.props
    const blockInfo = 'header,content,footer'
    const trueLangPkg = langPkg.toJS()

    const resData = resInfo ? resInfo.toJS() : undefined
    if (resData['hot']) {
      resData['hot'].rowName = trueLangPkg.popular_recommendation
      resData['hot'].rpage = '404'
    }

    const rowContainerStyle = {
      position: 'relative',
      fontSize: '0',
      overflow: 'visible',
      whiteSpace: 'nowrap'
    }

    return (
      <>
        <ErrorPage statusCode={statusCode || 200} />
        <div
          ref={this.rowBlockRef}
          className="row-container"
          style={rowContainerStyle}
        >
          <RowBlock
            imgType="1"
            hoverType="normal"
            data={resData['hot']}
            blockName="popular"
            id="popular"
            gtagCategory="404_popular"
          />
        </div>
        <TongJiPb rpage="404" blockInfo={blockInfo} />
        <div className="reason404" style={{ display: 'none' }}>
          {reason404}
        </div>
      </>
    )
  }
}

const mapStateToProps = state => ({
  resInfo: state.getIn(['home', 'resInfo']),
  langPkg: state.getIn(['language', 'langPkg']),
  dispatch: state.dispatch
})
export default connect(mapStateToProps)(Error)
