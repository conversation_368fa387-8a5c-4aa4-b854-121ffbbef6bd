import React from 'react'
import { connect } from 'react-redux'
import BankCard from '@/components/pages/vip/BankCard'
import VipPageContainer from '@/components/pages/vip/VipPageContainer'
import { locUrl } from '@/kit/common'
import Meta from '@/components/common/Meta'
import { queryFromUrl } from '@/kit/url'
import { getCookies } from '@/kit/cookie'
import { utmPbParams } from '@/utils/pingBack'
import { getAbtest } from '@/components/pages/vip/api'
import TemplateErrorPop from '@/components/pages/vip/Error'

class BankPay extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      error: '',
      abParams: {}
    }
  }

  componentDidMount() {
    const abParams = this.getAbParams()
    this.setState({ abParams })
    this.sendPb(abParams)
  }

  getAbParams() {
    const _url = locUrl()
    return {
      fc: queryFromUrl(_url, 'fc') || '',
      fv: getCookies('playFV') || queryFromUrl(_url, 'fv') || '',
      abtest: queryFromUrl(_url, 'abtest') || '',
      pid: queryFromUrl(_url, 'pid') || '',
      v_prod: queryFromUrl(_url, 'v_prod') || '',
      pay_type: queryFromUrl(_url, 'pay_type') || ''
    }
  }

  sendPb(initParams) {
    const varScript = document.createElement('script')
    const rpage = 'bank_card_fill'
    const { modeLangObj } = this.props
    const lang = modeLangObj.get('lang')
    const mod = modeLangObj.get('mod')
    const testRes = getAbtest()
    const abtest = `page_cashier${testRes ? `,${testRes}` : ''}${
      initParams.abtest ? ',' + initParams.abtest : ''
    }`
    const { vfm, encodeVfm } = utmPbParams()

    varScript.innerHTML = `
      window.intlPageInfo = window.intlPageInfo || {};
      intlPageInfo.i18n = 'global';
      intlPageInfo.pbInfos = {
        "rpage": "${rpage}",
        "lang": "${lang}",
        "mod": "${mod}"
      }
      intlPageInfo.pbInfos.pbShowParams = intlPageInfo.pbInfos.pbShowParams || {}
      intlPageInfo.pbInfos.pbClickParams = intlPageInfo.pbInfos.pbClickParams || {}
      intlPageInfo.pbInfos.pbShowParams.bstp = 56
      intlPageInfo.pbInfos.pbShowParams.abtest = '${abtest}'
      intlPageInfo.pbInfos.pbShowParams.fc = '${initParams.fc || ''}'
      intlPageInfo.pbInfos.pbShowParams.fv = '${initParams.fv || ''}'
      intlPageInfo.pbInfos.pbShowParams.v_pid = '${initParams.pid || ''}'
      intlPageInfo.pbInfos.pbShowParams.v_prod = '${initParams.v_prod || ''}'
      intlPageInfo.pbInfos.pbShowParams.pay_type = '${initParams.pay_type ||
        ''}'
      intlPageInfo.pbInfos.pbShowParams.cashier_type = 'norm'
      if("${vfm}") {
        intlPageInfo.pbInfos.pbShowParams.vfm = "${encodeVfm}"
        intlPageInfo.pbInfos.pbClickParams.vfm = "${vfm}"
      }
    `
    document.body.append(varScript)
  }

  setErrorPop = msg => {
    this.setState({
      error: msg
    })
  }

  render() {
    const cashierLangPkg = this.props.cashierLangPkg.toJS()
    const vipLangPkg = this.props.vipLangPkg.toJS()

    const _localUrl = locUrl()
    const pageInfo = {
      order: queryFromUrl(_localUrl, 'order') || '',
      vipOrder: queryFromUrl(_localUrl, 'vipOrder') || '',
      typeName:
        decodeURIComponent(queryFromUrl(_localUrl, 'vipTypeName')) || '',
      name: decodeURIComponent(queryFromUrl(_localUrl, 'text3')) || '',
      currencySymbol:
        decodeURIComponent(queryFromUrl(_localUrl, 'currencySymbol')) || '',
      price: queryFromUrl(_localUrl, 'price') || '',
      originalPrice: queryFromUrl(_localUrl, 'originalPrice') || '',
      autorenewTip:
        decodeURIComponent(queryFromUrl(_localUrl, 'autorenewTip')) || '',
      detail: decodeURIComponent(queryFromUrl(_localUrl, 'desc')) || '',
      cashierType:
        decodeURIComponent(queryFromUrl(_localUrl, 'cashierType')) || '',
      returnUrl: decodeURIComponent(queryFromUrl(_localUrl, 'returnUrl')) || ''
    }
    return (
      <>
        <Meta
          desc={cashierLangPkg.cashier_html_description}
          title={cashierLangPkg.upgrade_VIP}
        />
        <VipPageContainer autoWidth>
          {this.state.error ? (
            <TemplateErrorPop
              image="//www.iqiyipic.com/common/fix/global/api_network_error.png"
              title={vipLangPkg.pcashier_error_network}
              btnText={vipLangPkg.pcashier_error_retry}
              btnClick={() => {
                window.location.reload()
              }}
            />
          ) : (
            <BankCard
              pageInfo={pageInfo}
              setErrorPop={this.setErrorPop}
              abParams={this.state.abParams}
            />
          )}
        </VipPageContainer>
      </>
    )
  }
}

const mapStateToProps = state => {
  return {
    modeLangObj: state.getIn(['language', 'modeLangObj']),
    cashierLangPkg: state.getIn(['vip', 'cashierLangPkg']),
    vipLangPkg: state.getIn(['vip', 'vipLangPkg'])
  }
}
export default connect(mapStateToProps)(BankPay)
