import React from 'react'
import { connect } from 'react-redux'
import Meta from '@/components/common/Meta'
import TongJiPb from '@/components/common/TongJiPb'
import DownloadComp from '@/components/common/Download'

class Download extends React.Component {
  constructor(props) {
    super(props)
    this.state = {}
  }

  componentDidMount() {}

  static async getInitialProps({ ctx }) {
    const { query, isMobile } = ctx
    const mod = query.mod || ''

    return { mod, isMobile }
  }

  render() {
    const { langPkg, isMobile, isPad } = this.props
    const trueLangPkg = langPkg.toJS()

    return (
      <>
        <Meta
          desc={trueLangPkg.download_page_html_description}
          title={trueLangPkg.download_page_html_title}
          isShowAnimateCSS
        />
        <DownloadComp
          isPad={isPad}
          isMobile={isMobile}
          trueLangPkg={trueLangPkg}
        />
        <TongJiPb rpage="download" blockInfo="header,footer" />
      </>
    )
  }
}

const mapStateToProps = state => ({
  langPkg: state.getIn(['language', 'langPkg'])
})

export default connect(mapStateToProps)(Download)
