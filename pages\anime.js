// 儿童页面 复用主页相关组件
import React from 'react'
import { connect } from 'react-redux'
import ChannelWrapper from '@/style/channelStyle'
import FocusImg from '@/components/common/FocusImg'
import { getDevice, getMobileType } from '@/kit/device'
import { gtagReportConversion } from '@/utils/gtag'
import Meta from '@/components/common/Meta'
import TongJiPb from '@/components/common/TongJiPb'
import { renderRowContainer } from '@/utils/renderRowContainer'
import {
  fetchConfigAction,
  setConfigAjaxAction
} from '@/store/reducers/commonConfig/commonConfig'
import { getCard } from '@/kit/commonConfig'
import { Loading } from '@/constants/style'
import { LoadingStyle } from '@/components/pages/home/<USER>/loadingStyle'
import throttle from 'lodash.throttle'
import { getWebPreData } from '@/utils/commonUtil'
import { handleCommonConfigAjax } from '@/store/sagas/commonConfig/handleData'
import { webMarkConfig } from '@/utils/performance/config'
import FooterFixed from '@/components/common/FooterFixed'
import { webpreData, getChannelPreDataUrl } from '@/utils/esrRender'
import HomeSchema from '@/components/pages/home/<USER>'
import OrganizationSchema from '@/components/pages/home/<USER>'
import BreadcrumbListSchema from '@/components/pages/home/<USER>'

class Anime extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      isMobile: getDevice() === 'mobile',
      deviceType: getMobileType(),
      nextPageNum: 1
    }
    this.containerRef = React.createRef()
  }

  componentDidMount() {
    const isMobile = getDevice() === 'mobile'

    this.getPreData(isMobile)
    this.getCommonConfigData()

    this.setState({
      isMobile
    })
    window.addEventListener('resize', () => {
      const { isMobile, deviceType } = this.state
      const newIsMobile = getDevice() === 'mobile'
      const newDeviceType = getMobileType()
      if (isMobile !== newIsMobile) {
        this.setState({
          isMobile: newIsMobile
        })
      }
      if (deviceType !== newDeviceType) {
        this.setState({
          deviceType: newDeviceType
        })
      }
    })

    if (isMobile) {
      this.setScrollListener()
    }

    gtagReportConversion('AW-339868652/iuHWCNe8yPECEOz3h6IB')
  }

  // 提前获得数据如热门推荐
  async getPreData(isMobile) {
    const params = {
      rpage: 'anime',
      isMobile
    }

    const webPreData = getWebPreData('anime')
    const hotCard = webPreData?.hotCard
    if (hotCard) {
      const hotCardData = handleCommonConfigAjax(hotCard, 'anime_web', params)
      if (hotCardData.data && hotCardData.data.isAjaxEnd) {
        hotCardData.data.isAjaxEnd = false
      }
      await this.props.dispatch(setConfigAjaxAction(hotCardData))
      setTimeout(() => {
        window.performance.mark(webMarkConfig?.getPreChannelData?.end)
      }, 200)
    }
  }

  setScrollListener() {
    // mobile 下在这里请求pcw-common接口
    window.addEventListener(
      'scroll',
      throttle(
        () => {
          const { hasMore } = this.props
          if (this.containerRef && this.containerRef.current) {
            const rect = this.containerRef.current.getBoundingClientRect()
            if (rect.bottom - 50 < window.innerHeight && hasMore) {
              this.getCommonConfigData()
            }
          }
        },
        1500,
        { leading: true }
      )
    )
  }

  async getCommonConfigData() {
    const { isMobile } = this.state
    const { requestDate, dispatch } = this.props
    if (this.isFetching) {
      return
    }
    this.isFetching = true
    const {
      data: { nextUrlParams, hasMore }
    } = await dispatch(
      fetchConfigAction({
        pageSt: 'anime_web',
        _catch: {},
        channelId: 4,
        requestDate,
        pgSize: 4
      })
    )

    if (nextUrlParams && nextUrlParams.pg_num && +nextUrlParams.pg_num === 2) {
      setTimeout(() => {
        window.performance.mark(webMarkConfig?.noGetPreChannelData?.end)
      }, 500)
    }

    this.setState({
      nextPageNum: +nextUrlParams?.pg_num
    })

    setTimeout(() => {
      this.isFetching = false

      if (nextUrlParams && nextUrlParams.pg_num && nextUrlParams.pg_num <= 3) {
        if (!isMobile && hasMore) {
          this.getCommonConfigData()
        }
      } else {
        this.setScrollListener()
      }
    }, 500)
  }

  static async getInitialProps({ ctx }) {
    const { isServer } = ctx
    const requestDate = new Date().getTime() // sid所需要的时间戳

    return {
      isServer,
      requestDate
    }
  }

  render() {
    const { isMobile, deviceType, nextPageNum } = this.state
    const {
      langPkg,
      curWebData,
      isSeo,
      hasMore,
      pcwCommonIsAjaxEnd,
      modeCode,
      langCode,
      chnId,
      focusCardId,
      firstCardId
    } = this.props

    const endFocusNum = isSeo ? 100 : global.window ? 100 : 1

    const blockInfo =
      'header,focus_banner,popular,child_recommend,latest_release,anime_recommend,footer,anime_recommend_info'
    const trueLangPkg = langPkg.toJS()
    const trueCurWebData = curWebData && curWebData.toJS()
    const { cards = [], resources, sourceMap } = trueCurWebData

    const pcwFocusBanner = getCard('pcw_focus_banner', cards)[0] || []
    let focusImgInfo = pcwFocusBanner['blocks'] || []
    focusImgInfo = focusImgInfo.slice(0, endFocusNum)

    const rowContainerStyle = {
      position: 'relative',
      fontSize: '0',
      overflow: 'visible',
      whiteSpace: 'nowrap',
      minHeight: '60vh'
    }

    const channelPreDataUrl = getChannelPreDataUrl({
      modeCode,
      langCode,
      pageSt: 'anime_web',
      chnId,
      focusCardId,
      firstCardId
    })

    return (
      <>
        <Meta
          desc={trueLangPkg.anime_html_description}
          title={trueLangPkg.anime_html_title}
          brand={trueLangPkg.brand_html}
          commonKeywords={trueLangPkg.common_keywords}
          seriesKeywords={trueLangPkg.series_keywords}
          brandKeywords={trueLangPkg.brand_keywords}
          path="anime"
        />
        <OrganizationSchema langCode={langCode} />
        <HomeSchema langCode={langCode} />
        <BreadcrumbListSchema
          path="/anime"
          langPkg={trueLangPkg}
          langCode={langCode}
        />
        <div style={{ minHeight: '100vh' }}>
          <FocusImg
            focusImgInfo={focusImgInfo}
            isMobile={isMobile}
            deviceType={deviceType}
            rpage="anime"
            gtagCategory="anime_TopBanner"
            isAjaxEnd={pcwCommonIsAjaxEnd}
          />
          {webpreData}
          {channelPreDataUrl}
          <ChannelWrapper ref={this.containerRef} style={{ minHeight: '60vh' }}>
            <div className="row-container" style={rowContainerStyle}>
              {renderRowContainer({
                rpage: 'anime',
                resources,
                intlDataColNew: cards,
                sourceMap,
                isSeo
              })}
              <FooterFixed hasMore={hasMore} nextPageNum={nextPageNum} />
              {hasMore && pcwCommonIsAjaxEnd && (
                <LoadingStyle>
                  <Loading width="24px" />
                </LoadingStyle>
              )}
            </div>
          </ChannelWrapper>
          <TongJiPb rpage="anime" blockInfo={blockInfo} />
        </div>
      </>
    )
  }
}

const mapStateToProps = state => ({
  langPkg: state.getIn(['language', 'langPkg']),
  curWebData: state.getIn(['commonConfig', 'anime_web']),
  hasMore: state.getIn(['commonConfig', 'anime_web', 'hasMore']),
  pcwCommonIsAjaxEnd: state.getIn(['commonConfig', 'anime_web', 'isAjaxEnd']),
  dispatch: state.dispatch
})
export default connect(mapStateToProps)(Anime)
