import React from 'react'
import { connect } from 'react-redux'
import <PERSON><PERSON><PERSON><PERSON>tList from '@/components/common/PicTextList'
import $http from '@/kit/fetch'
import { getCookies } from '@/kit/cookie'
import { platformId } from '@/kit/common'
import { FILMLIB_LIST } from '@/constants/interfaces'
import { handleVideoFilter } from '@/store/sagas/filmLibrary/handleData'
import DeviceCtx from '@/components/context'
import { sendBlockPb } from '@/utils/pingBack'
import { Loading } from '@/constants/style'
import { LoadingStyle } from '@/components/pages/home/<USER>/loadingStyle'
import { supportUrlPbMod } from '@/utils/video'

import { VideoFilterWrapper } from './style'

const MAX_VIDEO_COUNT = 12
const DEFAULT_DATA = {
  videos: [],
  block: 'filter_result',
  rpage: '',
  isInit: true // 判断初始化
}
const sMode = {
  s11: 11,
  s4: 4
}

class <PERSON>Filter extends React.Component {
  static contextType = DeviceCtx

  constructor() {
    super()
    this.state = {
      data: DEFAULT_DATA,
      pn: 1,
      isLoading: false
    }
    this.dontFetch = false
    this.videoRef = React.createRef()
    this.scrollFn = () => {}
  }

  componentDidMount() {
    const { checks, chnid } = this.props
    this.scrollFn = this.initScroll()
    window.addEventListener('scroll', this.scrollFn)
    this.fetchOnceData({ chnId: chnid }, checks)
  }

  componentDidUpdate(preProps, preState) {
    this.handleUpdateFetch(preProps)
    if (
      this.state.data.videos.length &&
      preState.data.videos.length !== this.state.data.videos.length
    ) {
      this.handleCard21Pb() // 切换标签筛选视频长度变化收口
    }
  }

  handleUpdateFetch = preProps => {
    const { checks, chnid } = this.props
    const { checks: _checks, chnid: _chnid } = preProps
    if (chnid !== _chnid) {
      // 切换频道
      this.fetchOnceData()
      return
    }
    const tagValues = this.handleTagValues(checks)
    const _tagValues = this.handleTagValues(_checks)

    if (tagValues !== _tagValues) {
      // 切换标签
      this.fetchOnceData()
    }
  }

  componentWillUnmount() {
    window.removeEventListener('scroll', this.scrollFn)
  }

  initScroll = () => {
    // 初始化滚动事件
    const handleScrollFetch = this.handleScrollFetch()
    return () => {
      handleScrollFetch()
      this.handleCard21Pb()
    }
  }

  handleSTag = check => {
    // pingback.s_tag
    return check
      .reduce((t, check) => {
        t += `;${check.class}:${check.text}`
        return t
      }, '')
      .slice(1)
  }

  handleSMode = check => {
    // pingback.s_mode
    return check.reduce((t, check) => {
      if (!t) {
        t = sMode[check.type]
      }
      return t
    }, '')
  }

  // handleLabel21Pb = _checks => {
  //   // 标签t21
  //   const { chnid } = this.props
  //   const param = {
  //     rpage: `explore_library_${chnid}`,
  //     tjPb: {
  //       bstp: 18,
  //       s_mode: this.handleSMode(_checks)
  //     }
  //   }
  //   param.tjPb.s_tag = this.handleSTag(_checks)
  //   sendBlockPb('filter_result', param)
  // }

  fetchOnceData = async (param = {}) => {
    const { checks } = this.props // 在mount和update中执行props都是最新的
    // 切换标签、切换频道，一次性更新data
    this.setState({ data: DEFAULT_DATA })
    const data = await this.fetchVidoFilter(param, checks)
    if (data) {
      this.dontFetch = !data.videos.length
      this.setState({ data })
      // this.handleLabel21Pb(checks)
      if (!data.videos.length) {
        // 无结果时的t21
        this.handleNoDataPb()
      }
    }
    this.setState({ pn: 1 }) // 页码归零
  }

  handleNoDataPb = () => {
    const { chnid, checks } = this.props
    const param = {
      rpage: `explore_library_${chnid}`,
      tjPb: {
        bstp: 18,
        s_mode: this.handleSMode(checks),
        s_tag: this.handleSTag(checks)
      }
    }
    sendBlockPb('filter_result', param)
  }

  handleTagValues = check => {
    if (!check.length) return null // 初始checks为[]
    return check
      .reduce((str, ch) => {
        if (ch.type) {
          str += ',' + ch.type
        }
        return str
      }, '')
      .slice(1)
  }

  fetchVidoFilter = async (params = {}) => {
    const { checks } = this.props
    const { isMobile } = this.context
    const { chnid } = this.props
    this.setState({ isLoading: true })
    const startTime = Date.now()

    try {
      const modeCode = getCookies('mod') || 'intl'
      const _data = await $http(FILMLIB_LIST, {
        params: {
          deviceId: getCookies('QC005'),
          platformId: platformId(),
          langCode: getCookies('lang') || 'en_us',
          modeCode,
          pn: 1, // 默认为1
          ps: MAX_VIDEO_COUNT,
          chnId: chnid,
          tagValues: this.handleTagValues(checks),
          ...params
        }
      })
      if (_data && _data.code === '0') {
        const _videoData = handleVideoFilter(_data.data, {
          block: 'filter_result',
          rpage: `explore_library_${chnid}`,
          s_ct: Date.now() - startTime,
          s_page: params.pn || 1,
          s_source: isMobile ? 'explore-lib' : 'pcw_lib',
          s_tag: this.handleSTag(checks),
          s_mode: this.handleSMode(checks),
          pbMsg: supportUrlPbMod(modeCode),
          isMobile,
          modeCode
        })
        return _videoData
      }
      return false
    } catch (e) {
      console.log(e)
      return false // eslint强迫要加
    } finally {
      this.setState({ isLoading: false })
    }
  }

  fetchMoreData = async () => {
    // 数据叠加请求
    const { pn } = this.state
    // 追加数据
    const _data = await this.fetchVidoFilter({
      pn: pn + 1
    })
    if (_data) {
      const { videos: _videos } = _data
      this.setState(pre => {
        const { data } = pre
        const { videos } = data
        const newArr = [...videos, ..._videos]
        return {
          data: {
            ...data,
            videos: [...newArr]
          },
          pn: pn + 1
        }
      })
      this.dontFetch = !_videos.length // 没数据不再请求
      // this.dontFetch = _videos.length < MAX_VIDEO_COUNT // 不满MAX_VIDEO_COUNT不再请求
    }
  }

  handleScrollFetch = () => {
    let isFetch = false
    let preTop = document.documentElement.scrollTop || document.body.scrollTop
    return async () => {
      const { current } = this.videoRef

      if (current) {
        const rect = current.getBoundingClientRect()
        const scrollTop =
          document.documentElement.scrollTop || document.body.scrollTop
        if (!this.dontFetch) {
          // 没数据时停止请求
          if (preTop < scrollTop && rect.bottom - 50 < window.innerHeight) {
            // 当tabItem底部露出时
            if (!isFetch) {
              isFetch = true
              await this.fetchMoreData()
              isFetch = false
            }
          }
          preTop = scrollTop
        }
      }
    }
  }

  handleCard21Pb = () => {
    const { data } = this.state
    const { chnid, checks } = this.props
    const card = document.getElementsByClassName('img-type-1')
    const cards = [].slice.call(card, 0)

    const { isMobile } = this.context
    cards.forEach((card, inx) => {
      const rect = card.getBoundingClientRect()
      if (
        !card.getAttribute('data-isSendPb') &&
        rect.bottom > 0 &&
        rect.top <= window.innerHeight
      ) {
        card.setAttribute('data-isSendPb', 1)
        const { videos } = data
        const param = {
          rpage: `explore_library_${chnid}`,
          tjPb: {
            bstp: 18,
            rseat: videos[inx].pingback.index,
            s_tag: this.handleSTag(checks),
            r: videos[inx].id,
            s_bkt: videos[inx].pingback.s_bkt,
            s_e: videos[inx].pingback.s_e,
            s_page: videos[inx].pingback.s_page,
            s_mode: this.handleSMode(checks),
            s_source: isMobile ? 'explore-lib' : 'pcw_lib',
            s_ct: videos[inx].pingback.s_ct,
            s_st: videos[inx].pingback.s_st,
            s_docids: videos[inx].id
          }
        }
        sendBlockPb('filter_result', param)
      }
    })
  }

  render() {
    const { data, isLoading } = this.state
    const { noDataLangPkg, deviceClassName, mod } = this.props

    return (
      <VideoFilterWrapper className={deviceClassName}>
        {data.videos.length ? (
          <div className="video-filter-wrapper" ref={this.videoRef}>
            <PicTextList
              imgType="1"
              hoverType="normal"
              showPullDown={() => {}}
              hidePullDown={() => {}}
              // pullDownId={}
              lazyLoad={false}
              // showLazyLoad={}
              // isVip={}
              // gtagCategory='jilupian_TopBanner'
              // showNum={}
              data={data}
              mod={mod}
            />
          </div>
        ) : !isLoading && !data.isInit ? (
          <div className="video-filter-noData">
            <div className="empty-history">
              <img
                src="//www.iqiyipic.com/common/fix/empty_history.png"
                alt={noDataLangPkg}
              />
              <p className="empty-title">{noDataLangPkg}</p>
            </div>
          </div>
        ) : null}
        {isLoading ? (
          <LoadingStyle>
            <Loading />
          </LoadingStyle>
        ) : null}
      </VideoFilterWrapper>
    )
  }
}

const mapStateToProps = state => ({
  noDataLangPkg: state.getIn(['language', 'langPkg', 'library_no_results']),
  mod: state.getIn(['language', 'modeLangObj', 'mod'])
})
export default connect(mapStateToProps)(VideoFilter)
