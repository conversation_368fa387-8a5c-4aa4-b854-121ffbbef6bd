import React from 'react'
import { connect } from 'react-redux'
import debounce from 'lodash.debounce'
import { imgUrl } from '@/utils/imgUrl'
import { DetailArrowIcon } from '@/components/svgs'
import getUpdate from '@/utils/getUpdate'
import sendGtag from '@/utils/gtag'
import DeviceCtx from '@/components/context'
import { curlMod, isImage } from '@/kit/common'
import { getDevice } from '@/kit/device'
import $http from '@/kit/fetch'
import { ISCOLLECTION } from '@/constants/interfaces'
import { sendBlockPb } from '@/utils/pingBack'
import { rpageObj, setS2S3S4 } from '@/utils/common'
import { isLogin } from '@/utils/userInfo'
import { callAppFun } from '@/utils/videoCallApp'
import {
  GridCardVipIcon,
  GridCardTvodIcon,
  GridCardLiveIcon
} from '@/constants/style'
import PicTextUnitWrapper from './style'
import WideDetails from '../WideDetails'
import Mask from '../Mask'
import MarkIcon from '../MarkIcon'

const setFirstDefaultImg = imgType => {
  let imgSrc
  const isMobile = getDevice() === 'mobile'
  if (imgType === '2') {
    imgSrc = `//www.iqiyipic.com/lequ/20220511/${
      !isMobile ? '1920-transverce' : 'H5-transverce'
    }.png`
  } else {
    imgSrc = `//www.iqiyipic.com/lequ/20220511/${
      !isMobile ? '1920-vertical' : 'H5-vertical'
    }.png`
  }
  return imgSrc
}

class PicTextUnit extends React.Component {
  static contextType = DeviceCtx

  constructor(props) {
    super(props)
    const firstImg = setFirstDefaultImg(props.imgType)
    const tempState = {
      hoverClass: 'normal',
      horizonStyle: {
        opacity: 0
      },
      // imgSrc: '//www.iqiyipic.com/common/fix/global/bg_51_40_x1.png',
      imgSrc: firstImg,
      showDetail: false,
      isShowWideDetailBg: false,
      showingWide: false,
      updateInfo: '',
      videoOrder: '',
      alreadyShowDetail: false,
      isAdded: null,
      sentShowPb: false,
      lazyLoaded: false, // 懒加载是否完毕
      markCtag: ''
    }
    this.state = tempState
    this.unitRef = React.createRef()
    this.handleMEnter = this.handleMEnter.bind(this)
    this.handleMLeave = this.handleMLeave.bind(this)
  }

  componentDidMount() {
    const { langPkg, item, lazyLoad, id, hoverType } = this.props
    const { updateDesc, videoOrder } = item
    updateDesc.qipuId = item.id // 针对播单做集数单独过滤
    const trueLang = langPkg.toJS()
    let order = ''
    if (videoOrder && langPkg.toJS().episode) {
      order = langPkg.toJS().episode.replace('{}', videoOrder)
    }
    this.setDefaultImg()
    this.setState({
      updateInfo: getUpdate(updateDesc, {
        updateTo: trueLang.update_episode,
        count: trueLang.total_episode,
        termTpisode: trueLang.term_episode
      }),
      videoOrder: order
    })
    if (hoverType === 'history') {
      this.showPic()
    }
    if (!lazyLoad && id > 7) {
      this.showPic()
    }
  }

  componentDidUpdate(prevProps) {
    if (prevProps.showLazyLoad !== this.props.showLazyLoad) {
      if (this.props.lazyLoad && this.props.showLazyLoad) {
        this.showPic()
      }
    }

    if (prevProps.showNum < this.props.showNum) {
      if (this.props.lazyLoad) {
        this.showPic()
      }
    }
  }

  getSlideDomData() {
    try {
      const containerW = this.unitRef.current.parentNode.offsetWidth
      const unitW = this.unitRef.current.parentNode.childNodes[0].offsetWidth
      const curH = this.unitRef.current.childNodes[0].childNodes[0]
        .childNodes[0].offsetHeight
      const curW = this.unitRef.current.childNodes[0].childNodes[0]
        .childNodes[0].offsetWidth
      const len = this.unitRef.current.parentNode.childNodes.length
      const num = Math.ceil(containerW / unitW)
      return { containerW, unitW, len, num, curH, curW }
    } catch (err) {
      console.log(err)
    }
    return {}
  }

  setDefaultImg() {
    const clientWidth = document.documentElement.clientWidth
    // let imgSrc = '//www.iqiyipic.com/common/fix/global/bg_51_40_x1.png'
    // const { imgType } = this.props
    // if (clientWidth <= 767) {
    //   imgSrc = '//www.iqiyipic.com/common/fix/global/bg_38_30_x1.png'
    // } else if (clientWidth <= 1023) {
    //   imgSrc = '//www.iqiyipic.com/common/fix/global/bg_56_44_x1.png'
    //   if (imgType === '2') {
    //     imgSrc = '//www.iqiyipic.com/common/fix/global/bg_51_40_x1.png'
    //   }
    // } else if (clientWidth >= 1680) {
    //   imgSrc = '//www.iqiyipic.com/common/fix/global/bg_84_66_x1.png'
    // }
    // this.setState({
    //   imgSrc
    // })
    let imgSrc
    const { imgType } = this.props
    if (imgType === '2') {
      imgSrc = `//www.iqiyipic.com/lequ/20210401/${
        clientWidth > 1023 ? '1920-transverce' : 'H5-transverce'
      }.png`
    } else {
      imgSrc = `//www.iqiyipic.com/lequ/20210401/${
        clientWidth > 1023 ? '1920-vertical' : 'H5-vertical'
      }.png`
    }
    this.setState({
      imgSrc
    })
  }

  isCollection = item => {
    const { id: qipiId } = item
    const { hoverType } = this.props
    const collParam = {
      mod: curlMod() || 'intl',
      // subType: handleSubType(sourceCode, qipiId),
      subKey: qipiId,
      agent_type: 426,
      channelId: item.chnId
    }
    debounce(async () => {
      if (isLogin()) {
        if (hoverType !== 'mask') {
          // 排除继续看
          // 是否收藏
          const param = {}
          param.params = collParam
          const data = await $http(ISCOLLECTION, param)
          if (data.code === 'A00001') {
            this.setState({ isAdded: false }, () => this.sendPb())
          } else if (data.code === 'A00000') {
            this.setState({ isAdded: true }, () => this.sendPb())
          }
        }
      } else {
        this.setState({ isAdded: false })
      }
    }, 500)()
  }

  changeIsAdded = isAdded => {
    // 还有更好解决方案，但是已上线改太多不保险....
    this.setState({ isAdded })
  }

  sendPb = () => {
    const { sentShowPb } = this.state
    const { pathname } = this.context
    const rpage = rpageObj[pathname] ? rpageObj[pathname] : '404'
    if (!sentShowPb) {
      sendBlockPb('hover', {
        rpage
      })
      this.setState({ sentShowPb: true })
    }
  }

  showPic() {
    let src = ''
    const { lazyLoaded } = this.state
    const { item, id, showNum, showLazyLoad, lazyLoad, hoverType } = this.props
    const albumPic = item.albumPic
    const albumPicBak = item.albumPicBak
    if (lazyLoad) {
      if (lazyLoaded || !showLazyLoad) {
        return
      }

      if (showNum && +id >= showNum) {
        return
      }
    }

    if (isImage(albumPic)) {
      if (this.props.imgType === '1' || this.props.imgType === '3') {
        if (/\/common\//.test(albumPic)) {
          src = albumPic // 如果图片带有common链接，则不拼图直接展示
        } else {
          src = imgUrl(albumPic, '_260_360')
        }
      } else if (/\/common\//.test(albumPic)) {
        src = imgUrl(albumPicBak, '_480_270') // 如果图片带有common链接，则横图拼albumPicBak(albumPic)做展示，不用resPic(albumPic)
      } else if (hoverType === 'history' && /_480_270/.test(albumPic)) {
        src = imgUrl(albumPic, '')
      } else {
        src = imgUrl(albumPic, '_480_270')
      }
      const temp = new Image()
      temp.src = src
      temp.onload = () => {
        this.setState({
          lazyLoaded: true,
          imgSrc: src
        })
      }
    }
  }

  // 只有影人页面才发送
  hoverSendIdPb() {
    const { pbParamObj = {} } = this.props
    const { rpage, block } = pbParamObj
    sendBlockPb(block, {
      rpage
    })
  }

  handleMEnter() {
    const {
      showPullDown,
      id,
      movement,
      hoverType,
      item,
      isHoverSendIdPb
    } = this.props
    if (isHoverSendIdPb) {
      this.hoverSendIdPb()
    }
    this.setState({ showDetail: true, isShowWideDetailBg: true })
    setTimeout(() => {
      const { showDetail, alreadyShowDetail } = this.state
      if (showDetail) {
        if (showPullDown) {
          showPullDown(id, alreadyShowDetail)
        }
        if (!alreadyShowDetail) {
          this.setState({ alreadyShowDetail: true })
        }

        if (hoverType === 'horizontal') {
          if (document.body.offsetWidth > 1024) {
            const obj = this.getSlideDomData()
            movement(id, obj.unitW, obj.num)
            this.setState({
              horizonStyle: {
                opacity: 1,
                width: obj.unitW + obj.curW + 'px',
                height: obj.curH + 'px'
              },
              updateInfoStyle: {
                opacity: 0
              },
              showingWide: true
            })
          } else {
            this.setState({
              hoverClass: 'normal'
            })
          }
        }
      }
    }, 500)
    // 收藏参数
    this.isCollection(item)
  }

  handleMLeave() {
    const { hoverType } = this.props
    this.setState({ showDetail: false })
    setTimeout(() => {
      if (hoverType === 'horizontal' && document.body.offsetWidth > 1024) {
        const { moveBack } = this.props
        moveBack()
        this.setState({
          horizonStyle: {
            opacity: 0
          },
          updateInfoStyle: {
            opacity: 1
          },
          showingWide: false
        })
      }
    }, 500)
  }

  handleSendGtag(e, item) {
    const { mobileClick } = this.props
    if (mobileClick === 'callApp' && getDevice() === 'mobile') {
      e.preventDefault()
      const { pathname } = this.context
      callAppFun({
        url: window.location.href,
        tvId: item.tvId,
        albumId: item.albumId,
        mod: curlMod() || 'intl',
        rpage: rpageObj[pathname] ? rpageObj[pathname] : '404',
        block: item.pingback.block,
        rseat: item.pingback.index
      })
    } else {
      sendGtag(e.currentTarget)
    }
  }

  handleNameAndUpdateRender() {
    const { updateInfo, videoOrder, hoverClass } = this.state
    const { item, playControl, id, hoverType } = this.props
    // const trueLang = langPkg.toJS()
    const { tvYear, chnId, name, vv } = item
    const { isMobile } = this.context
    let _hoverClass = hoverClass
    switch (hoverType) {
      case 'horizontal':
        _hoverClass = 'horizontal'
        break
      case 'vertical':
        _hoverClass = 'vertical'
        break
      case 'mask':
        _hoverClass = 'mask'
        break
      default:
        _hoverClass = 'normal'
    }
    const pullDownArrow =
      _hoverClass === 'vertical' ? (
        <div className="pull-down-arrow">
          <DetailArrowIcon />
        </div>
      ) : null
    let _hoverType = hoverType
    if (vv) _hoverType = 'hotPlay'

    switch (_hoverType) {
      case 'mask':
        return (
          <div className="text-box">
            <div
              className="title"
              style={{ color: isMobile && playControl ? '#999' : '' }}
            >
              {chnId === 1
                ? name
                : chnId === 6
                ? name + ' ' + tvYear
                : name + ' ' + videoOrder}
            </div>
          </div>
        )
      case 'hotPlay':
        return (
          <div className="text-box rank-list">
            <img
              alt={name}
              src={`//www.iqiyipic.com/common/fix/global/hot_${id + 1}@2x.png`}
            />
            <div className="rank-block">
              <p className="title">{name}</p>
              <p className="subTitle">{updateInfo}</p>
            </div>
            {pullDownArrow}
          </div>
        )
      case 'history': {
        return (
          <div className="text-box">
            <p
              className="title"
              rseat={id}
              data-pb={`block=continue_watching&r=${item.id}&a=title`}
            >
              {name}
            </p>
          </div>
        )
      }
      default:
        return (
          <div className="text-box">
            <p className="title">{name}</p>
            {/* <p className="subTitle">{updateInfo}</p> */}
            {pullDownArrow}
          </div>
        )
    }
  }

  handleRightTopIconRender = () => {
    const { item, isVip, langPkg, currentLang } = this.props
    const { vipInfo, payMark, isLive } = item
    const trueLang = langPkg.toJS()

    if (isLive) {
      // 直播视频无vip，测试数据有误，故做兼容
      return <GridCardLiveIcon>{trueLang.live_logo}</GridCardLiveIcon>
    }
    if (vipInfo?.payMark === 'PAY_ON_DEMAND_MARK' && vipInfo?.isTvod === 1) {
      let ratio = 3
      switch (currentLang) {
        case 'zh_cn':
          ratio = 1
          break
        case 'zh_tw':
          ratio = 2
          break
        default:
          break
      }
      return <GridCardTvodIcon ratio={ratio} />
    }
    if (isVip || (vipInfo && vipInfo.payMark === 'VIP_MARK') || payMark === 1) {
      return <GridCardVipIcon />
    }
    return null
  }

  getAlt = item => {
    const { pathname } = this.context

    const { langPkg } = this.props
    const pkg = langPkg.toJS()
    const name = item['name'] || ''
    let publishTime =
      (item['updateDesc']['publishTime'] || '').slice(0, 4) || ''

    publishTime = +publishTime ? `(${publishTime})` : ''

    let alt = `${pkg.watch_online} ${name} ${
      publishTime ? publishTime + ' ' : ''
    }${pkg.subtitle_language} ${pkg.dubbing_language}`

    if (
      rpageObj[pathname] === 'home' ||
      rpageObj[pathname] === 'thaiseries' ||
      rpageObj[pathname] === 'kdrama'
    ) {
      alt += ' ' + pkg['brand_html']
    } else if (rpageObj[pathname] !== 'play') {
      let brandHtml

      switch (+item.chnId) {
        case 1:
          brandHtml = pkg['navigation_movie']
          break
        case 2:
          brandHtml = pkg['navigation_drama']
          break
        case 4:
          brandHtml = pkg['navigation_anime']
          break
        case 6:
          brandHtml = pkg['navigation_variety_show']
          break
        default:
          brandHtml = pkg['brand_html']
          break
      }
      alt += ' ' + brandHtml
    }
    return alt
  }

  // 角标id
  setMarkCtag = ids => {
    this.setState({
      markCtag: ids
    })
  }

  render() {
    const {
      item,
      imgType,
      hoverType,
      pullDownId,
      id,
      styleObj,
      // isVip,
      gtagCategory,
      pbParam,
      pbParamObj = {},
      lazyLoad,
      // langPkg,
      pageType,
      markIcons
    } = this.props
    const {
      name,
      url,
      playTimePencent,
      // playPlan,
      // tvYear,
      // videoOrder,
      // chnId,
      playControl,
      vv
      // vipInfo,
      // payMark,
      // isLive
    } = item

    const trueMarkIcons = markIcons.toJS()
    const {
      horizonStyle,
      showingWide,
      imgSrc,
      isAdded,
      isShowWideDetailBg,
      lazyLoaded,
      updateInfo,
      updateInfoStyle,
      markCtag
    } = this.state
    const { isMobile } = this.context
    let { hoverClass } = this.state
    // const trueLang = langPkg.toJS()
    let _hoverType = hoverType
    if (vv) _hoverType = 'hotPlay'

    const isPlayControl = playControl
    switch (hoverType) {
      case 'horizontal':
        hoverClass = 'horizontal'
        break
      case 'vertical':
        hoverClass = 'vertical'
        break
      case 'mask':
        hoverClass = 'mask'
        break
      default:
        hoverClass = 'normal'
    }
    // let pullDownArrow

    // if (hoverClass === 'vertical') {
    //   pullDownArrow = (
    //     <div className="pull-down-arrow">
    //       <DetailArrowIcon />
    //     </div>
    //   )
    // } else {
    //   pullDownArrow = <></>
    // }
    if (!item.pingback) {
      item.pingback = {}
    }
    // 处理有pb值设置传入对情况
    let rseat = id
    let curBlock = item.pingback.block
    if (pbParamObj.rpage === 'star_infopage') {
      rseat = pbParamObj.rseat
      curBlock = pbParamObj.block
    }
    let dataPb = `c1=${item.chnId}&block=${curBlock}&r=${item.id}&rseat=${id}&stype=${item.pingback.stype}&ht=${item.pingback.ht}&bkt=${item.pingback.bkt}&r_source=${item.pingback.r_source}&r_area=${item.pingback.r_area}&e=${item.pingback.e}&reasonid=${item.pingback.reasonid}&position=${item.pingback.position}`
    if (item.pingback.s2) {
      // 搜索结果页通栏
      dataPb += `&s2=${item.pingback.s2}&s3=${item.pingback.s3}&s4=${item.pingback.s4}&bstp=2`
    }

    if (item.pingback.stype) {
      // 推荐数据
      dataPb += '&bstp=24'
    }
    if (item.pingback.bstp === 18) {
      // 片库
      dataPb = `rpage=${item.pingback.rpage}&block=${item.pingback.block}&
      r=${item.id}&
      s_bkt=${item.pingback.s_bkt}&bstp=18&
      s_tag=${item.pingback.s_tag}&s_e=${item.pingback.s_e}&
      s_page=${item.pingback.s_page}&s_mode=${item.pingback.s_mode}&
      s_source=${item.pingback.s_source}&s_ct=${item.pingback.s_ct}&
      s_st=${item.pingback.s_st}&s_docids=${item.id}&s_mode=${item.pingback.s_mode}`
    }
    if (item.pingback.type === 'search_list') {
      const { rpage, block, position } = item.pingback
      dataPb = `rpage=${rpage}&block=${block}&position=${position}`
    }
    if (hoverType === 'history') {
      dataPb = `rpage=${pbParamObj.rpage}&block=${item.pingback.block}&r=${item.id}&a=image`
    }

    if (markCtag) {
      dataPb += `&ctag=${markCtag}`
    }

    let playUrl = url
    if (url.indexOf('frmrp') === -1) {
      playUrl = url + pbParam
    }
    // qipuIdStr为空时加接口传参，方便后端定位
    if (!item.qipuIdStr && item.urlParams) {
      playUrl += '&' + item.urlParams
    }

    // 第一个通栏前6个图片采用同步渲染，这里的图片地址拼接同showPic方法，后面可以考虑放到数据处理里面
    let curImgSrc = imgSrc
    const albumPic = item.albumPic
    const albumPicBak = item.albumPicBak
    // const { block_type } = item
    if (!lazyLoad && id < 8) {
      if (imgType === '1' || imgType === '3') {
        if (/\/common\//.test(albumPic)) {
          curImgSrc = albumPic // 如果图片带有common链接，则不拼图直接展示
        } else {
          curImgSrc = imgUrl(albumPic, '_260_360')
        }
      } else if (/\/common\//.test(albumPic)) {
        curImgSrc = imgUrl(albumPicBak, '_480_270') // 如果图片带有common链接，则横图拼albumPicBak(albumPic)做展示，不用resPic(albumPic)
      } else {
        curImgSrc = imgUrl(albumPic, '_480_270')
      }
    }
    const channelItemSty = pageType === 'channel' ? 'channelPage' : ''

    return (
      <PicTextUnitWrapper
        style={styleObj}
        className={`${hoverClass} img-type-${imgType || '1'} ${channelItemSty}`}
        ref={this.unitRef}
      >
        <div
          className={`plist-img-wrap
            ${pullDownId === id ? 'pull-hover' : ''}
            ${isPlayControl ? 'play-control' : ''}`}
        >
          <a
            href={isPlayControl ? 'javascript:void(0)' : playUrl}
            gtag-category={gtagCategory}
            rseat={
              pbParamObj.rpage === 'star_infopage' ? rseat : item.pingback.index
            }
            data-pb={dataPb}
            onClick={e => {
              if (hoverType === 'history') {
                setS2S3S4({
                  s2: pbParamObj.rpage,
                  s3: item.pingback.block,
                  s4: item.pingback.index,
                  e: item.pingback.e
                })
              }
              this.handleSendGtag(e, item)
            }}
            target="_blank"
            rel="noreferrer"
          >
            <div
              style={{ position: 'relative' }}
              onMouseEnter={this.handleMEnter}
              onMouseLeave={this.handleMLeave}
              onFocus={this.handleMOver}
              onBlur={this.handleMOut}
            >
              <div className="pic-box">
                {global.window && (
                  <MarkIcon
                    marks={item.marks}
                    parentRef={this.unitRef}
                    mkid={`PicTextUnit-mark-${item.id}-${item.name}`}
                    setMarkCtag={this.setMarkCtag}
                  />
                )}
                <img
                  className={`img ${showingWide ? 'showingWide' : ''} ${
                    typeof gtagCategory === 'string' &&
                    gtagCategory.slice(-3) === 'Hot'
                      ? 'hot-play'
                      : ''
                  }`}
                  src={curImgSrc}
                  style={{
                    objectFit: lazyLoaded
                      ? 'cover'
                      : /_51_40_/.test(curImgSrc) // 在这里做个兼容，如果是懒加载的背景图，就不放css
                      ? ''
                      : 'fill'
                  }}
                  alt={this.getAlt(item) || name}
                  rseat={
                    pbParamObj.rpage === 'star_infopage'
                      ? rseat
                      : item.pingback.index
                  }
                />
                {hoverType === 'history' ? (
                  <div className="history-wrap">
                    <div className="history-update">
                      {trueMarkIcons[item?.marks?.left_bottom?.num]?.img && (
                        <img
                          alt="icon"
                          className="history-update-icon"
                          // src="http://u8.iqiyipic.com/intl_lang/20240530/8d/21/intl_lang_9bd1c9874c53a744484f960bff21_default.png"
                          src={
                            trueMarkIcons[item?.marks?.left_bottom?.num]?.img
                          }
                        />
                      )}
                      {item?.marks?.left_bottom?.text ||
                        trueMarkIcons[item?.marks?.left_bottom?.num]?.text}
                      {/* {playPlan} */}
                    </div>
                    <div className="history-bar">
                      <div
                        className="history-time"
                        style={{ width: playTimePencent }}
                      />
                    </div>
                  </div>
                ) : null}
                {((hoverType === 'normal' || hoverType === 'history') && (
                  <>
                    <Mask
                      playTimePencent={playTimePencent}
                      hoverType={hoverType}
                      item={item}
                      isPlayControl={isPlayControl}
                      isMobile={isMobile}
                      isAdded={isAdded}
                      changeIsAdded={this.changeIsAdded}
                      isShowColl
                      rpage={pbParamObj.rpage}
                      block={pbParamObj.block}
                      rseatHead="addlist"
                      clickPbId={id}
                      clickPbQipiId={item.qipiId || item.tvId}
                    />
                  </>
                )) ||
                  null}
                {this.handleRightTopIconRender()}
                {hoverType === 'mask' ? (
                  <Mask
                    isPlayControl={isPlayControl}
                    isMobile={isMobile}
                    rpage={pbParamObj.rpage}
                    block={pbParamObj.block}
                    item={item}
                    rseatHead="addlist"
                    clickPbId={id}
                    clickPbQipiId={item.qipiId || item.tvId}
                  />
                ) : null}
                {updateInfo &&
                hoverType !== 'mask' &&
                _hoverType !== 'hotPlay' &&
                hoverType !== 'history' ? (
                  <>
                    <div className="update-info-layer" style={updateInfoStyle}>
                      <div className="update-info-mask">{updateInfo}</div>
                    </div>
                  </>
                ) : null}
              </div>
              {hoverType === 'horizontal' ? (
                <WideDetails
                  item={item}
                  id={id}
                  rpage={pbParamObj.rpage}
                  block={curBlock}
                  styleObj={horizonStyle}
                  changeIsAdded={this.changeIsAdded}
                  isShowWideDetailBg={isShowWideDetailBg}
                  isAdded={isAdded}
                  dataPb={dataPb}
                  alt={this.getAlt(item)}
                />
              ) : (
                <></>
              )}
            </div>
            {this.handleNameAndUpdateRender()}
          </a>
        </div>
      </PicTextUnitWrapper>
    )
  }
}

const mapStateToProps = state => ({
  langPkg: state.getIn(['language', 'langPkg']),
  currentLang: state.getIn(['language', 'modeLangObj', 'lang']),
  markIcons: state.getIn(['commonConfig', 'markIcons'])
})
export default connect(mapStateToProps)(PicTextUnit)
