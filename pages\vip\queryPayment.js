import React from 'react'
import { connect } from 'react-redux'
import $http from '@/kit/fetch'
import { getTimeZone } from '@/utils/common'
import { queryFromUrl } from '@/kit/url'
import { locUrl, rebuildCommonUrl } from '@/kit/common'
import { getCookies } from '@/kit/cookie'
import { intlVipPayResultInterface } from '@/constants/interfaces'
import Meta from '@/components/common/Meta'
// import TongJiPb from '@/components/common/TongJiPb'
import VipPageContainer from '@/components/pages/vip/VipPageContainer'
import QueryPayment from '@/components/pages/vip/QueryPayment'
import {
  getOrderQuery,
  getPbQueryFromUrl,
  getAbtest
} from '@/components/pages/vip/api'
import TemplateErrorPop from '@/components/pages/vip/Error'
import { getUid } from '@/utils/userInfo'
import { utmPbParams } from '@/utils/pingBack'

class Page extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      error: '',
      notFinishToast: false,
      abParams: {}
    }
  }

  componentDidMount() {
    this.queryResult()
    const abParams = this.getAbParams()
    this.setState({ abParams })
    this.sendPb(abParams)
  }

  getAbParams() {
    const _url = locUrl()
    return {
      fc: queryFromUrl(_url, 'fc') || '',
      fv: getCookies('playFV') || queryFromUrl(_url, 'fv') || '',
      abtest: queryFromUrl(_url, 'abtest') || '',
      pid: queryFromUrl(_url, 'pid') || '',
      v_prod: queryFromUrl(_url, 'v_prod') || '',
      pay_type: queryFromUrl(_url, 'pay_type') || ''
    }
  }

  sendPb(initParams) {
    const varScript = document.createElement('script')
    const rpage = 'redirection'
    const { modeLangObj } = this.props
    const lang = modeLangObj.get('lang')
    const mod = modeLangObj.get('mod')
    const testRes = getAbtest()
    const abtest = `page_cashier${testRes ? `,${testRes}` : ''}${
      initParams.abtest ? ',' + initParams.abtest : ''
    }`
    const { vfm, encodeVfm } = utmPbParams()

    varScript.innerHTML = `
      window.intlPageInfo = window.intlPageInfo || {};
      intlPageInfo.i18n = 'global';
      intlPageInfo.pbInfos = {
        "rpage": "${rpage}",
        "lang": "${lang}",
        "mod": "${mod}"
      }
      intlPageInfo.pbInfos.pbShowParams = intlPageInfo.pbInfos.pbShowParams || {}
      intlPageInfo.pbInfos.pbClickParams = intlPageInfo.pbInfos.pbClickParams || {}
      intlPageInfo.pbInfos.pbShowParams.bstp = 56
      intlPageInfo.pbInfos.pbShowParams.abtest = '${abtest}'
      intlPageInfo.pbInfos.pbShowParams.fc = '${initParams.fc || ''}'
      intlPageInfo.pbInfos.pbShowParams.fv = '${initParams.fv || ''}'
      intlPageInfo.pbInfos.pbShowParams.pay_type = '${initParams.pay_type ||
        ''}'
      intlPageInfo.pbInfos.pbShowParams.v_pid = '${initParams.pid || ''}'
      intlPageInfo.pbInfos.pbShowParams.v_prod = '${initParams.v_prod || ''}'
      intlPageInfo.pbInfos.pbShowParams.cashier_type = 'norm'
      if("${vfm}") {
        intlPageInfo.pbInfos.pbShowParams.vfm = "${encodeVfm}"
        intlPageInfo.pbInfos.pbClickParams.vfm = "${vfm}"
      }
    `
    document.body.append(varScript)
    // document.body.append(script)
  }

  queryResult = async onlyOnce => {
    const modeLangObj = this.props.modeLangObj.toJS()
    const { mod, lang } = modeLangObj
    const platform = this.props.bossCode
    const timeZone = getTimeZone(mod)
    const url = locUrl()
    const vipOrder = queryFromUrl(url, 'vipOrder')
    if (!onlyOnce && this.timer) {
      clearTimeout(this.timer)
    }
    try {
      const resultData =
        (await $http(`${intlVipPayResultInterface}`, {
          method: 'POST',
          params: {
            platform,
            app_lm: mod,
            lang,
            timeZone,
            version: '1.1',
            orderCode: vipOrder
          }
        })) || {}
      const { i18nOrderInfo } = resultData.data || {}
      // 添加线上日志

      const addloggers = {
        queryPayment: {
          time: new Date(),
          uid: getUid(),
          resultData
        }
      }
      const loggers = localStorage.getItem('QiyiPlayerLogger')
      window.localStorage.setItem(
        'QiyiPlayerLogger',
        loggers + JSON.stringify(addloggers)
      )

      if (
        resultData.code === 'Q00301' ||
        (resultData.code === 'A00000' && i18nOrderInfo.status === 7)
      ) {
        // eslint-disable-next-line no-unused-expressions
        onlyOnce &&
          this.setState({ notFinishToast: true }, () => {
            setTimeout(() => {
              this.setState({ notFinishToast: false })
            }, 2000)
          })

        // eslint-disable-next-line no-unused-expressions
        !onlyOnce && (this.timer = setTimeout(this.queryResult, 5000))
      } else if (resultData.code === 'Q00332') {
        this.setState({
          error: 'timeout'
        })
      } else {
        window.gtag &&
          window.gtag('event', 'in_web_purchase', {
            currency: i18nOrderInfo.currencyUnit,
            value: i18nOrderInfo.realFee / 100,
            mod: mod || 'intl',
            rpage: 'cashier_norm'
          })
        const url = window.location.href
        const query = getOrderQuery(url)
        const pbInfo = getPbQueryFromUrl(url)
        window.location.href = rebuildCommonUrl(
          `vip/payResult${
            query ? query + '&vipOrder=' : '?vipOrder='
          }${vipOrder}&${pbInfo}`
        )
        // setStep('payResultStep')
      }
    } catch (error) {
      this.setState({
        error: 'timeout'
      })
      console.log(error)
    }
  }

  componentDidUpdate() {}

  render() {
    const { cashierLangPkg } = this.props
    const { notFinishToast, abParams } = this.state
    const vipLangPkg = this.props.vipLangPkg.toJS()
    const url = locUrl()
    const vipOrder = queryFromUrl(url, 'vipOrder')
    const type = queryFromUrl(url, 'type')
    return (
      <>
        <Meta
          desc={cashierLangPkg.getIn(['cashier_html_description'])}
          title={cashierLangPkg.getIn(['upgrade_VIP'])}
        />
        <VipPageContainer autoWidth>
          {this.state.error ? (
            <TemplateErrorPop
              image="//www.iqiyipic.com/common/fix/global/api_network_error.png"
              title={vipLangPkg.pcashier_error_network}
              btnText={vipLangPkg.pcashier_error_retry}
              btnClick={() => {
                window.location.reload()
              }}
            />
          ) : (
            <QueryPayment
              vipOrder={vipOrder}
              type={type}
              queryResult={this.queryResult}
              notFinishToast={notFinishToast}
              abParams={abParams}
            />
          )}
        </VipPageContainer>
        {/* <TongJiPb rpage="pc_casher" /> */}
      </>
    )
  }
}

const mapStateToProps = state => ({
  vipLangPkg: state.getIn(['vip', 'vipLangPkg']),
  cashierLangPkg: state.getIn(['vip', 'cashierLangPkg']),
  modeLangObj: state.getIn(['language', 'modeLangObj']),
  userInfo: state.getIn(['user', 'userInfo'])
})

export default connect(mapStateToProps)(Page)
