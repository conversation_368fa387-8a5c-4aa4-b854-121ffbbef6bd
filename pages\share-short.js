import React, { useEffect } from 'react'
import { connect } from 'react-redux'
import { checkQipuIdStr } from '@/kit/url'
import {
  fetchVideoInfoAction,
  getPlayStatusCodeAction
} from '@/store/reducers/play/play'
import { getCtxDevice } from '@/kit/cookie'
import ShareShortContent from '@/components/pages/shareShort/Content'
import { fetchConfigAction } from '@/store/reducers/commonConfig/commonConfig'
import { getCard } from '@/kit/commonConfig'
import AlbumMeta from '@/components/pages/album/meta'

const ShareShort = props => {
  const { finalId, cards, albumInfo, curUrl, modeLangObj } = props
  useEffect(() => {
    props.dispatch(
      fetchConfigAction({
        pageSt: 'short_drama_share',
        channelId: 35
      })
    )
  }, [])

  const recommendData = getCard('pcw_vertical_list', cards.toJS())[0]
  return (
    <>
      <AlbumMeta curUrl={curUrl} mod={modeLangObj.get('mod')} />
      <ShareShortContent recommendData={recommendData} albumInfo={albumInfo} />
    </>
  )
}

ShareShort.getInitialProps = async ({ ctx }) => {
  const { query, store, res } = ctx
  const { id } = query
  const isMobile = getCtxDevice(ctx) === 'mobile'
  const finalId = checkQipuIdStr(id)
  const curUrl = ctx.req.url.split('?')[0]
  if (!finalId) {
    await store.dispatch(getPlayStatusCodeAction(404))
    res.setHeader('x-page-type', 'not-found')
  } else {
    await store.dispatch(
      fetchVideoInfoAction({
        id: finalId,
        isSupportSEO: true,
        ctx,
        isMobile,
        _catch: {}
      })
    )
  }
  return { finalId, curUrl }
}

const mapStateToProps = state => {
  return {
    albumInfo: state.getIn(['play', 'albumInfo']),
    cards: state.getIn(['commonConfig', 'short_drama_share', 'cards']),
    modeLangObj: state.getIn(['language', 'modeLangObj'])
  }
}

export default connect(mapStateToProps)(ShareShort)
