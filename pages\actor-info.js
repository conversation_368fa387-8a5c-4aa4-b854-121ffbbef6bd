/**
 * @description 影人信息页面
 */
import React, { useEffect } from 'react'
import { connect } from 'react-redux'
import Introduce from '@/components/pages/actorInfo/introduce'
import ActorContent from '@/components/pages/actorInfo/actorcontent'
import ActorInfoMeta from '@/components/pages/actorInfo/meta'

import TongJiPb from '@/components/common/TongJiPb'
import $http from '@/kit/fetch'
import { PCW_PEOPLE_COMMON } from '@/constants/interfaces'
import { getCookies } from '@/kit/cookie'
import { platformId } from '@/kit/common'
import { getS2S3S4, removeS2S3S4 } from '@/utils/common'

import ActorInfoWrapper from '@/style/actorInfo'
import { checkActorIdStr } from '@/kit/url'

import HomeSchema from '@/components/pages/home/<USER>'
import OrganizationSchema from '@/components/pages/home/<USER>'
import Schema from '@/components/pages/actorInfo/schema'

import Error from './_error'

const ActorInfo = props => {
  const rpage = 'star_infopage'
  const {
    starInfo = {},
    langPkg,
    id,
    curUrl,
    langCode,
    modeLangObj,
    peopleCommonData
  } = props

  useEffect(() => {
    const sObj = getS2S3S4()
    window.actorInfoS2 = sObj.s2
    window.actorInfoS3 = sObj.s3
    window.actorInfoS4 = sObj.s4
    removeS2S3S4()
  }, [])

  const trueLangPkg = langPkg.toJS()

  if (Object.keys(starInfo).length === 0) {
    return <Error statusCode={404} />
  }

  const { playList = [] } = starInfo
  return (
    <ActorInfoWrapper>
      <ActorInfoMeta
        starInfo={starInfo}
        id={id}
        langCode={props.langCode}
        curUrl={curUrl}
      />
      <OrganizationSchema langCode={langCode} />
      <HomeSchema langCode={langCode} />
      <Schema starInfo={starInfo} curUrl={curUrl} langCode={langCode} />
      {/* 简介组件 */}
      <Introduce
        {...starInfo}
        starInfo={starInfo}
        langPkg={trueLangPkg}
        rpage={rpage}
        id={id}
        modeLangObj={modeLangObj}
      />
      {/* 作品集组件 */}
      <ActorContent
        langPkg={trueLangPkg}
        qipuId={props.id}
        langCode={props.langCode || 'en_us'}
        modeCode={props.modeCode || 'intl'}
        playList={playList}
        // partialRelatedPeople={partialRelatedPeople}
        peopleCommonData={peopleCommonData}
      />
      <TongJiPb rpage={rpage} pbShowParams={{ rId: props.id }} />
    </ActorInfoWrapper>
  )
}

// 生日转换 按@孙立荣要求过滤生日为0的状况
const transBirthday = birthday => {
  if (Number(birthday?.year) === 0) {
    return undefined
  }
  const result = `${birthday?.year}-${
    birthday?.month < 10 ? '0' + birthday?.month : birthday?.month
  }-${birthday?.day < 10 ? '0' + birthday?.day : birthday?.day}`
  return result
}

// 数据转化
const transData = (starInfo, lang) => {
  const {
    coverPic,
    header,
    gender,
    name,
    nationalityName,
    englishName,
    birthday,
    description,
    occupation,
    nationalityRegions,
    collectionDTOS,
    height,
    width,
    titleSEO,
    qipuId,
    playList, // 播单数组
    partialRelatedPeople, // 相关人物数组
    weight,
    age,
    constellation,
    aliasName
  } = starInfo || {}
  const birthdayStr = transBirthday(birthday)
  const nameArr = [name, englishName, nationalityName]
  let firstName = ''
  let secondName = ''

  let aliasNameStr = aliasName[0] || ''
  let commaStr = ', '
  let commaStr2 = ': '
  if (lang === 'zh_cn' || lang === 'zh_tw') {
    commaStr = '，'
    commaStr2 = '：'
  }
  aliasNameStr = aliasNameStr?.replace(/(,|、)/g, commaStr)
  aliasNameStr = commaStr2 + aliasNameStr

  const filterArr = [...new Set(nameArr)]
  // 第一个名取三者里第一个不为空的名字， 第二个名取三者里第二个不为空的名字
  filterArr.find((item, index) => {
    if (item) {
      firstName = item
      if (index === 0) {
        secondName = filterArr[index + 1] || filterArr[index + 2]
      } else {
        secondName = filterArr[index + 1]
      }
      return true
    }
    return false
  })

  // 若取到的俩个名称相等则隐藏
  if (firstName === secondName) {
    secondName = undefined
  }

  const transResult = {
    headImg: coverPic || header,
    gender,
    firstName,
    name,
    englishName,
    secondName,
    aliasNameStr,
    aliasName: aliasName[0] || '',
    nationalityName,
    birthday: birthdayStr,
    desc: description?.trim(),
    occupation: occupation?.slice(0, 3).join(commaStr),
    nationality: nationalityRegions?.slice(0, 3).join(commaStr),
    collectionDTOS: collectionDTOS || [],
    height,
    width,
    titleSEO,
    qipuId,
    playList, // 播单数组
    partialRelatedPeople, // 相关人物数组
    weight,
    age,
    constellation
  }
  return transResult
}

// const handleId = idParam => {
//   if (idParam.match(/^[0-9]*05$/)) {
//     return idParam
//   } else {
//     const splitResult = idParam.split('-')
//     return splitResult[splitResult.length - 1]
//   }
// }

// SSR服务端渲染
ActorInfo.getInitialProps = async ({ ctx }) => {
  const { query = {} } = ctx
  const curUrl = ctx.req.url.split('?')[0]
  const { mod, lang, id } = query
  const finalId = checkActorIdStr(id)
  if (!finalId || finalId === '208809305') {
    // 特殊处理签子的影人qipuId
    ctx.res.writeHead(301, {
      Location: `//www.iq.com`
    })
    return ctx.res.end()
  }
  let starInfo = {}
  let peopleCommonData = {}
  // let langPkg = {}
  const langCode = getCookies('lang', ctx) || 'en_us'
  try {
    // 拉取影人详情页面的数据
    const param = {
      qipuId: finalId,
      langCode: lang || langCode,
      modeCode: mod || 'intl',
      platformId: 3 || platformId(),
      pageSize: 30
    }
    const options = {}
    options.params = {
      ...param
    }

    // options.params.pageSize = 30
    const peopleCommonRes = await $http(PCW_PEOPLE_COMMON, options)

    if (peopleCommonRes.code === '0' && peopleCommonRes.data) {
      peopleCommonData = peopleCommonRes?.data?.cardMap
      starInfo = peopleCommonData['people-collection-list']?.respData
    } else {
      peopleCommonData = {}
    }

    starInfo = transData(starInfo, lang || langCode)
  } catch (e) {
    console.log('request error')
    console.log(e)
    starInfo = {}
  }
  return {
    starInfo,
    // langPkg,
    langCode: lang || langCode,
    modeCode: mod || 'intl',
    id: finalId,
    curUrl,
    peopleCommonData
  }
}

const mapStateToProps = state => ({
  langPkg: state.getIn(['language', 'langPkg']),
  modeLangObj: state.getIn(['language', 'modeLangObj'])
})

export default connect(mapStateToProps)(ActorInfo)
