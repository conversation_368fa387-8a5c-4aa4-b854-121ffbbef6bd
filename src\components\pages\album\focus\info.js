import React, { Component } from 'react'
import dynamic from 'next/dynamic'
import { connect } from 'react-redux'
import { supportUrlPbMod, isLiveId, getNewCategoryTagList } from '@/utils/video'
import { isCollected } from '@/utils/videoApi'
import getUpdate from '@/utils/getUpdate'
import {
  curlMod,
  rebuildPlayUrl,
  handleRankInfo,
  rebuildCommonUrl
} from '@/kit/common'
// import $http from '@/kit/fetch'
// import { ISCOLLECTION } from '@/constants/interfaces'
import AlbumScore from '@/components/pages/album/score'
// import Collection from '@/components/common/Collection'
// import ShareFold from '@/components/common/ShareFold'
import {
  NewPlayButtonArrow,
  ShadowDropDownArrow,
  ShadowUpArrow
} from '@/components/svgs'
import {
  // getMobileType,
  getOs
} from '@/kit/device'
// import ToolTip from '@/components/common/ToolTip'
import { sendBlockPb } from '@/utils/pingBack'
import { isInScreen } from '@/kit/dom'
import { addResizeListener, removeResizeListener } from '@/kit/resizeEvent'
import { getShortLink } from '@/utils/jsCallApp'
import ReserveButton from '@/components/common/ReserveButton'
import { goToPlayRecord } from '@/utils/getAlbumPlayRecord'
import { AlbumImageIcon } from '@/constants/style'
import { setS2S3S4 } from '@/utils/common'
import PreviewPlayer from '@/utils/player/previewPlayer'
import Promotion from './promotion'
import FocusInfoWrapper from './infoStyle'

const Collection = dynamic(import('@/components/common/Collection'), {
  ssr: true
})
const ShareNewFlod = dynamic(import('@/components/common/ShareNewFlod'), {
  ssr: true
})
const MergeDownload = dynamic(import('@/components/common/MergeDownload'), {
  ssr: true
})

class FocusInfo extends Component {
  constructor(props) {
    super(props)
    this.state = {
      isAdded: null,
      isToastShow: false,
      toastMsg: '',
      // btnType: 'init',
      descMoreBtn: false,
      lessDescTag: true,
      initArrow: <ShadowDropDownArrow />,
      clickShortlink: '' // 点击短链
    }
    this.pbTag = false
    this.screenWidth = 0
    this.sendShowPb = this.sendShowPb.bind(this)
    this.checkDescHeight = this.checkDescHeight.bind(this)
    this.handleMoreDesc = this.handleMoreDesc.bind(this)
  }

  async componentDidMount() {
    const { albumInfo, modeLangObj } = this.props
    this.checkDescHeight()
    addResizeListener(this.checkDescHeight)
    // const totalVotes = albumScoreInfo.get('totalVotes')
    // let scoreTag = false
    // if (totalVotes > 10) {
    //   scoreTag = true
    // }
    // const clientWidth = document.body.clientWidth
    // let btnType = 'pc'
    // if (clientWidth < 768) {
    //   btnType = 'h5'
    // }
    // this.setState({
    // isShowScore: scoreTag
    // btnType
    // })
    const videoType = albumInfo.get('videoType')
    const subType = videoType === 'juji' ? 1 : videoType === 'laiyuan' ? 2 : 7
    const subKey = albumInfo.get('albumId') || albumInfo.get('defaultTvId')

    this.collParam = {
      mod: curlMod() || 'intl',
      subType,
      subKey,
      agent_type: 426,
      channelId: albumInfo.get('chnId')
    }
    this.handleCollected()

    this.sendShowPb()
    window.addEventListener('scroll', this.sendShowPb)
    // window.addEventListener('resize', () => {
    // const clientWidth = document.body.clientWidth
    // let btnType = 'pc'
    // if (clientWidth < 768) {
    //   btnType = 'h5'
    // }
    // this.setState({
    //   btnType
    // })
    // })
    // 获取短链 为提高展示效率 先获取短链信息
    // const { pathname } = this.context
    const linkpms = {
      pathname: '/album',
      langs: modeLangObj,
      videoInfo: albumInfo,
      rpage: 'album',
      block: 'video_information'
    }
    getShortLink({ rseat: 'download', ...linkpms }, link => {
      this.setState({
        clickShortlink: link
      })
    })
  }

  componentDidUpdate(preProps) {
    if (preProps.loginTag !== this.props.loginTag) {
      if (this.props.loginTag) this.handleCollected()
    }
  }

  componentWillUnmount = () => {
    removeResizeListener(this.checkDescHeight)
  }

  async handleCollected() {
    const isAdded = await isCollected(this.collParam)
    this.setState({ isAdded })
  }

  getOneLik = rseat => {
    return new Promise(resolve => {
      const langPkgData = this.props.langPkg.toJS()
      getShortLink(
        {
          pathname: '/album',
          langs: this.props.modeLangObj,
          downloadInvitecode: langPkgData.download_invite_code,
          videoInfo: this.props.albumInfo,
          rpage: 'album',
          block: 'share_block',
          rseat
        },
        link => {
          resolve(link)
        }
      )
    })
  }

  showCopylinkToast() {
    if (this.timer) {
      clearTimeout(this.timer)
    }
    this.setState({
      isToastShow: true
    })
    this.timer = setTimeout(() => {
      this.setState({
        isToastShow: false
      })
    }, 2000)
  }

  sendShowPb() {
    if (this.pbTag) {
      window.removeEventListener('scroll', this.sendShowPb)
      return
    }
    if (isInScreen(this.infoEl)) {
      this.pbTag = true
      const { albumInfo } = this.props
      const qipuId = albumInfo.get('qipuId')
      sendBlockPb('video_information', {
        rpage: 'album',
        rId: qipuId
      })
    }
  }

  handleLineNum = dom => {
    if (!dom) return 0

    let height
    let lineHeight
    if (dom.currentStyle) {
      height = dom.currentStyle['height']
      lineHeight = dom.currentStyle['lineHeight']
    } else {
      height = window.getComputedStyle(dom, false)['height']
      lineHeight = window.getComputedStyle(dom, false)['line-height']
    }
    if (height === 'auto') {
      height = dom.offsetHeight
    }
    return Math.round(parseFloat(height) / parseFloat(lineHeight))
  }

  handleInfoLineNum = () => {
    if (!(this.descDom && this.descDom.children)) return null
    const decWrapRef = this.descDom.children
    const decRef = decWrapRef ? Array.prototype.slice.apply(decWrapRef) : []
    let desclineCount = 0
    if (decRef) {
      decRef.forEach(child => {
        if (child.getAttribute('class') === 'key') {
          return
        }
        // 排除按钮区域，只计算实际内容的行数
        // if (child.getAttribute('class') === 'intl-album-des-btn') {
        //   return
        // }
        desclineCount += this.handleLineNum(child)
      })
    }
    this.desclineCount = desclineCount
    return null
  }

  checkDescHeight() {
    if (this.screenWidth === document.body.clientWidth) return
    this.screenWidth = document.body.clientWidth
    this.handleInfoLineNum()
    // if (this.actlineCount + this.dirlineCount + this.desclineCount > 4) {
    if (this.desclineCount > 3) {
      this.setState({
        descMoreBtn: true
      })
    } else {
      this.setState({
        descMoreBtn: false
      })
    }
  }

  handleMoreDesc() {
    const { showMoreBtn } = this.state
    const moreBtnTag = !showMoreBtn
    let lessDescTag
    let arrowDom
    if (moreBtnTag) {
      lessDescTag = false
      arrowDom = <ShadowUpArrow />
    } else {
      lessDescTag = true
      arrowDom = <ShadowDropDownArrow />
    }
    this.setState({
      showMoreBtn: moreBtnTag,
      initArrow: arrowDom,
      lessDescTag
    })
  }

  getH2Content(pkg) {
    const { albumInfo } = this.props
    const name = albumInfo.get('name')

    return `${pkg.title_details} | ${name}`
  }

  render() {
    const {
      albumInfo,
      langPkg,
      loginTag,
      modeLangObj,
      dispatch,
      albumReserveShow,
      isMobile,
      previewVideoInfo,
      albumScoreInfo
    } = this.props
    const {
      // isShowScore,
      isAdded,
      isToastShow,
      toastMsg,
      // btnType,
      descMoreBtn,
      lessDescTag,
      initArrow,
      clickShortlink
    } = this.state
    const pkg = langPkg.toJS()
    const videoType = albumInfo.get('videoType')
    const qipuId = albumInfo.get('qipuId')
    const qipuIdStr = albumInfo.get('qipuIdStr')
    const playLocSuffix = albumInfo.get('playLocSuffix')
    const flag = albumReserveShow.get('flag')
    let playUrl =
      rebuildPlayUrl(playLocSuffix) || rebuildPlayUrl(qipuIdStr) || ''
    const mod = modeLangObj.get('mod')
    if (supportUrlPbMod(mod)) {
      if (playUrl.indexOf('?') === -1) {
        playUrl += `?frmrp=album&frmb=video_information&frmrs=play`
      } else {
        playUrl += `&frmrp=album&frmb=video_information&frmrs=play`
      }
    }
    const name = albumInfo.get('name')
    const chnId = albumInfo.get('chnId')
    const rankInfo =
      albumInfo.get('rankInfo') && albumInfo.get('rankInfo').toJS()
    const vipInfo = albumInfo.get('vipInfo').toJS()
    const rankLangKey = handleRankInfo(rankInfo, chnId)
    const categoryTagMap = albumInfo.get('categoryTagMap')
    const descMsg = albumInfo.get('desc')
    const ratingVal = albumInfo.get('rating')
    const ratingWrap =
      ratingVal && ratingVal !== '0+' ? <span>{ratingVal}</span> : ''
    let yearWrap
    const yearVal = albumInfo.get('year')
    if (yearVal && yearVal !== '0') {
      if (ratingWrap) {
        yearWrap = (
          <>
            <div className="broken-line" />
            <span>{yearVal}</span>
          </>
        )
      } else {
        yearWrap = <span>{yearVal}</span>
      }
    }
    let typeArr = []
    if (categoryTagMap) {
      typeArr = getNewCategoryTagList(categoryTagMap.toJS())
      typeArr = typeArr.splice(0, 7) // 最多展示7个标签
    }
    const dirArr = albumInfo.get('dirArr').toJS() || []
    const mainAct = albumInfo.get('actorArr') || []
    const actor = albumInfo.get('actor') || []
    const Act =
      videoType === 'laiyuan' ? mainAct.toJS() : mainAct.concat(actor).toJS()
    const dirKey = videoType === 'laiyuan' ? pkg.host : pkg.play_director
    const actKey = videoType === 'laiyuan' ? pkg.guest : pkg.play_main_character

    let topWrap = null
    let liveWrap = null
    let tvodVipMarkWrap = null
    let originalExclusiveWrap = null

    if (rankInfo) {
      topWrap = (
        <span className="focus-item-label-top">
          <span className="focus-item-label-rank">
            TOP {rankInfo.rankInChart}
          </span>
          {pkg[rankLangKey]}
        </span>
      )
    }

    if (isLiveId(albumInfo.get('qipuId'))) {
      liveWrap = (
        <span className="focus-item-label-live"> {pkg.live_logo} </span>
      )
    }

    const lang = modeLangObj.get('lang')
    const tvodRatio = lang === 'zh_cn' ? 1 : lang === 'zh_tw' ? 2 : 3
    // 为了seo
    let commaStr = ', '
    let colonStr = ': '
    if (lang === 'zh_cn' || lang === 'zh_tw') {
      commaStr = '，'
      colonStr = '：'
    }
    if (vipInfo?.isTvod === 1 && vipInfo?.payMark === 'PAY_ON_DEMAND_MARK') {
      tvodVipMarkWrap = <AlbumImageIcon ratio={tvodRatio} />
    } else if (vipInfo.isVip) {
      tvodVipMarkWrap = <AlbumImageIcon ratio={0} />
    }

    if (albumInfo.get('isQiyiProduced')) {
      // 自制
      originalExclusiveWrap = (
        <span className="focus-item-label-original">{pkg.original}</span>
      )
    } else if (albumInfo.get('isExclusive')) {
      //  独播
      originalExclusiveWrap = (
        <span className="focus-item-label-exclusive">{pkg.exclusive}</span>
      )
    }

    const option = {
      chnId: albumInfo.get('chnId'),
      total: albumInfo.get('originalTotal') || 0,
      tvCount: albumInfo.get('maxOrder'),
      publishTime: albumInfo.get('publishTime')
    }
    let updateString = getUpdate(option, {
      updateTo: pkg.update_episode,
      count: pkg.total_episode,
      termTpisode: pkg.update_publishtime,
      updateTotalEpisode: pkg.update_with_total_episode
    })
    if (videoType === 'singleVideo') {
      // 单视频-电影 展示时长
      const infoHour = albumInfo.get('hour')
      const infoMin = albumInfo.get('min')
      if (infoHour >= 1) {
        updateString = pkg.film_length
          .replace('${h}', infoHour)
          .replace('${m}', infoMin)
      } else if (infoMin >= 1) {
        updateString = pkg.video_length.replace('${m}', infoMin)
      } else {
        updateString = ''
      }
    }
    let updateWrap = ''
    if (updateString !== '') {
      if (yearWrap || ratingWrap) {
        updateWrap = (
          <>
            <div className="broken-line" />
            <span>{updateString}</span>
          </>
        )
      } else {
        updateWrap = <span>{updateString}</span>
      }
    }
    const alterTitle = albumInfo.get('alterTitle') || ''
    let alterTitleWrap = ''
    if (alterTitle) {
      alterTitleWrap = (
        <>
          <br />
          <span className="key">
            <h3>
              {pkg.alias}
              {colonStr}
            </h3>
          </span>
          <span>{alterTitle}</span>
        </>
      )
    }
    let moreBtnMsg = ''
    if (lessDescTag) {
      moreBtnMsg = pkg.shareProgramInfoShowMore
    } else {
      moreBtnMsg = pkg.shareProgramInfoShowLess
    }

    let playBtnWrap = (
      <a
        className="btn play"
        data-pb={`block=video_information&r=${qipuId}`}
        rseat="play"
        role="button"
        tabIndex="0"
        onClick={() => {
          setS2S3S4({
            s2: 'album',
            s3: `video_information`,
            s4: 'play',
            ps2: window.albumS2,
            ps3: window.albumS3,
            ps4: window.albumS4
          })
          goToPlayRecord(qipuId, playUrl)
        }}
      >
        <NewPlayButtonArrow />
        <span className="play-font">{pkg.play_video}</span>
      </a>
    )
    if (
      albumInfo.get('controlStatus') === 0 ||
      albumInfo.get('defaultTvId') === 0
    ) {
      playBtnWrap = (
        <a
          className="btn notPlay"
          href="javascript:;"
          data-pb={`block=video_information&r=${qipuId}`}
          rseat="notPlay"
        >
          <NewPlayButtonArrow />
          <span className="play-font">{pkg.play_video}</span>
        </a>
      )
    }

    const h2Content = this.getH2Content(pkg)

    // const mobileType = getMobileType()
    // let btnType = 'pc'
    // if (mobileType === 'iphone' || mobileType === 'android') {
    //   btnType = 'h5'
    // }

    const totalVotes = albumScoreInfo.get('totalVotes')

    return (
      <FocusInfoWrapper
        ref={ref => {
          this.infoEl = ref
        }}
      >
        {previewVideoInfo ? (
          <PreviewPlayer
            videoInfo={previewVideoInfo}
            parentRef={this.infoEl}
            playerId="album-preview"
            rpage="album"
          />
        ) : (
          ''
        )}

        <div className="focus-info-wrapper">
          <h1 className="focus-info-title">{name}</h1>
          <h2 style={{ display: 'none' }}>{h2Content}</h2>
          <div className="focus-info-mark">
            {liveWrap}
            {topWrap}
            {originalExclusiveWrap}
            {tvodVipMarkWrap}
          </div>
          <div className="focus-info-tag">
            {totalVotes > 10 && (
              <div className="pcScore">
                <AlbumScore />
                <div className="broken-line" />
              </div>
            )}
            {ratingWrap}
            {yearWrap}
            {updateWrap}
          </div>
          {typeArr.length ? (
            <div className="focus-info-tag type">
              {typeArr.map((item, index) => {
                const keyVal = 'typearr_' + index
                const toHref = rebuildCommonUrl(
                  `film-library?value=${item.id};must&chnid=${albumInfo.get(
                    'chnId'
                  )}`
                )
                return (
                  <a
                    href={toHref}
                    data-pb={`block=library_channel&rpage=album&rseat=${item.id}`}
                  >
                    <span className="type-style" key={keyVal}>
                      {item.name}
                    </span>
                  </a>
                )
              })}
            </div>
          ) : null}
          {!flag && (
            <div className="focus-promotion">
              <Promotion
                downloadInvitecode={pkg.download_invite_code}
                langs={modeLangObj}
                videoInfo={albumInfo}
                dispatch={dispatch}
              />
            </div>
          )}
          {flag && (
            <div className="focus-reservation">
              <ReserveButton
                site="album"
                videoInfo={albumInfo}
                langs={modeLangObj}
                isMobile={isMobile}
              />
            </div>
          )}
          <div className="focus-info-tag">
            <div
              className={`tag-inline no-broken-line ${
                dirArr.length ? '' : 'dn'
              }`}
            >
              {dirArr.length ? (
                <div>
                  <span className="key">
                    <h3>{dirKey}</h3>
                    {colonStr}
                  </span>
                  {dirArr.map((item, i) => {
                    return (
                      <span key={item.name}>
                        <a
                          href={item.url}
                          tabIndex="0"
                          rseat={item.name}
                          data-pb="block=video_information"
                          className="dir"
                        >
                          {item.name}
                        </a>
                        <i>
                          {i === dirArr.length - 1
                            ? ''
                            : item.name
                            ? commaStr
                            : ''}
                        </i>
                      </span>
                    )
                  })}
                </div>
              ) : null}
            </div>
            <div
              className={`tag-inline no-broken-line ${Act.length ? '' : 'dn'}`}
            >
              {Act.length ? (
                <div>
                  <span className="key">
                    <h3>{actKey}</h3>
                    {colonStr}
                  </span>
                  {Act.map((item, i) => {
                    return (
                      <span key={item.name}>
                        <a
                          href={item.url}
                          tabIndex="0"
                          rseat={item.name}
                          data-pb="block=video_information"
                          className="act"
                        >
                          {item.name}
                        </a>
                        <i>
                          {i === Act.length - 1
                            ? ''
                            : item.name
                            ? commaStr
                            : ''}
                        </i>
                      </span>
                    )
                  })}
                </div>
              ) : null}
            </div>
          </div>
          {descMsg ? (
            <div
              className={`focus-info-desc ${lessDescTag ? '' : 'allShow'}`}
              ref={desc => {
                this.descDom = desc
              }}
            >
              <span className="key">
                <h3>{pkg.play_introduction}</h3>
                {colonStr}
              </span>
              <span>{descMsg}</span>

              {alterTitleWrap}
              <div className="intl-album-des-btn">
                {/* {descMoreBtn&&(<div className="intl-album-des-cover" />)} */}
                <div className="intl-album-des-cover" />
                <div
                  className={`intl-album-more-btn ${descMoreBtn ? 'show' : ''}`}
                  role="button"
                  tabIndex="0"
                  onClick={this.handleMoreDesc}
                  data-pb={`block=video_information&r=${qipuId}`}
                  rseat={`${lessDescTag ? 'more' : 'collapse'}`}
                >
                  {moreBtnMsg}
                  <div className="intl-album-more-arrow">{initArrow}</div>
                </div>
              </div>
            </div>
          ) : null}
          <div className="focus-info-btn">
            {playBtnWrap}
            <div className="focus-info-h5-btn">
              <ShareNewFlod
                pkg={pkg}
                rpage="album"
                qipuIdStr={albumInfo.get('qipuIdStr')}
                albumName={albumInfo.get('name')}
                className="play_menu_item"
                clickShortlink={clickShortlink}
                getOneLik={this.getOneLik}
                shareAllowed={albumInfo.get('shareAllowed')}
                albumFocus125={albumInfo.get('albumFocus125')}
                showBlock="share_block"
                block="video_information"
                rId={qipuId}
              />
            </div>
            <div className="focus-info-h5-btn">
              <Collection
                isAdded={isAdded || false}
                collParam={this.collParam}
                toastDirection="left"
                className="play_menu_item"
                h5IconBtn="true"
              />
            </div>
            <div className="btn share focus-info-pc-btn">
              <ShareNewFlod
                rpage="album"
                pkg={pkg}
                qipuIdStr={albumInfo.get('qipuIdStr')}
                albumName={albumInfo.get('name')}
                shareAllowed={albumInfo.get('shareAllowed')}
                albumFocus125={albumInfo.get('albumFocus125')}
                getOneLik={this.getOneLik}
                pcAndH5Btn="true"
                clickShortlink={clickShortlink}
                showBlock="share_block"
                block="video_information"
                rId={qipuId}
              />
            </div>
            <div className="btn download focus-info-pc-btn">
              <MergeDownload
                langPkgData={pkg}
                rpage="album"
                block="video_information"
                rseat={
                  getOs() === 'Mac' ? 'pca_mac_download' : 'pca_win_download '
                }
                rId={qipuId}
                clickShortlink={clickShortlink}
                langs={modeLangObj}
                videoInfo={albumInfo}
              />
            </div>
            <div className="btn favorite focus-info-pc-btn">
              <Collection
                isAdded={loginTag ? isAdded : false}
                collParam={this.collParam}
                pcAndH5Btn="true"
              />
            </div>
          </div>
        </div>
        {isToastShow && (
          <div className="play_click_tost" style={{ display: 'block' }}>
            {toastMsg}
          </div>
        )}
      </FocusInfoWrapper>
    )
  }
}

const mapStateToProps = state => {
  return {
    loginTag: state.getIn(['user', 'userInfo', 'loginTag']),
    albumScoreInfo: state.getIn(['album', 'albumScoreInfo']),
    defaultVideoInfo: state.getIn(['album', 'defaultVideoInfo']),
    albumReserveShow: state.getIn(['album', 'albumReserveShow']),
    previewVideoInfo: state.getIn(['album', 'previewVideoInfo']),
    dispatch: state.dispatch
  }
}

export default connect(mapStateToProps)(FocusInfo)
