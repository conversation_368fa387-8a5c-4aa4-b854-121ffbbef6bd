// @cuibo 2024-07-11 TV收银台改版TV已登录跳转页http://pms.qiyi.domain/browse/GLOBALLINEDEV-9210
import React from 'react'
import { connect } from 'react-redux'
import DeviceCtx from '@/components/context'
import Meta from '@/components/common/Meta'
import TongJiPb from '@/components/common/TongJiPb'
import LoginTvWrapper from '@/style/loginTv'
import { rebuildCommonUrl } from '@/kit/common'
import { isLogin } from '@/utils/userInfo'
import UserWrap from '@/components/common/Header/userWrap'
import { serialize } from '@/kit/fetch'
import { getOtpVipInfo, getNewAuth, ipToMod } from '@/utils/otpCodeLogin'
import Toast from '@/components/common/Toast'
import { sendBlockPb } from '@/utils/pingBack'

const reg = /^[0-9a-zA-Z]{6}$/
class LoginTv extends React.Component {
  static contextType = DeviceCtx

  constructor(props, context) {
    super(props)
    const { ptid } = context
    this.state = {
      codeValue: '',
      isError: false,
      errorMg: '',
      ptid,
      isFocus: false
    }

    this.inputRef = React.createRef()

    this.onChange = this.onChange.bind(this)
    this.handleSubmit = this.handleSubmit.bind(this)
    this.setError = this.setError.bind(this)
  }

  static async getInitialProps({ ctx }) {
    const { query } = ctx
    const scanToken = query.token || ''
    return { scanToken }
  }

  async componentDidMount() {
    if (isLogin()) {
      await this.loginRef.handleUserLogout()
    }
    const { scanToken } = this.props
    if (scanToken) {
      this.handleSubmit(scanToken)
    }
  }

  onFocus() {
    this.setState({
      isFocus: true,
      isError: false,
      errorMg: ''
    })
  }

  onBlur() {
    const { langPkg } = this.props
    const { codeValue } = this.state
    const trueLangPkg = langPkg.toJS()

    if (codeValue.length < 6 || !reg.test(codeValue)) {
      this.setState({
        isError: true,
        isFocus: false,
        errorMg: trueLangPkg['PCW_FRONTEND_1692865648171_119']
      })
    } else {
      this.setState({
        isError: false,
        isFocus: false
      })
    }
  }

  setError(errorMg, code, token) {
    if (token) {
      Toast.info(errorMg)
    } else {
      this.setState({
        isError: true,
        errorMg
      })
    }
    code &&
      sendBlockPb(code, {
        rpage: 'tv_login_yes_H5_otp'
      })
  }

  async handleSubmit(token) {
    const { codeValue, ptid } = this.state
    const { langPkg, netErrorLangPkg } = this.props
    const trueLangPkg = langPkg.toJS()
    const key = codeValue || token

    if (!reg.test(key) && !token) {
      this.setError(trueLangPkg['PCW_FRONTEND_1692865648171_119'])
      return
    }
    try {
      const vipInfo = await getOtpVipInfo({ key })
      if (vipInfo) {
        const res = await getNewAuth(vipInfo.optKey, ptid)
        if (res.code === 'A00000') {
          const orderUrl = `vip/order?myvip=true&key=${vipInfo?.key}&${
            vipInfo?.extraInfo ? serialize(JSON.parse(vipInfo.extraInfo)) : ''
          }&H5=true`
          await ipToMod()
          window.location.href = rebuildCommonUrl(orderUrl)
        } else if (res.code === 'A00001') {
          // 激活码已过期
          this.setError(
            trueLangPkg['PCW_FRONTEND_1692784866490_756'],
            res.code,
            token
          )
        } else if (res.code === 'P00159') {
          // 高危
          this.setError(
            trueLangPkg['PCW_FRONTEND_1692866013950_125'],
            res.code,
            token
          )
        } else if (res.code === 'P00500') {
          this.setError(
            trueLangPkg['PCW_FRONTEND_1721987581539_739'],
            res.code,
            token
          )
        } else {
          this.setError(netErrorLangPkg, res.code, token)
        }
      } else {
        this.setError(
          trueLangPkg['webtv_cashier_otp_keyNotValid'],
          'A00000',
          token
        )
      }
    } catch (err) {
      this.setError(netErrorLangPkg, token)
    }
  }

  onChange(e) {
    const key = e.target.value.trim()

    this.setState({
      codeValue: key
    })
  }

  bindLoginRef = ref => {
    this.loginRef = ref
  }

  render() {
    const { langPkg, scanToken } = this.props
    const { codeValue, isError, errorMg, isFocus } = this.state
    const trueLangPkg = langPkg.toJS()
    return (
      <>
        <Meta
          desc={trueLangPkg.home_html_description}
          title={trueLangPkg.home_html_title}
        />
        <LoginTvWrapper>
          <div
            style={{
              position: 'relative',
              background: '#fff',
              minHeight: '600px'
            }}
          >
            {scanToken ? (
              <></>
            ) : (
              <div className="section1">
                <div className="desc">
                  {trueLangPkg?.PCW_FRONTEND_1692079999407_573}
                </div>
                <div
                  className={`${isError ? 'error' : ''} ${
                    isFocus ? 'focus' : ''
                  } input-box`}
                >
                  <input
                    className="input"
                    ref={this.inputRef}
                    autoComplete="off"
                    onChange={this.onChange}
                    onFocus={e => this.onFocus(e)}
                    onBlur={e => this.onBlur(e)}
                    type="text"
                    value={codeValue}
                    maxLength="6"
                  />
                  <div className="tip">
                    {trueLangPkg?.PCW_FRONTEND_1692080042095_425}
                  </div>
                </div>
                <div
                  className="error-tip"
                  style={{ display: `${isError ? 'block' : 'none'}` }}
                >
                  {errorMg}
                </div>
                <div
                  className="button"
                  onClick={() => this.handleSubmit()}
                  role="button"
                  tabIndex="0"
                  rseat="click"
                  data-pb="block=continue&rpage=tv_login_yes_H5_otp"
                >
                  {trueLangPkg?.PCW_FRONTEND_1692080076568_375}
                </div>
              </div>
            )}
          </div>
        </LoginTvWrapper>
        <div className="user-wrapper">
          <UserWrap isVipButton="true" onRef={this.bindLoginRef} />
        </div>
        <TongJiPb rpage="tv_login_yes_H5_otp" />
      </>
    )
  }
}

const mapStateToProps = state => ({
  langPkg: state.getIn(['language', 'langPkg']),
  netErrorLangPkg: state.getIn([
    'vip',
    'cashierLangPkg',
    'PCW_CASHIER_1650957546922_650'
  ]),
  userInfo: state.getIn(['user', 'userInfo']),
  langs: state.getIn(['language', 'modeLangObj'])
})

export default connect(mapStateToProps)(LoginTv)
