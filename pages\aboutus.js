import React from 'react'
import { connect } from 'react-redux'
import $http from '@/kit/fetch'
import Meta from '@/components/common/Meta'
import TongJiPb from '@/components/common/TongJiPb'
import AboutPage from '@/components/pages/aboutus'
import { LANG_PKG } from '@/constants/interfaces'
import { platformId } from '@/kit/common'

class About extends React.Component {
  constructor(props) {
    super(props)
    this.state = {}
  }

  componentDidMount() {}

  static async getInitialProps({ ctx }) {
    const { query, store } = ctx
    const mod = query.mod || ''

    let langPkg = {}
    try {
      const state = store.getState()
      const langCode = state.getIn(['language', 'modeLangObj', 'lang'])
      const params = {
        langCode,
        platformId: platformId()
      }
      const prodLangPkgRes = await $http(LANG_PKG, {
        params: {
          ...params,
          businessName: 'PCW_PRODUCT'
        }
      })
      if (Number(prodLangPkgRes.code) === 0) langPkg = prodLangPkgRes.data

      const langPkgRes = await $http(LANG_PKG, {
        params
      })
      if (Number(langPkgRes.code) === 0)
        langPkg.navigation_about_us = langPkgRes.data.navigation_about_us
    } catch (err) {
      console.log(err)
    }

    return { mod, langPkg }
  }

  render() {
    const { mod, langPkg } = this.props
    return (
      <>
        <Meta
          desc={langPkg.aboutus_iQIYI_desc}
          title={langPkg.aboutus_iQIYI_title}
        />
        <AboutPage
          langPkg={langPkg}
          title={langPkg.navigation_about_us}
          mod={mod}
        />
        <TongJiPb rpage="aboutus_page" blockInfo="header,footer" />
      </>
    )
  }
}

const mapStateToProps = state => ({
  modeLangObj: state.getIn(['language', 'modeLangObj']),
  globalLangPkg: state.getIn(['language', 'langPkg'])
})

export default connect(mapStateToProps)(About)
