import React from 'react'
import { connect } from 'react-redux'
import ShareUnlockContent from '@/components/pages/shareunlock'
import TongJiPb from '@/components/common/TongJiPb'
import DeviceCtx from '@/components/context'

const blockInfo =
  'header,pcw_unlock_act_share,pcw_unlock_act_rule,pcw_unlock_act_hotcontent'
class ShareUnlock extends React.Component {
  static contextType = DeviceCtx

  constructor(props) {
    super(props)
    this.state = {}
  }

  render() {
    return (
      <>
        <style jsx global>
          {`
            .header-container {
              background-image: none;
              background: rgb(51, 51, 51);
            }
          `}
        </style>
        <ShareUnlockContent />
        <TongJiPb rpage="pcw_unlock_act" blockInfo={blockInfo} />
      </>
    )
  }
}
const mapStateToProps = state => {
  return {
    dispatch: state.dispatch,
    unlockInfo: state.getIn(['shareunlock', 'unlockInfo'])
  }
}
export default connect(mapStateToProps)(ShareUnlock)
