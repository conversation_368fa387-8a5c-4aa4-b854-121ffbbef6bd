import React from 'react'
import { connect } from 'react-redux'
import ServicesRightsContent from '@/components/pages/servicesRights'
import TongJiPb from '@/components/common/TongJiPb'
import {
  getPageLangListAction,
  getVipSellInfoAction
} from '@/store/reducers/pagelanguage/pagelanguage'
// class ServicesRights extends React.Component {
//   constructor(props) {
//     super(props)
//   }
// }
function ServicesRights(props) {
  const { serverloginTag } = props
  const blockInfo = 'header,title,content,view,download,download_device,user'

  return (
    <>
      <ServicesRightsContent serverloginTag={serverloginTag} />
      <TongJiPb rpage="service_page" blockInfo={blockInfo} />
    </>
  )
}
ServicesRights.getInitialProps = async ({ ctx }) => {
  const webRegexp = new RegExp('I00019=1')
  const { req, store } = ctx
  const { dispatch } = store
  dispatch(getPageLangListAction({ businessName: 'PCW_PRODUCT' }))
  dispatch(getVipSellInfoAction({ interfaceCode: '25220b127c0b879b' }))
  const cookie = req.headers.cookie || ''
  let serverloginTag = false
  if (webRegexp.test(cookie)) {
    serverloginTag = true
  }
  return { serverloginTag }
}
const mapStateToProps = state => {
  return {
    dispatch: state.dispatch,
    unlockInfo: state.getIn(['shareunlock', 'unlockInfo'])
  }
}
export default connect(mapStateToProps)(ServicesRights)
