import React from 'react'
import { connect } from 'react-redux'
import Meta from '@/components/common/Meta'
import TongJiPb from '@/components/common/TongJiPb'
import TvTutorialWrapper from '@/style/tvInstallStyle'
import $http from '@/kit/fetch'
import { iqSwitchPlatformId, rebuildCommonUrl } from '@/kit/common'
import { getCookies } from '@/kit/cookie'
import { TV_INSTALL_GUIDE } from '@/constants/interfaces'
import jsBridge from '@/utils/jsBridge'
import { isApp } from '@/utils/appDevice'
import { sendBlockPb } from '@/utils/pingBack'

const rpage = 'TV_tutorial'
const InstallWayTwo = [
  {
    img: 'http://www.iqiyipic.com/lequ/20230608/img_apple.png',
    rseat: 'appletv',
    block: 'TV_tutorial_way2'
  },
  {
    img: 'http://www.iqiyipic.com/lequ/20230607/<EMAIL>',
    rseat: 'googleplay',
    block: 'TV_tutorial_way2'
  },
  {
    img: 'http://www.iqiyipic.com/lequ/20230607/<EMAIL>',
    rseat: 'firetv',
    block: 'TV_tutorial_way2'
  },
  {
    img: 'http://www.iqiyipic.com/lequ/20230607/<EMAIL>',
    rseat: 'rokutv',
    block: 'TV_tutorial_way2'
  }
]

class TvTutorial extends React.Component {
  constructor(props) {
    super(props)
    this.state = {}
  }

  componentDidMount() {
    sendBlockPb('TV_tutorial_way1', {
      rpage
    })

    sendBlockPb('TV_tutorial_way2', {
      rpage
    })

    if (isApp) {
      jsBridge(window)
    }
  }

  handClick(type) {
    if (window.jsWebViewBridge) {
      window.jsWebViewBridge.callHandler(
        'onLaunchPlatformStore',
        { type },
        data => {
          console.log(data)
        }
      )
    }
  }

  static async getInitialProps({ ctx }) {
    const { query } = ctx
    const hasBanner = query.hasBanner || ''

    let tvList = []
    try {
      const res = await $http(TV_INSTALL_GUIDE, {
        params: {
          app_k: 'appk_pcw',
          app_v: '3.5.1',
          platform_id: iqSwitchPlatformId(),
          lang: global.langCode,
          app_lm: global.modeCode
        }
      })

      if (res.code === 0) {
        tvList = res.data
      }
    } catch (err) {
      console.log(err)
    }

    return { hasBanner, tvList }
  }

  render() {
    const { langPkg, hasBanner, tvList } = this.props
    const trueLangPkg = langPkg.toJS()

    return (
      <>
        <Meta
          desc={trueLangPkg.download_page_html_description}
          title={trueLangPkg.direct_headers}
          isShowAnimateCSS
        />
        <TvTutorialWrapper>
          <div
            className="banner"
            style={{ display: `${hasBanner ? 'block' : 'none'}` }}
          >
            <img src="http://www.iqiyipic.com/lequ/20230606/<EMAIL>" />
          </div>
          <div className="install-way-one">
            <div className="title">{trueLangPkg.direct_way1_header}</div>
            <div className="sub-title">{trueLangPkg.direct_way1_1}</div>
            <div
              className="search-icon"
              style={{ display: `${hasBanner ? 'none' : 'block'}` }}
            >
              <img src="http://www.iqiyipic.com/lequ/20230606/<EMAIL>" />
            </div>

            <div className="description">{trueLangPkg.direct_way1_2}</div>
            <ul className="install-list">
              {tvList.length &&
                tvList.map(item => {
                  return (
                    <li key={item?.oppkit_data?.name}>
                      <a
                        href={rebuildCommonUrl(
                          '/tv_tutorial_details?in_app=1&tvId=' +
                            item.id +
                            '&_FORCE_INTL_SITE=INTL'
                        )}
                      >
                        <img
                          src={item?.oppkit_data?.legend}
                          rseat={item?.oppkit_data?.name}
                          data-pb="rpage=TV_tutorial&block=TV_tutorial_way1"
                        />
                      </a>
                    </li>
                  )
                })}
            </ul>
          </div>
          <div className="install-way-two">
            <div className="title">{trueLangPkg.direct_way2_header}</div>
            <div className="description">{trueLangPkg.direct_way2_1}</div>
            <ul className="install-list">
              {InstallWayTwo.map((item, index) => {
                const dataPb = 'rpage=TV_tutorial&block=TV_tutorial_way2'
                return (
                  <li
                    key={item.rseat}
                    onClick={() => this.handClick(index + 1)}
                  >
                    <img src={item.img} rseat={item.rseat} data-pb={dataPb} />
                  </li>
                )
              })}
            </ul>
          </div>
        </TvTutorialWrapper>
        <TongJiPb rpage="TV_tutorial" blockInfo="header,footer" />
      </>
    )
  }
}

const mapStateToProps = state => ({
  langPkg: state.getIn(['language', 'langPkg'])
})

export default connect(mapStateToProps)(TvTutorial)
