import React from 'react'
import { connect } from 'react-redux'
import ChannelWrapper from '@/style/channelStyle'
import FocusImg from '@/components/common/FocusImg'
import { getDevice, getMobileType } from '@/kit/device'
import Meta from '@/components/common/Meta'
import TongJiPb from '@/components/common/TongJiPb'
import { renderRowContainer } from '@/utils/renderRowContainer'
import { cdnFrom } from '@/utils/common'
import { getCard } from '@/kit/commonConfig'
import {
  fetchConfigAction,
  setConfigAjaxAction
} from '@/store/reducers/commonConfig/commonConfig'
import { Loading } from '@/constants/style'
import { LoadingStyle } from '@/components/pages/home/<USER>/loadingStyle'
import throttle from 'lodash.throttle'
import { getWebPreData } from '@/utils/commonUtil'
import { handleCommonConfigAjax } from '@/store/sagas/commonConfig/handleData'
import HomeSchema from '@/components/pages/home/<USER>'
import OrganizationSchema from '@/components/pages/home/<USER>'
import BreadcrumbListSchema from '@/components/pages/home/<USER>'

class Local extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      isMobile: getDevice() === 'mobile',
      deviceType: getMobileType()
    }
    this.focusResId = ''
    this.containerRef = React.createRef()
  }

  componentDidMount() {
    const isMobile = getDevice() === 'mobile'

    this.getPreData(isMobile)
    this.getCommonConfigData()

    this.setState({
      isMobile
    })

    window.addEventListener('resize', () => {
      const { isMobile, deviceType } = this.state
      const newIsMobile = getDevice() === 'mobile'
      const newDeviceType = getMobileType()
      if (isMobile !== newIsMobile) {
        this.setState({
          isMobile: newIsMobile
        })
      }

      if (deviceType !== newDeviceType) {
        this.setState({
          deviceType: newDeviceType
        })
      }
    })

    if (isMobile) {
      this.setScrollListener()
    }
  }

  // 提前获得数据如热门推荐
  getPreData(isMobile) {
    const params = {
      rpage: 'local',
      isMobile
    }

    const webPreData = getWebPreData('local')
    const hotCard = webPreData?.hotCard
    if (hotCard) {
      const hotCardData = handleCommonConfigAjax(hotCard, 'local_web', params)
      if (hotCardData.data && hotCardData.data.isAjaxEnd) {
        hotCardData.data.isAjaxEnd = false
      }
      this.props.dispatch(setConfigAjaxAction(hotCardData))
    }
  }

  setScrollListener() {
    // mobile 下在这里请求pcw-common接口
    window.addEventListener(
      'scroll',
      throttle(
        () => {
          const { hasMore } = this.props
          if (this.containerRef && this.containerRef.current) {
            const rect = this.containerRef.current.getBoundingClientRect()
            if (rect.bottom - 50 < window.innerHeight && hasMore) {
              this.getCommonConfigData()
            }
          }
        },
        1500,
        { leading: true }
      )
    )
  }

  async getCommonConfigData() {
    const { isMobile } = this.state
    const { requestDate, dispatch } = this.props
    if (this.isFetching) {
      return
    }
    this.isFetching = true
    const {
      data: { nextUrlParams, hasMore }
    } = await dispatch(
      fetchConfigAction({
        pageSt: 'local_web',
        _catch: {},
        requestDate,
        pgSize: 4
      })
    )
    setTimeout(() => {
      this.isFetching = false

      if (nextUrlParams && nextUrlParams.pg_num && nextUrlParams.pg_num <= 3) {
        if (!isMobile && hasMore) {
          this.getCommonConfigData()
        }
      } else {
        this.setScrollListener()
      }
    }, 500)
  }

  static async getInitialProps({ ctx }) {
    const { isServer } = ctx
    const state = ctx.store.getState()
    const trueCurWebData = state.getIn(['commonConfig', 'local_web']).toJS()
    const { cards = [] } = trueCurWebData
    const pcwFocusBanner = getCard('pcw_focus_banner', cards)[0]
    const requestDate = new Date().getTime() // sid所需要的时间戳

    if (!pcwFocusBanner) {
      const basePath = ctx.pathname.replace('/', '')
      let uri = `https://www.iq.com?source=${basePath}`
      const _fromVal = cdnFrom(ctx.query._from, ctx.query.from)
      uri += _fromVal
      ctx.res.writeHead(302, { Location: uri })
      ctx.res.end()
    }
    return { isServer, requestDate }
  }

  render() {
    const { deviceType } = this.state
    const {
      langPkg,
      curWebData,
      mod,
      isSeo,
      pcwCommonIsAjaxEnd,
      hasMore,
      langCode
    } = this.props
    const endFocusNum = isSeo ? 100 : global.window ? 100 : 1

    const blockInfo = 'header,focus_banner,footer'
    const trueLangPkg = langPkg.toJS()
    const trueCurWebData = curWebData && curWebData.toJS()
    const { cards = [], resources, sourceMap } = trueCurWebData

    const pcwFocusBanner = getCard('pcw_focus_banner', cards)[0] || []
    let focusImgInfo = pcwFocusBanner['blocks'] || []
    focusImgInfo = focusImgInfo.slice(0, endFocusNum)

    const rowContainerStyle = {
      position: 'relative',
      fontSize: '0',
      overflow: 'visible',
      whiteSpace: 'nowrap',
      minHeight: '60vh'
    }

    const titleKey = `local_my_title`
    const descKey = `local_my_desc`
    const title = trueLangPkg[titleKey]
    const desc = trueLangPkg[descKey] || ''

    return (
      <>
        <Meta desc={desc} title={title} path="local" />
        <OrganizationSchema langCode={langCode} />
        <HomeSchema langCode={langCode} />
        <BreadcrumbListSchema
          path="/local"
          langPkg={trueLangPkg}
          langCode={langCode}
        />
        <div style={{ minHeight: '100vh' }}>
          <FocusImg
            focusImgInfo={focusImgInfo}
            deviceType={deviceType}
            rpage="local_my"
            gtagCategory="thaiseries_TopBanner"
            isAjaxEnd={pcwCommonIsAjaxEnd}
          />
          <ChannelWrapper ref={this.containerRef} style={{ minHeight: '60vh' }}>
            <div className="row-container" style={rowContainerStyle}>
              {renderRowContainer({
                rpage: `local_my`,
                resources,
                intlDataColNew: cards,
                sourceMap,
                isSeo
              })}
              {hasMore && (
                <LoadingStyle>
                  <Loading width="24px" />
                </LoadingStyle>
              )}
            </div>
          </ChannelWrapper>
          <TongJiPb rpage={`local_${mod}`} blockInfo={blockInfo} />
        </div>
      </>
    )
  }
}

const mapStateToProps = state => ({
  mod: state.getIn(['language', 'modeLangObj', 'mod']),
  langPkg: state.getIn(['language', 'langPkg']),
  pcwCommonIsAjaxEnd: state.getIn(['commonConfig', 'local_web', 'isAjaxEnd']),
  hasMore: state.getIn(['commonConfig', 'local_web', 'hasMore']),
  curWebData: state.getIn(['commonConfig', 'local_web']),
  dispatch: state.dispatch
})

export default connect(mapStateToProps)(Local)
