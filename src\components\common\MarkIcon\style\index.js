import styled from 'styled-components'
import { SCREEN_WIDTH } from '@/constants/style'

const MarkIconWrapper = styled.div`
  display: block;

  .mark-icon-right-top {
    position: absolute;
    right: 0px;
    top: 0px;
    z-index: 4;
    min-width: 24px;
    border-radius: 2px 4px 2px 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    &.right-arae {
      border-radius: 4px 2px 4px 2px;
    }

    &.text {
      height: 20px;
      box-sizing: border-box;
      padding: 0 6px;
      line-height: 20px;
      font-size: 12px;
      color: rgb(255, 255, 255);
      letter-spacing: 0px;
      text-align: center;
      /* background: #1cc749; */
      font-weight: 510;
    }

    &.icon {
      height: 20px;
      padding: 0px 6px;
      /* background-color: #f2bf83; */

      img {
        width: 100%;
        height: 100%;
      }
    }
    &.icon-207 {
      width: 34px;
      height: 34px;
    }
    &.icon-203 {
      width: 18px;
      height: 18px;
      padding: 1px 6px;
    }
  }
  .mark-top-text {
    padding-left: 3px;
    position: relative;
    font-weight: 860;
  }
  .mark-complex-wrap {
    visibility: hidden;
    position: absolute;
    right: 0px;
    top: 0px;
    z-index: 4;
    display: flex;

    .mark-complext-left {
      position: relative;
      padding: 0 0 0 6px;
      min-width: auto;
      border-radius: 2px 0px 0px 4px;

      &.complex-arae {
        border-radius: 4px 0px 0px 2px;
      }
    }
    .mark-complext-right {
      position: relative;
      padding: 0 6px 0 3px;
      min-width: auto;
      border-radius: 0px 4px 2px 0px;

      &.complex-arae {
        border-radius: 0px 2px 4px 0px;
      }
    }
    .complext-mid {
      position: relative;
      width: 9px;
      transform: scaleX(1.05);
      z-index: -1;
      .complex-mid-left {
        position: absolute;
        left: 0px;
        width: 9px;
        height: 20px;
      }
      .complex-mid-right {
        position: absolute;
        left: 3px;
        width: 0;
        height: 0;
        border-top: 20px solid transparent;
        border-right-width: 6px;
        border-right-style: solid;
        isplay: inline-block;
        overflow: hidden;
      }
    }
  }
  @media screen and (max-width: ${SCREEN_WIDTH[767]}) {
    .mark-icon-right-top {
      border-radius: 1px 2px 1px 2px;

      &.right-arae {
        border-radius: 2px 1px 2px 1px;
      }
      &.text {
        height: 14px;
        line-height: 14px;
        font-size: 10px;
        padding: 0 4px;
      }

      &.icon {
        height: 14px;
        padding: 0px 4px;
      }
      &.icon-207 {
        width: 24px;
        height: 24px;
      }

      &.icon-203 {
        width: 14px;
        height: 14px;
      }
    }
    .mark-complex-wrap {
      .mark-complext-left {
        padding: 0 0 0 4px;
        border-radius: 1px 0px 0px 2px;

        &.complex-arae {
          border-radius: 2px 0px 0px 1px;
        }
      }
      .mark-complext-right {
        padding: 0 4px 0 2px;
        border-radius: 0px 2px 1px 0px;

        &.complex-arae {
          border-radius: 0px 1px 2px 0px;
        }
      }
      .complext-mid {
        width: 6px;
        .complex-mid-left {
          left: 0px;
          width: 6px;
          height: 14px;
        }
        .complex-mid-right {
          left: 2px;
          width: 0;
          height: 0;
          border-top: 14px solid transparent;
          border-right-width: 4px;
          border-right-style: solid;
        }
      }
    }
  }
`

export default MarkIconWrapper
