import React from 'react'
import { connect } from 'react-redux'
import jsBridge from '@/utils/jsBridge'
import { isApp } from '@/utils/appDevice'
import { MODE_PTID_WEB } from '@/constants/interfaces'
import $http from '@/kit/fetch'
import { getCookies } from '@/kit/cookie'
import { getDevice, getMobileType } from '@/kit/device'
import Meta from '@/components/common/Meta'
import CodeKey from '@/components/pages/vip/CodeKey'
import { setCodeSwitchAction } from '@/store/reducers/vip/vip'
import TongJiPb from '@/components/common/TongJiPb'

class Codekey extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      isMobile: getDevice() === 'mobile',
      deviceType: getMobileType(),
      mod: props.mod,
      bossCode: props.bossCode
    }
  }

  componentDidMount = async () => {
    const isMobile = getDevice() === 'mobile'
    this.setState({
      isMobile
    })
    // window.isRefresh = () => {
    //   window.location.reload()
    // }

    // 注册jsBridge
    if (isApp) {
      jsBridge(window)
      const commonData = await this.getCommonData()
      const mod = commonData.app_lm
      const ptidData = await $http(MODE_PTID_WEB, {
        params: {
          langCode: commonData.lang,
          deviceId: getCookies('QC005'),
          modeCode: mod,
          platformId: 4
        }
      })

      this.setState({
        mod,
        bossCode: ptidData.data.bossCode
      })
    }

    window.addEventListener('resize', () => {
      const { isMobile, deviceType } = this.state
      const newIsMobile = getDevice() === 'mobile'
      const newDeviceType = getMobileType()
      if (isMobile !== newIsMobile) {
        this.setState({
          isMobile: newIsMobile
        })
      }
      if (deviceType !== newDeviceType) {
        this.setState({
          deviceType: newDeviceType
        })
      }
    })
  }

  getCommonData = async () => {
    return new Promise(resolve => {
      if (window.jsWebViewBridge) {
        window.jsWebViewBridge.callHandler('getCommonData', {}, data => {
          const resData = /android/gi.test(navigator.userAgent)
            ? JSON.parse(data)
            : data
          resolve(resData)
        })
      }
    })
  }

  static async getInitialProps({ ctx }) {
    const { isServer, query, store, req } = ctx
    // if (
    //   /qiyivideo/gi.test(global.deviceAgent) &&
    //   getMobileType() === 'android'
    // ) {
    //   const { res } = ctx
    //   res.writeHead(301, {
    //     Location: `https://www.iq.com`
    //   })
    //   res.end()
    // }
    const isInApp = query.in_app === 1

    // const mod = query.mod || ''
    const { mod = '', code } = query
    const cookie = req.headers.cookie || ''
    const webRegexp = new RegExp('I00019=1')
    const appRegexp = new RegExp('P00001=1')
    if (webRegexp.test(cookie) || (appRegexp.test(cookie) && isInApp)) {
      store.dispatch(setCodeSwitchAction(2))
    }
    return { isServer, mod, urlCode: code }
  }

  render() {
    const { langPkg, userInfo, isInApp, urlCode, modeLangObj } = this.props
    const lang = modeLangObj.get('lang')
    const { mod, bossCode } = this.state
    const { loginTag } = userInfo.toJS()
    const trueLangPkg = langPkg.toJS()
    const isMobile = this.state.isMobile
    // const blockInfo = 'header, footer'
    return (
      <>
        <Meta
          desc={trueLangPkg.drama_html_description}
          title={trueLangPkg.codekey_html_title}
        />
        <script
          type="text/javascript"
          async
          src="//security.iq.com/static/iq/v2/verifycenter/js/verifycenter.js"
        />
        <link
          rel="stylesheet"
          type="text/css"
          async
          href="//security.iq.com/static/iq/v2/verifycenter/css/verifycenter.css"
        />
        <CodeKey
          isInApp={isInApp}
          mod={mod}
          lang={lang}
          isLogin={loginTag}
          isMobile={isMobile}
          bossCode={bossCode}
          urlCode={urlCode}
        />
        <TongJiPb rpage="redeem_vip" />
      </>
    )
  }
}

const mapStateToProps = state => ({
  langPkg: state.getIn(['vip', 'vipLangPkg']),
  modeLangObj: state.getIn(['language', 'modeLangObj']),
  userInfo: state.getIn(['user', 'userInfo'])
})

export default connect(mapStateToProps)(Codekey)
