import React from 'react'
import { connect } from 'react-redux'
import ChannelWrapper from '@/style/channelStyle'
import FocusImg from '@/components/common/FocusImg'
import { getDevice, getMobileType } from '@/kit/device'
import Meta from '@/components/common/Meta'
import TongJiPb from '@/components/common/TongJiPb'
import { cdnFrom } from '@/utils/common'
import { renderRowContainer } from '@/utils/renderRowContainer'
import { getCard } from '@/kit/commonConfig'
import {
  fetchConfigAction,
  setConfigAjaxAction
} from '@/store/reducers/commonConfig/commonConfig'
import { Loading } from '@/constants/style'
import { LoadingStyle } from '@/components/pages/home/<USER>/loadingStyle'
import throttle from 'lodash.throttle'
import { getWebPreData } from '@/utils/commonUtil'
import { handleCommonConfigAjax } from '@/store/sagas/commonConfig/handleData'
import HomeSchema from '@/components/pages/home/<USER>'
import OrganizationSchema from '@/components/pages/home/<USER>'
import BreadcrumbListSchema from '@/components/pages/home/<USER>'

class Free extends React.Component {
  constructor(props) {
    super(props)
    this.state = {
      isMobile: getDevice() === 'mobile',
      deviceType: getMobileType()
    }
    this.focusResId = ''
  }

  componentDidMount() {
    const isMobile = getDevice() === 'mobile'

    this.getPreData(isMobile)
    this.getCommonConfigData()

    this.setState({
      isMobile
    })
    window.addEventListener('resize', () => {
      const { isMobile, deviceType } = this.state
      const newIsMobile = getDevice() === 'mobile'
      const newDeviceType = getMobileType()
      if (isMobile !== newIsMobile) {
        this.setState({
          isMobile: newIsMobile
        })
      }
      if (deviceType !== newDeviceType) {
        this.setState({
          deviceType: newDeviceType
        })
      }
    })

    if (isMobile) {
      this.setScrollListener()
    }
  }

  // 提前获得数据如热门推荐
  getPreData(isMobile) {
    const params = {
      rpage: 'free',
      isMobile
    }

    const webPreData = getWebPreData('free')
    const hotCard = webPreData?.hotCard
    if (hotCard) {
      const hotCardData = handleCommonConfigAjax(hotCard, 'free_web', params)
      if (hotCardData.data && hotCardData.data.isAjaxEnd) {
        hotCardData.data.isAjaxEnd = false
      }
      this.props.dispatch(setConfigAjaxAction(hotCardData))
    }
  }

  setScrollListener() {
    // mobile 下在这里请求pcw-common接口
    window.addEventListener(
      'scroll',
      throttle(
        () => {
          const { hasMore } = this.props
          if (this.containerRef && this.containerRef.current) {
            const rect = this.containerRef.current.getBoundingClientRect()
            if (rect.bottom - 50 < window.innerHeight && hasMore) {
              this.getCommonConfigData()
            }
          }
        },
        1500,
        { leading: true }
      )
    )
  }

  async getCommonConfigData() {
    const { isMobile } = this.state
    const { requestDate, dispatch } = this.props
    if (this.isFetching) {
      return
    }
    this.isFetching = true
    const {
      data: { nextUrlParams, hasMore }
    } = await dispatch(
      fetchConfigAction({
        pageSt: 'free_web',
        _catch: {},
        requestDate,
        pgSize: 4
      })
    )
    setTimeout(() => {
      this.isFetching = false

      if (nextUrlParams && nextUrlParams.pg_num && nextUrlParams.pg_num <= 3) {
        if (!isMobile && hasMore) {
          this.getCommonConfigData()
        }
      } else {
        this.setScrollListener()
      }
    }, 500)
  }

  static async getInitialProps({ ctx }) {
    const { isServer } = ctx
    const state = ctx.store.getState()
    const trueCurWebData = state.getIn(['commonConfig', 'free_web']).toJS()
    const { cards = [] } = trueCurWebData
    const pcwFocusBanner = getCard('pcw_focus_banner', cards)[0]
    const requestDate = new Date().getTime() // sid所需要的时间戳

    if (!pcwFocusBanner) {
      const basePath = ctx.pathname.replace('/', '')
      let uri = `https://www.iq.com?source=${basePath}`
      const _fromVal = cdnFrom(ctx.query._from, ctx.query.from)
      uri += _fromVal
      if (ctx.query._from === 'iqiyi') {
        uri += `&_from=iqiyi`
      }
      ctx.res.writeHead(302, { Location: uri })
      ctx.res.end()
    }
    return { isServer, requestDate }
  }

  render() {
    const { deviceType } = this.state
    const {
      langPkg,
      curWebData,
      isSeo,
      hasMore,
      pcwCommonIsAjaxEnd,
      langCode
    } = this.props
    const endFocusNum = isSeo ? 100 : global.window ? 100 : 1

    const blockInfo = 'header,focus_banner,footer'
    const trueLangPkg = langPkg.toJS()
    const trueCurWebData = curWebData && curWebData.toJS()
    const { cards = [], resources, sourceMap } = trueCurWebData

    const pcwFocusBanner = getCard('pcw_focus_banner', cards)[0] || []
    let focusImgInfo = pcwFocusBanner['blocks'] || []
    focusImgInfo = focusImgInfo.slice(0, endFocusNum)

    const rowContainerStyle = {
      position: 'relative',
      fontSize: '0',
      overflow: 'visible',
      whiteSpace: 'nowrap',
      minHeight: '60vh'
    }

    return (
      <>
        <Meta
          desc={trueLangPkg.free_html_description}
          title={trueLangPkg.free_html_title}
          brand={trueLangPkg.brand_html}
          commonKeywords={trueLangPkg.common_keywords}
          seriesKeywords={trueLangPkg.series_keywords}
          brandKeywords={trueLangPkg.brand_keywords}
          path="free"
        />
        <OrganizationSchema langCode={langCode} />
        <HomeSchema langCode={langCode} />
        <BreadcrumbListSchema
          path="/free"
          langPkg={trueLangPkg}
          langCode={langCode}
        />
        <div style={{ minHeight: '100vh' }}>
          <FocusImg
            focusImgInfo={focusImgInfo}
            deviceType={deviceType}
            rpage="free"
            gtagCategory="free_TopBanner"
            isAjaxEnd={pcwCommonIsAjaxEnd}
          />
          <ChannelWrapper style={{ minHeight: '60vh' }}>
            <div className="row-container" style={rowContainerStyle}>
              {renderRowContainer({
                rpage: 'free',
                resources,
                intlDataColNew: cards,
                sourceMap,
                isSeo
              })}
              {hasMore && (
                <LoadingStyle>
                  <Loading width="24px" />
                </LoadingStyle>
              )}
            </div>
          </ChannelWrapper>
          <TongJiPb rpage="free" blockInfo={blockInfo} />
        </div>
      </>
    )
  }
}

const mapStateToProps = state => ({
  langPkg: state.getIn(['language', 'langPkg']),
  curWebData: state.getIn(['commonConfig', 'free_web']),
  hasMore: state.getIn(['commonConfig', 'free_web', 'hasMore']),
  pcwCommonIsAjaxEnd: state.getIn(['commonConfig', 'free_web', 'isAjaxEnd']),
  dispatch: state.dispatch
})

export default connect(mapStateToProps)(Free)
